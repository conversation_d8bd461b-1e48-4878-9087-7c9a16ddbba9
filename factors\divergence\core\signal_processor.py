#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信号处理器

背离因子的主要类，协调整个分析流程。
提供标准API接口，支持实时和批量分析。

作者: QuantFM Team
创建时间: 2025-08-11
"""

import numpy as np
from typing import Dict, Any, Optional, List, Union
import logging
from datetime import datetime

from .extreme_detector import AdaptiveExtremeDetector, ExtremePoint
from .pattern_analyzer import PatternAnalyzer, Pattern
from .indicator_calculator import IndicatorCalculator
from ..utils.validation import validate_input_data, check_data_quality
from ..utils.adaptive_params import AdaptiveParameterManager
from ..utils.metrics import calculate_divergence_strength, assess_signal_quality
from ..config.default_params import DEFAULT_CONFIG
from ..config.market_regimes import MARKET_REGIMES, detect_market_regime

logger = logging.getLogger(__name__)


class DivergenceFactor:
    """背离因子主类
    
    提供完整的背离分析功能，包括：
    1. 多指标背离检测
    2. 实时信号处理
    3. 市场状态感知
    4. 自适应参数调整
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化背离因子
        
        Args:
            config: 配置参数字典，如果为None则使用默认配置
        """
        # 加载配置
        self.config = self._load_config(config)
        
        # 初始化组件
        self.extreme_detector = AdaptiveExtremeDetector(self.config.get('extreme_detection', {}))
        self.pattern_analyzer = PatternAnalyzer(self.config.get('divergence_analysis', {}))
        self.indicator_calculator = IndicatorCalculator(self.config.get('indicators', {}))
        self.param_manager = AdaptiveParameterManager(self.config.get('adaptive_params', {}))
        
        # 缓存
        self._last_analysis_result = None
        self._last_realtime_result = None
        
        logger.info("背离因子初始化完成")
    
    def analyze(self, prices: Union[np.ndarray, List[float]], 
                volumes: Union[np.ndarray, List[float]],
                high: Optional[Union[np.ndarray, List[float]]] = None,
                low: Optional[Union[np.ndarray, List[float]]] = None) -> Dict[str, Any]:
        """分析背离模式
        
        Args:
            prices: 价格序列（通常是收盘价）
            volumes: 成交量序列
            high: 最高价序列
            low: 最低价序列
            
        Returns:
            分析结果字典，包含所有必要字段
        """
        try:
            # 数据预处理
            prices = np.array(prices, dtype=float)
            volumes = np.array(volumes, dtype=float)
            high = np.array(high, dtype=float) if high is not None else prices.copy()
            low = np.array(low, dtype=float) if low is not None else prices.copy()
            
            # 数据验证
            validation_result = validate_input_data(prices, volumes, high, low)
            if not validation_result['is_valid']:
                logger.warning(f"数据验证失败: {validation_result['errors']}")
                return self._get_empty_result(validation_result)
            
            # 数据质量检查
            quality_score = check_data_quality(prices, volumes, high, low)
            
            # 计算技术指标
            indicators = self.indicator_calculator.calculate_all_indicators(high, low, prices, volumes)
            
            # 检测价格极值点
            price_extremes = self.extreme_detector.detect_extremes(prices, high, low, volumes)
            
            # 检测各指标的极值点
            indicator_extremes = {}
            for indicator_name, indicator_data in indicators.items():
                if isinstance(indicator_data, dict):
                    for sub_name, sub_data in indicator_data.items():
                        key = f"{indicator_name}_{sub_name}"
                        extremes = self.extreme_detector.detect_extremes(sub_data)
                        indicator_extremes[key] = extremes
                else:
                    extremes = self.extreme_detector.detect_extremes(indicator_data)
                    indicator_extremes[indicator_name] = extremes
            
            # 分析背离模式
            all_patterns = []
            for indicator_name, extremes in indicator_extremes.items():
                patterns = self.pattern_analyzer.analyze_patterns(price_extremes, extremes)
                for pattern in patterns:
                    pattern.indicator_name = indicator_name
                all_patterns.extend(patterns)
            
            # 生成综合信号
            consensus = self._generate_consensus(all_patterns, indicators)
            
            # 生成交易建议
            trade_suggestion = self._generate_trade_suggestion(consensus, prices, quality_score)
            
            # 构建结果
            result = {
                'metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'data_length': len(prices),
                    'data_quality_score': quality_score,
                    'indicators_calculated': list(indicators.keys()),
                    'extremes_detected': len(price_extremes),
                    'patterns_found': len(all_patterns)
                },
                'flags': {
                    'has_divergence': len([p for p in all_patterns if 'divergence' in p.type]) > 0,
                    'has_strong_signal': consensus.get('strength', 0) > 0.7,
                    'data_quality_ok': quality_score > 0.6,
                    'sufficient_data': len(prices) >= 30
                },
                'indicators': self._format_indicators_output(indicators),
                'extremes': {
                    'price_extremes': self._format_extremes_output(price_extremes),
                    'indicator_extremes': {k: self._format_extremes_output(v) 
                                         for k, v in indicator_extremes.items()}
                },
                'patterns': self._format_patterns_output(all_patterns),
                'summary': {
                    'total_patterns': len(all_patterns),
                    'confirmed_patterns': len([p for p in all_patterns if not p.is_forming]),
                    'forming_patterns': len([p for p in all_patterns if p.is_forming]),
                    'max_strength': max([p.strength for p in all_patterns], default=0),
                    'avg_confidence': np.mean([p.confidence for p in all_patterns]) if all_patterns else 0
                },
                'consensus': consensus,
                'trade_suggestion': trade_suggestion
            }
            
            # 缓存结果
            self._last_analysis_result = result
            
            logger.debug(f"分析完成，检测到 {len(all_patterns)} 个模式")
            return result
            
        except Exception as e:
            logger.error(f"背离分析失败: {e}")
            return self._get_error_result(str(e))
    
    def analyze_realtime(self, prices: Union[np.ndarray, List[float]], 
                        volumes: Union[np.ndarray, List[float]],
                        high: Optional[Union[np.ndarray, List[float]]] = None,
                        low: Optional[Union[np.ndarray, List[float]]] = None) -> Dict[str, Any]:
        """实时分析背离模式
        
        Args:
            prices: 价格序列
            volumes: 成交量序列
            high: 最高价序列
            low: 最低价序列
            
        Returns:
            实时分析结果，包含realtime_signals和realtime_flags
        """
        try:
            # 执行常规分析
            base_result = self.analyze(prices, volumes, high, low)
            
            # 提取实时特定信息
            realtime_signals = self._extract_realtime_signals(base_result)
            realtime_flags = self._extract_realtime_flags(base_result)
            
            # 构建实时结果
            realtime_result = base_result.copy()
            realtime_result['realtime_signals'] = realtime_signals
            realtime_result['realtime_flags'] = realtime_flags
            
            # 缓存结果
            self._last_realtime_result = realtime_result
            
            return realtime_result
            
        except Exception as e:
            logger.error(f"实时分析失败: {e}")
            return self._get_error_result(str(e))
    
    def get_signal(self) -> Optional[Dict[str, Any]]:
        """获取最新的信号
        
        Returns:
            最新分析结果的信号部分
        """
        if self._last_analysis_result:
            return {
                'consensus': self._last_analysis_result.get('consensus'),
                'trade_suggestion': self._last_analysis_result.get('trade_suggestion'),
                'summary': self._last_analysis_result.get('summary')
            }
        return None
    
    def get_strength(self) -> float:
        """获取信号强度
        
        Returns:
            信号强度值 (0-1)
        """
        if self._last_analysis_result:
            return self._last_analysis_result.get('consensus', {}).get('strength', 0.0)
        return 0.0
    
    def get_realtime_signal(self) -> Optional[Dict[str, Any]]:
        """获取实时信号
        
        Returns:
            实时信号结果
        """
        if self._last_realtime_result:
            return {
                'realtime_signals': self._last_realtime_result.get('realtime_signals'),
                'realtime_flags': self._last_realtime_result.get('realtime_flags'),
                'consensus': self._last_realtime_result.get('consensus')
            }
        return None
    
    def _load_config(self, config: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """加载配置"""
        if config is None:
            return DEFAULT_CONFIG.copy()
        
        # 合并用户配置和默认配置
        merged_config = DEFAULT_CONFIG.copy()
        self._deep_update(merged_config, config)
        
        return merged_config
    
    def _deep_update(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def _generate_consensus(self, patterns: List[Pattern], indicators: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合信号"""
        if not patterns:
            return {
                'strength': 0.0,
                'direction': 'neutral',
                'confidence': 0.0,
                'recommendation': 'hold'
            }
        
        # 检测市场状态
        market_regime = detect_market_regime(indicators)
        regime_config = MARKET_REGIMES.get(market_regime, MARKET_REGIMES['sideways'])
        
        # 按指标分组模式
        indicator_patterns = {}
        for pattern in patterns:
            indicator_name = getattr(pattern, 'indicator_name', 'unknown')
            if indicator_name not in indicator_patterns:
                indicator_patterns[indicator_name] = []
            indicator_patterns[indicator_name].append(pattern)
        
        # 计算加权强度
        weighted_strength = 0.0
        total_weight = 0.0
        
        for indicator_name, indicator_patterns_list in indicator_patterns.items():
            # 获取指标权重
            weight = regime_config['indicator_weights'].get(indicator_name, 0.2)
            
            # 计算该指标的最强模式
            max_strength = max([p.strength for p in indicator_patterns_list], default=0)
            
            weighted_strength += max_strength * weight
            total_weight += weight
        
        # 标准化强度
        final_strength = weighted_strength / total_weight if total_weight > 0 else 0
        
        # 确定方向
        bullish_patterns = [p for p in patterns if 'bullish' in p.type or p.type == 'bottom_bottom_high']
        bearish_patterns = [p for p in patterns if 'bearish' in p.type or p.type == 'top_top_low']
        
        if len(bullish_patterns) > len(bearish_patterns):
            direction = 'bullish'
        elif len(bearish_patterns) > len(bullish_patterns):
            direction = 'bearish'
        else:
            direction = 'neutral'
        
        # 计算置信度
        avg_confidence = np.mean([p.confidence for p in patterns])
        
        # 生成建议
        recommendation = self._generate_recommendation(final_strength, direction, avg_confidence)
        
        return {
            'strength': final_strength,
            'direction': direction,
            'confidence': avg_confidence,
            'recommendation': recommendation,
            'market_regime': market_regime,
            'contributing_indicators': list(indicator_patterns.keys())
        }

    def _generate_recommendation(self, strength: float, direction: str, confidence: float) -> str:
        """生成交易建议"""
        if strength < 0.3 or confidence < 0.5:
            return 'hold'
        elif strength > 0.7 and confidence > 0.7:
            return 'strong_buy' if direction == 'bullish' else 'strong_sell'
        elif strength > 0.5:
            return 'buy' if direction == 'bullish' else 'sell'
        else:
            return 'hold'

    def _generate_trade_suggestion(self, consensus: Dict[str, Any], prices: np.ndarray,
                                 quality_score: float) -> Dict[str, Any]:
        """生成交易建议"""
        if len(prices) == 0:
            return {'action': 'hold', 'confidence': 0.0}

        current_price = prices[-1]
        strength = consensus.get('strength', 0)
        direction = consensus.get('direction', 'neutral')

        # 计算止损和止盈
        atr_multiplier = self.config.get('signal_generation', {}).get('stop_loss_atr_multiplier', 2.0)
        profit_multiplier = self.config.get('signal_generation', {}).get('take_profit_atr_multiplier', 3.0)

        # 简化的ATR计算
        if len(prices) > 1:
            price_changes = np.abs(np.diff(prices[-20:]))  # 最近20个周期
            atr = np.mean(price_changes) if len(price_changes) > 0 else current_price * 0.02
        else:
            atr = current_price * 0.02

        suggestion = {
            'action': consensus.get('recommendation', 'hold'),
            'confidence': min(strength * quality_score, 1.0),
            'entry_price': current_price,
            'stop_loss': None,
            'take_profit': None,
            'risk_reward_ratio': profit_multiplier / atr_multiplier
        }

        # 设置止损止盈
        if direction == 'bullish' and strength > 0.5:
            suggestion['stop_loss'] = current_price - atr * atr_multiplier
            suggestion['take_profit'] = current_price + atr * profit_multiplier
        elif direction == 'bearish' and strength > 0.5:
            suggestion['stop_loss'] = current_price + atr * atr_multiplier
            suggestion['take_profit'] = current_price - atr * profit_multiplier

        return suggestion

    def _extract_realtime_signals(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """提取实时信号"""
        patterns = result.get('patterns', [])
        forming_patterns = [p for p in patterns if p.get('is_forming', False)]

        return {
            'forming_patterns_count': len(forming_patterns),
            'latest_pattern_strength': max([p.get('strength', 0) for p in forming_patterns], default=0),
            'completion_probabilities': [p.get('completion_probability', 0) for p in forming_patterns],
            'avg_completion_probability': np.mean([p.get('completion_probability', 0)
                                                 for p in forming_patterns]) if forming_patterns else 0
        }

    def _extract_realtime_flags(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """提取实时标志"""
        patterns = result.get('patterns', [])
        forming_patterns = [p for p in patterns if p.get('is_forming', False)]

        return {
            'has_forming_patterns': len(forming_patterns) > 0,
            'high_completion_probability': any(p.get('completion_probability', 0) > 0.8
                                             for p in forming_patterns),
            'strong_forming_signal': any(p.get('strength', 0) > 0.7 for p in forming_patterns),
            'multiple_confirmations': len(forming_patterns) >= 2
        }

    def _format_indicators_output(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """格式化指标输出"""
        formatted = {}
        for name, data in indicators.items():
            if isinstance(data, dict):
                # 多值指标（如MACD）
                formatted[name] = {}
                for sub_name, sub_data in data.items():
                    if isinstance(sub_data, np.ndarray):
                        formatted[name][sub_name] = {
                            'current': float(sub_data[-1]) if len(sub_data) > 0 else 0.0,
                            'previous': float(sub_data[-2]) if len(sub_data) > 1 else 0.0,
                            'trend': 'up' if len(sub_data) > 1 and sub_data[-1] > sub_data[-2] else 'down'
                        }
            elif isinstance(data, np.ndarray):
                # 单值指标（如RSI）
                formatted[name] = {
                    'current': float(data[-1]) if len(data) > 0 else 0.0,
                    'previous': float(data[-2]) if len(data) > 1 else 0.0,
                    'trend': 'up' if len(data) > 1 and data[-1] > data[-2] else 'down'
                }

        return formatted

    def _format_extremes_output(self, extremes: List[ExtremePoint]) -> List[Dict[str, Any]]:
        """格式化极值点输出"""
        return [
            {
                'index': extreme.index,
                'price': extreme.price,
                'strength': extreme.strength,
                'type': extreme.type,
                'confirmed': extreme.confirmed,
                'completion_prob': extreme.completion_prob
            }
            for extreme in extremes
        ]

    def _format_patterns_output(self, patterns: List[Pattern]) -> List[Dict[str, Any]]:
        """格式化模式输出"""
        return [
            {
                'type': pattern.type,
                'strength': pattern.strength,
                'confidence': pattern.confidence,
                'is_forming': pattern.is_forming,
                'completion_probability': pattern.completion_probability,
                'price_points_count': len(pattern.price_points),
                'indicator_points_count': len(pattern.indicator_points),
                'indicator_name': getattr(pattern, 'indicator_name', 'unknown')
            }
            for pattern in patterns
        ]

    def _get_empty_result(self, validation_result: Dict[str, Any]) -> Dict[str, Any]:
        """获取空结果"""
        return {
            'metadata': {
                'timestamp': datetime.now().isoformat(),
                'data_length': 0,
                'data_quality_score': 0.0,
                'validation_errors': validation_result.get('errors', [])
            },
            'flags': {
                'has_divergence': False,
                'has_strong_signal': False,
                'data_quality_ok': False,
                'sufficient_data': False
            },
            'indicators': {},
            'extremes': {'price_extremes': [], 'indicator_extremes': {}},
            'patterns': [],
            'summary': {
                'total_patterns': 0,
                'confirmed_patterns': 0,
                'forming_patterns': 0,
                'max_strength': 0,
                'avg_confidence': 0
            },
            'consensus': {
                'strength': 0.0,
                'direction': 'neutral',
                'confidence': 0.0,
                'recommendation': 'hold'
            },
            'trade_suggestion': {
                'action': 'hold',
                'confidence': 0.0
            }
        }

    def _get_error_result(self, error_message: str) -> Dict[str, Any]:
        """获取错误结果"""
        result = self._get_empty_result({'errors': [error_message]})
        result['metadata']['error'] = error_message
        return result
