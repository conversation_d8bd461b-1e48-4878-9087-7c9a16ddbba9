#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模型定义

包含系统中使用的各种数据类定义，避免循环导入问题。

作者: QuantFM Team
创建时间: 2025-01-06
"""

from dataclasses import dataclass
from datetime import date
from typing import Optional


@dataclass
class Signal:
    """策略信号数据类"""
    stock_code: str
    stock_name: str
    strategy_name: str
    signal_date: date
    signal_strength: float = 0.0
    # 具体的信号数据字段
    latest_volume: int = 0
    avg_volume: int = 0
    volume_ratio: float = 0.0
    latest_close: float = 0.0
    max_high_20d: float = 0.0
    breakout_ratio: float = 0.0
    signal_note: str = ""