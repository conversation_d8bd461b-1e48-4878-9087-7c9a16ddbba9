#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBV背离检测器

专门处理OBV指标的背离分析，重点关注量价关系和资金流向。

作者: QuantFM Team
创建时间: 2025-08-11
"""

import numpy as np
from dataclasses import dataclass
from typing import Dict, Any, List, Optional
import logging
from scipy import stats

logger = logging.getLogger(__name__)


@dataclass
class IndicatorResult:
    """指标分析结果"""
    patterns: List[Dict[str, Any]]      # 检测到的模式
    summary: Dict[str, Any]             # 汇总信息
    signals: List[Dict[str, Any]]       # 信号列表
    metadata: Dict[str, Any]            # 元数据


class OBVDivergenceDetector:
    """OBV背离检测器
    
    核心功能:
    1. 量价关系分析（OBV核心功能）
    2. OBV趋势线突破检测（考虑累积性质）
    3. 平滑处理降噪
    4. 累积/分散模式识别（机构资金流向）
    5. 趋势线分析
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化检测器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # OBV特定参数
        self.smoothing_window = self.config.get('smoothing_window', 5)
        self.trend_line_periods = self.config.get('trend_line_periods', 20)
        self.accumulation_threshold = self.config.get('accumulation_threshold', 0.6)
        self.distribution_threshold = self.config.get('distribution_threshold', -0.6)
        self.trend_break_confirmation = self.config.get('trend_break_confirmation', 3)
        self.volume_price_correlation_window = self.config.get('volume_price_correlation_window', 15)
        
    def detect(self, obv_data: Dict[str, np.ndarray], price_extremes: List[Any],
              volume: Optional[np.ndarray] = None, prices: Optional[np.ndarray] = None) -> IndicatorResult:
        """检测OBV背离模式
        
        Args:
            obv_data: OBV数据，包含obv值
            price_extremes: 价格极值点
            volume: 成交量数据（用于验证）
            prices: 价格数据（用于量价分析）
            
        Returns:
            指标分析结果
        """
        try:
            obv = obv_data.get('obv', np.array([]))
            
            if len(obv) == 0:
                logger.warning("OBV数据为空")
                return self._get_empty_result()
            
            patterns = []
            signals = []
            
            # 1. 平滑OBV数据
            smoothed_obv = self._smooth_obv(obv)
            
            # 2. 分析OBV背离
            obv_patterns = self._analyze_obv_divergence(smoothed_obv, price_extremes)
            patterns.extend(obv_patterns)
            
            # 3. 检测趋势线突破
            trend_break_signals = self._detect_trend_line_breaks(smoothed_obv)
            signals.extend(trend_break_signals)
            
            # 4. 累积/分散模式分析
            accumulation_distribution = self._analyze_accumulation_distribution(smoothed_obv)
            
            # 5. 量价关系分析
            volume_price_analysis = {}
            if prices is not None and volume is not None:
                volume_price_analysis = self._analyze_volume_price_relationship(obv, prices, volume)
            
            # 6. 机构资金流向分析
            institutional_flow = self._analyze_institutional_flow(smoothed_obv, volume, prices)
            
            # 生成汇总
            summary = self._generate_summary(patterns, signals, accumulation_distribution, 
                                           volume_price_analysis, institutional_flow)
            
            # 构建元数据
            metadata = {
                'data_length': len(obv),
                'smoothing_applied': True,
                'accumulation_distribution': accumulation_distribution,
                'volume_price_analysis': volume_price_analysis,
                'institutional_flow': institutional_flow,
                'analysis_timestamp': np.datetime64('now').astype(str)
            }
            
            return IndicatorResult(
                patterns=patterns,
                summary=summary,
                signals=signals,
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"OBV背离检测失败: {e}")
            return self._get_empty_result()
    
    def _smooth_obv(self, obv: np.ndarray) -> np.ndarray:
        """平滑OBV数据以降噪"""
        try:
            if len(obv) < self.smoothing_window:
                return obv
            
            # 使用移动平均平滑
            smoothed = np.zeros_like(obv)
            for i in range(len(obv)):
                start_idx = max(0, i - self.smoothing_window + 1)
                smoothed[i] = np.mean(obv[start_idx:i + 1])
            
            return smoothed
            
        except Exception as e:
            logger.warning(f"OBV平滑处理失败: {e}")
            return obv
    
    def _analyze_obv_divergence(self, obv: np.ndarray, price_extremes: List[Any]) -> List[Dict[str, Any]]:
        """分析OBV背离"""
        patterns = []
        
        try:
            # 寻找OBV的极值点
            obv_peaks = self._find_obv_extremes(obv, 'peak')
            obv_troughs = self._find_obv_extremes(obv, 'trough')
            
            # 与价格极值点匹配
            for price_extreme in price_extremes:
                if price_extreme.type == 'peak':
                    matching_obv = self._find_matching_obv_extreme(price_extreme, obv_peaks, obv)
                    if matching_obv:
                        pattern = self._create_obv_divergence_pattern(
                            'obv_divergence', price_extreme, matching_obv, obv
                        )
                        if pattern:
                            patterns.append(pattern)
                
                elif price_extreme.type == 'trough':
                    matching_obv = self._find_matching_obv_extreme(price_extreme, obv_troughs, obv)
                    if matching_obv:
                        pattern = self._create_obv_divergence_pattern(
                            'obv_divergence', price_extreme, matching_obv, obv
                        )
                        if pattern:
                            patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            logger.warning(f"OBV背离分析失败: {e}")
            return []
    
    def _detect_trend_line_breaks(self, obv: np.ndarray) -> List[Dict[str, Any]]:
        """检测OBV趋势线突破"""
        signals = []
        
        try:
            if len(obv) < self.trend_line_periods:
                return signals
            
            # 计算趋势线
            for i in range(self.trend_line_periods, len(obv)):
                # 使用最近一段时间的数据拟合趋势线
                recent_obv = obv[i - self.trend_line_periods:i]
                x = np.arange(len(recent_obv))
                
                # 线性回归拟合趋势线
                try:
                    slope, intercept, r_value, p_value, std_err = stats.linregress(x, recent_obv)
                    
                    # 预测当前点的趋势线值
                    trend_value = slope * (len(recent_obv)) + intercept
                    actual_value = obv[i]
                    
                    # 检测突破
                    deviation = (actual_value - trend_value) / (np.std(recent_obv) + 1e-8)
                    
                    if abs(deviation) > 2.0:  # 2个标准差的突破
                        # 确认突破
                        confirmation = self._confirm_trend_break(obv, i, deviation > 0)
                        if confirmation['confirmed']:
                            signal = {
                                'type': 'obv_trend_break',
                                'index': i,
                                'strength': confirmation['strength'],
                                'direction': 'upward' if deviation > 0 else 'downward',
                                'deviation': deviation,
                                'trend_slope': slope,
                                'r_squared': r_value ** 2,
                                'significance': 'high' if abs(deviation) > 3.0 else 'medium'
                            }
                            signals.append(signal)
                            
                except Exception:
                    continue  # 跳过拟合失败的点
            
            return signals
            
        except Exception as e:
            logger.warning(f"OBV趋势线突破检测失败: {e}")
            return []
    
    def _analyze_accumulation_distribution(self, obv: np.ndarray) -> Dict[str, Any]:
        """分析累积/分散模式"""
        try:
            if len(obv) < 10:
                return {}
            
            # 计算OBV变化率
            obv_changes = np.diff(obv)
            
            # 累积期：连续上升
            accumulation_periods = []
            distribution_periods = []
            
            current_accumulation = None
            current_distribution = None
            
            for i, change in enumerate(obv_changes):
                if change > 0:  # 上升
                    if current_accumulation is None:
                        current_accumulation = {'start': i, 'strength': 0}
                    current_accumulation['strength'] += change
                    
                    # 结束分散期
                    if current_distribution is not None:
                        current_distribution['end'] = i
                        current_distribution['duration'] = i - current_distribution['start']
                        if current_distribution['duration'] >= 3:  # 至少3个周期
                            distribution_periods.append(current_distribution)
                        current_distribution = None
                        
                elif change < 0:  # 下降
                    if current_distribution is None:
                        current_distribution = {'start': i, 'strength': 0}
                    current_distribution['strength'] += abs(change)
                    
                    # 结束累积期
                    if current_accumulation is not None:
                        current_accumulation['end'] = i
                        current_accumulation['duration'] = i - current_accumulation['start']
                        if current_accumulation['duration'] >= 3:
                            accumulation_periods.append(current_accumulation)
                        current_accumulation = None
            
            # 处理最后的期间
            if current_accumulation is not None:
                current_accumulation['end'] = len(obv_changes)
                current_accumulation['duration'] = len(obv_changes) - current_accumulation['start']
                if current_accumulation['duration'] >= 3:
                    accumulation_periods.append(current_accumulation)
            
            if current_distribution is not None:
                current_distribution['end'] = len(obv_changes)
                current_distribution['duration'] = len(obv_changes) - current_distribution['start']
                if current_distribution['duration'] >= 3:
                    distribution_periods.append(current_distribution)
            
            # 当前状态
            recent_changes = obv_changes[-5:] if len(obv_changes) >= 5 else obv_changes
            avg_recent_change = np.mean(recent_changes)
            
            if avg_recent_change > self.accumulation_threshold:
                current_phase = 'accumulation'
            elif avg_recent_change < self.distribution_threshold:
                current_phase = 'distribution'
            else:
                current_phase = 'neutral'
            
            return {
                'current_phase': current_phase,
                'accumulation_periods': len(accumulation_periods),
                'distribution_periods': len(distribution_periods),
                'avg_recent_change': avg_recent_change,
                'total_accumulation_strength': sum([p['strength'] for p in accumulation_periods]),
                'total_distribution_strength': sum([p['strength'] for p in distribution_periods])
            }
            
        except Exception as e:
            logger.warning(f"累积分散分析失败: {e}")
            return {}
    
    def _analyze_volume_price_relationship(self, obv: np.ndarray, prices: np.ndarray, 
                                         volume: np.ndarray) -> Dict[str, Any]:
        """分析量价关系"""
        try:
            if len(obv) < self.volume_price_correlation_window:
                return {}
            
            # 计算最近一段时间的量价相关性
            recent_length = min(self.volume_price_correlation_window, len(obv))
            recent_obv = obv[-recent_length:]
            recent_prices = prices[-recent_length:]
            recent_volume = volume[-recent_length:]
            
            # OBV与价格的相关性
            obv_price_corr = np.corrcoef(recent_obv, recent_prices)[0, 1] if len(recent_obv) > 1 else 0
            
            # 成交量与价格变化的相关性
            price_changes = np.diff(recent_prices)
            volume_changes = recent_volume[1:]  # 对应价格变化的成交量
            
            if len(price_changes) > 1:
                volume_price_change_corr = np.corrcoef(np.abs(price_changes), volume_changes)[0, 1]
            else:
                volume_price_change_corr = 0
            
            # 量价背离检测
            obv_trend = 'up' if recent_obv[-1] > recent_obv[0] else 'down'
            price_trend = 'up' if recent_prices[-1] > recent_prices[0] else 'down'
            
            volume_price_divergence = obv_trend != price_trend
            
            return {
                'obv_price_correlation': obv_price_corr,
                'volume_price_change_correlation': volume_price_change_corr,
                'obv_trend': obv_trend,
                'price_trend': price_trend,
                'volume_price_divergence': volume_price_divergence,
                'correlation_strength': abs(obv_price_corr)
            }
            
        except Exception as e:
            logger.warning(f"量价关系分析失败: {e}")
            return {}
    
    def _analyze_institutional_flow(self, obv: np.ndarray, volume: Optional[np.ndarray], 
                                  prices: Optional[np.ndarray]) -> Dict[str, Any]:
        """分析机构资金流向"""
        try:
            if len(obv) < 10:
                return {}
            
            # 基于OBV变化分析资金流向
            obv_changes = np.diff(obv)
            
            # 大额资金流入/流出检测（基于OBV大幅变化）
            obv_std = np.std(obv_changes)
            large_inflows = np.sum(obv_changes > 2 * obv_std)
            large_outflows = np.sum(obv_changes < -2 * obv_std)
            
            # 持续性分析
            recent_obv = obv[-10:] if len(obv) >= 10 else obv
            trend_consistency = self._calculate_obv_trend_consistency(recent_obv)
            
            # 资金流向强度
            net_flow = np.sum(obv_changes[-10:]) if len(obv_changes) >= 10 else np.sum(obv_changes)
            flow_strength = abs(net_flow) / (np.std(obv_changes) * len(obv_changes[-10:]) + 1e-8)
            
            # 机构行为特征
            if trend_consistency > 0.7 and flow_strength > 1.5:
                institutional_activity = 'high'
            elif trend_consistency > 0.5 and flow_strength > 1.0:
                institutional_activity = 'medium'
            else:
                institutional_activity = 'low'
            
            return {
                'large_inflows': large_inflows,
                'large_outflows': large_outflows,
                'net_flow': net_flow,
                'flow_strength': flow_strength,
                'trend_consistency': trend_consistency,
                'institutional_activity': institutional_activity,
                'flow_direction': 'inflow' if net_flow > 0 else 'outflow'
            }
            
        except Exception as e:
            logger.warning(f"机构资金流向分析失败: {e}")
            return {}
    
    def _find_obv_extremes(self, obv: np.ndarray, extreme_type: str) -> List[Dict[str, Any]]:
        """寻找OBV极值点"""
        extremes = []
        window = 4  # OBV使用稍大的窗口，因为它是累积指标
        
        try:
            for i in range(window, len(obv) - window):
                is_extreme = True
                current_value = obv[i]
                
                # 检查是否为极值
                for j in range(i - window, i + window + 1):
                    if j == i:
                        continue
                    
                    if extreme_type == 'peak':
                        if current_value <= obv[j]:
                            is_extreme = False
                            break
                    else:  # trough
                        if current_value >= obv[j]:
                            is_extreme = False
                            break
                
                if is_extreme:
                    extremes.append({
                        'index': i,
                        'value': current_value,
                        'type': extreme_type
                    })
            
            return extremes

        except Exception as e:
            logger.warning(f"OBV极值检测失败: {e}")
            return []

    def _find_matching_obv_extreme(self, price_extreme: Any, obv_extremes: List[Dict[str, Any]],
                                  obv: np.ndarray) -> Optional[Dict[str, Any]]:
        """寻找匹配的OBV极值"""
        if not obv_extremes:
            return None

        # 寻找时间上最接近的极值点
        min_distance = float('inf')
        best_match = None

        for obv_extreme in obv_extremes:
            distance = abs(obv_extreme['index'] - price_extreme.index)
            if distance < min_distance and distance <= 15:  # OBV时间窗口较大
                min_distance = distance
                best_match = obv_extreme

        return best_match

    def _create_obv_divergence_pattern(self, pattern_type: str, price_extreme: Any,
                                     obv_extreme: Dict[str, Any], obv: np.ndarray) -> Optional[Dict[str, Any]]:
        """创建OBV背离模式"""
        try:
            # 计算背离强度
            time_diff = abs(obv_extreme['index'] - price_extreme.index)
            if time_diff > 15:  # 时间差太大
                return None

            # OBV背离强度计算（考虑累积性质）
            base_strength = max(0, 1.0 - time_diff / 15.0) * price_extreme.strength

            # 基于OBV变化幅度调整强度
            if len(obv) > obv_extreme['index'] + 5:
                recent_obv_range = np.max(obv[max(0, obv_extreme['index']-5):obv_extreme['index']+6]) - \
                                  np.min(obv[max(0, obv_extreme['index']-5):obv_extreme['index']+6])
                if recent_obv_range > 0:
                    obv_prominence = abs(obv_extreme['value'] - np.mean(obv[max(0, obv_extreme['index']-5):obv_extreme['index']+6]))
                    prominence_factor = min(obv_prominence / recent_obv_range, 1.0)
                    base_strength *= (0.5 + 0.5 * prominence_factor)

            return {
                'type': pattern_type,
                'strength': min(base_strength, 1.0),
                'price_extreme': {
                    'index': price_extreme.index,
                    'price': price_extreme.price,
                    'type': price_extreme.type
                },
                'obv_extreme': obv_extreme,
                'time_synchronization': 1.0 - time_diff / 15.0,
                'volume_price_relationship': 'divergent',  # OBV背离意味着量价背离
                'institutional_implication': self._assess_institutional_implication(obv_extreme, price_extreme)
            }

        except Exception as e:
            logger.warning(f"OBV背离模式创建失败: {e}")
            return None

    def _assess_institutional_implication(self, obv_extreme: Dict[str, Any], price_extreme: Any) -> str:
        """评估机构行为含义"""
        try:
            # 基于价格和OBV的方向判断机构行为
            if price_extreme.type == 'peak' and obv_extreme['type'] == 'trough':
                return 'institutional_selling'  # 价格高点但OBV低点，机构在卖出
            elif price_extreme.type == 'trough' and obv_extreme['type'] == 'peak':
                return 'institutional_buying'   # 价格低点但OBV高点，机构在买入
            elif price_extreme.type == 'peak' and obv_extreme['type'] == 'peak':
                return 'retail_driven_rally'    # 价格和OBV都高，散户推动
            elif price_extreme.type == 'trough' and obv_extreme['type'] == 'trough':
                return 'broad_based_selling'    # 价格和OBV都低，普遍卖出
            else:
                return 'mixed_signals'

        except Exception as e:
            logger.warning(f"机构行为含义评估失败: {e}")
            return 'unknown'

    def _confirm_trend_break(self, obv: np.ndarray, break_index: int, is_upward: bool) -> Dict[str, Any]:
        """确认趋势突破"""
        try:
            # 检查后续几个周期是否保持突破状态
            confirmed_periods = 0
            max_check_periods = min(self.trend_break_confirmation, len(obv) - break_index - 1)

            if max_check_periods <= 0:
                return {'confirmed': False, 'strength': 0.0, 'confirmed_periods': 0}

            # 计算突破前的趋势基准
            pre_break_obv = obv[max(0, break_index - 5):break_index]
            baseline = np.mean(pre_break_obv) if len(pre_break_obv) > 0 else obv[break_index]

            for i in range(1, max_check_periods + 1):
                if break_index + i >= len(obv):
                    break

                current_obv = obv[break_index + i]
                if is_upward:
                    if current_obv > baseline:
                        confirmed_periods += 1
                else:
                    if current_obv < baseline:
                        confirmed_periods += 1

            confirmation_ratio = confirmed_periods / max_check_periods if max_check_periods > 0 else 0

            return {
                'confirmed': confirmation_ratio >= 0.6,
                'strength': confirmation_ratio,
                'confirmed_periods': confirmed_periods
            }

        except Exception as e:
            logger.warning(f"OBV趋势突破确认失败: {e}")
            return {'confirmed': False, 'strength': 0.0, 'confirmed_periods': 0}

    def _calculate_obv_trend_consistency(self, obv_values: np.ndarray) -> float:
        """计算OBV趋势一致性"""
        try:
            if len(obv_values) < 3:
                return 0.5

            # 计算连续变化方向的一致性
            changes = np.diff(obv_values)
            positive_changes = np.sum(changes > 0)
            negative_changes = np.sum(changes < 0)
            total_changes = len(changes)

            if total_changes == 0:
                return 0.5

            # 一致性 = 主导方向的比例
            consistency = max(positive_changes, negative_changes) / total_changes

            return consistency

        except Exception as e:
            logger.warning(f"OBV趋势一致性计算失败: {e}")
            return 0.5

    def _generate_summary(self, patterns: List[Dict[str, Any]], signals: List[Dict[str, Any]],
                         accumulation_distribution: Dict[str, Any], volume_price_analysis: Dict[str, Any],
                         institutional_flow: Dict[str, Any]) -> Dict[str, Any]:
        """生成汇总信息"""
        try:
            total_patterns = len(patterns)
            confirmed_patterns = len([p for p in patterns if p.get('strength', 0) > 0.5])
            max_strength = max([p.get('strength', 0) for p in patterns], default=0)

            # 基于累积分散和机构流向生成建议
            current_phase = accumulation_distribution.get('current_phase', 'neutral')
            institutional_activity = institutional_flow.get('institutional_activity', 'low')
            flow_direction = institutional_flow.get('flow_direction', 'neutral')

            # 趋势突破信号统计
            trend_breaks = len([s for s in signals if s['type'] == 'obv_trend_break'])
            upward_breaks = len([s for s in signals if s['type'] == 'obv_trend_break' and s['direction'] == 'upward'])

            if current_phase == 'accumulation' and institutional_activity == 'high' and flow_direction == 'inflow':
                recommendation = 'strong_buy'
            elif current_phase == 'distribution' and institutional_activity == 'high' and flow_direction == 'outflow':
                recommendation = 'strong_sell'
            elif upward_breaks > 0 and institutional_activity != 'low':
                recommendation = 'buy'
            elif trend_breaks > upward_breaks and institutional_activity != 'low':
                recommendation = 'sell'
            elif max_strength > 0.7:
                recommendation = 'moderate_signal'
            else:
                recommendation = 'weak_signal'

            return {
                'total_patterns': total_patterns,
                'confirmed_patterns': confirmed_patterns,
                'max_strength': max_strength,
                'recommendation': recommendation,
                'current_phase': current_phase,
                'institutional_activity': institutional_activity,
                'flow_direction': flow_direction,
                'trend_breaks': trend_breaks,
                'volume_price_divergence': volume_price_analysis.get('volume_price_divergence', False),
                'obv_price_correlation': volume_price_analysis.get('obv_price_correlation', 0)
            }

        except Exception as e:
            logger.warning(f"OBV汇总生成失败: {e}")
            return {
                'total_patterns': 0,
                'confirmed_patterns': 0,
                'max_strength': 0,
                'recommendation': 'no_signal'
            }

    def _get_empty_result(self) -> IndicatorResult:
        """获取空结果"""
        return IndicatorResult(
            patterns=[],
            summary={
                'total_patterns': 0,
                'confirmed_patterns': 0,
                'max_strength': 0,
                'recommendation': 'no_signal'
            },
            signals=[],
            metadata={'error': 'No valid data'}
        )
