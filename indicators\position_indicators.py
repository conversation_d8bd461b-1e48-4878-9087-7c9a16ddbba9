"""
价格位置关系模块

提供价格与通道位置关系的计算功能。
主要功能：
- 判断价格相对于通道的位置（上方、内部、下方）
- 计算价格突破通道的状态
- 提供位置关系的量化指标
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
import logging
from enum import Enum

logger = logging.getLogger(__name__)


class PositionType(Enum):
    """价格位置类型枚举"""
    ABOVE = "above"      # 价格在通道上方
    INSIDE = "inside"    # 价格在通道内部
    BELOW = "below"      # 价格在通道下方


class BreakoutType(Enum):
    """突破类型枚举"""
    NONE = "none"                    # 无突破
    UPPER_BREAKOUT = "upper_breakout"    # 突破上轨
    LOWER_BREAKOUT = "lower_breakout"    # 突破下轨
    UPPER_BREAKDOWN = "upper_breakdown"  # 跌破上轨
    LOWER_BREAKDOWN = "lower_breakdown"  # 跌破下轨


def calculate_price_position(df: pd.DataFrame, 
                           price_column: str = 'close',
                           channel_names: List[str] = ['channel1', 'channel2']) -> pd.DataFrame:
    """
    计算价格与通道位置关系
    
    Args:
        df: 包含价格和通道数据的DataFrame
        price_column: 价格列名，默认为'close'
        channel_names: 通道名称列表，默认为['channel1', 'channel2']
        
    Returns:
        包含位置关系指标的DataFrame副本
        
    Raises:
        ValueError: 当输入数据无效时
        KeyError: 当缺少必需的列时
    """
    if df.empty:
        raise ValueError("输入DataFrame不能为空")
        
    if price_column not in df.columns:
        raise KeyError(f"价格列 '{price_column}' 不存在于DataFrame中")
        
    # 验证通道数据存在
    for channel_name in channel_names:
        required_columns = [f'{channel_name}_upper', f'{channel_name}_lower']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise KeyError(f"通道 {channel_name} 缺少必需的列: {missing_columns}")
    
    # 创建DataFrame副本
    result_df = df.copy()
    
    try:
        prices = result_df[price_column].values
        
        # 为每个通道计算位置关系
        for channel_name in channel_names:
            upper_values = result_df[f'{channel_name}_upper'].values
            lower_values = result_df[f'{channel_name}_lower'].values
            
            # 计算位置类型
            position_types = _calculate_position_types(prices, upper_values, lower_values)
            result_df[f'{channel_name}_position'] = position_types
            
            # 计算位置比率（价格相对于通道的位置）
            position_ratios = _calculate_position_ratios(prices, upper_values, lower_values)
            result_df[f'{channel_name}_position_ratio'] = position_ratios
            
            # 计算距离指标
            distance_metrics = _calculate_distance_metrics(prices, upper_values, lower_values)
            result_df[f'{channel_name}_distance_upper'] = distance_metrics['distance_upper']
            result_df[f'{channel_name}_distance_lower'] = distance_metrics['distance_lower']
            result_df[f'{channel_name}_distance_middle'] = distance_metrics['distance_middle']
            
            # 计算突破状态
            breakout_states = _calculate_breakout_states(result_df, price_column, channel_name)
            result_df[f'{channel_name}_breakout'] = breakout_states
            
        logger.debug(f"成功计算 {len(channel_names)} 个通道的位置关系")
        return result_df
        
    except Exception as e:
        logger.error(f"计算价格位置关系失败: {str(e)}")
        raise


def get_position_at_index(df: pd.DataFrame, 
                         index: int,
                         price_column: str = 'close',
                         channel_name: str = 'channel1') -> Dict[str, Union[str, float]]:
    """
    获取指定索引处的价格位置信息
    
    Args:
        df: 包含价格和通道数据的DataFrame
        index: 数据索引
        price_column: 价格列名
        channel_name: 通道名称
        
    Returns:
        包含位置信息的字典
        
    Raises:
        IndexError: 当索引超出范围时
        KeyError: 当缺少必需的列时
    """
    if index < 0 or index >= len(df):
        raise IndexError(f"索引 {index} 超出DataFrame范围 [0, {len(df)-1}]")
        
    required_columns = [
        price_column,
        f'{channel_name}_upper',
        f'{channel_name}_lower',
        f'{channel_name}_position'
    ]
    
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise KeyError(f"缺少必需的列: {missing_columns}")
        
    try:
        row = df.iloc[index]
        price = float(row[price_column])
        upper = float(row[f'{channel_name}_upper'])
        lower = float(row[f'{channel_name}_lower'])
        
        return {
            'price': price,
            'upper': upper,
            'lower': lower,
            'position': row[f'{channel_name}_position'],
            'position_ratio': float(row.get(f'{channel_name}_position_ratio', 0)),
            'distance_upper': float(row.get(f'{channel_name}_distance_upper', 0)),
            'distance_lower': float(row.get(f'{channel_name}_distance_lower', 0)),
            'distance_middle': float(row.get(f'{channel_name}_distance_middle', 0)),
            'breakout': row.get(f'{channel_name}_breakout', BreakoutType.NONE.value)
        }
        
    except Exception as e:
        logger.error(f"获取位置信息失败: {str(e)}")
        raise


def find_position_changes(df: pd.DataFrame, 
                         channel_name: str = 'channel1') -> Dict[str, List[int]]:
    """
    寻找价格位置变化的时点
    
    Args:
        df: 包含位置数据的DataFrame
        channel_name: 通道名称
        
    Returns:
        包含位置变化时点的字典，格式为:
        {
            'to_above': [索引列表],    # 变为上方的时点
            'to_inside': [索引列表],   # 变为内部的时点
            'to_below': [索引列表]     # 变为下方的时点
        }
    """
    position_column = f'{channel_name}_position'
    
    if position_column not in df.columns:
        raise KeyError(f"位置列 '{position_column}' 不存在")
        
    try:
        positions = df[position_column].values
        
        to_above = []
        to_inside = []
        to_below = []
        
        for i in range(1, len(positions)):
            prev_pos = positions[i-1]
            curr_pos = positions[i]
            
            if prev_pos != curr_pos:
                if curr_pos == PositionType.ABOVE.value:
                    to_above.append(i)
                elif curr_pos == PositionType.INSIDE.value:
                    to_inside.append(i)
                elif curr_pos == PositionType.BELOW.value:
                    to_below.append(i)
                    
        return {
            'to_above': to_above,
            'to_inside': to_inside,
            'to_below': to_below
        }
        
    except Exception as e:
        logger.error(f"寻找位置变化失败: {str(e)}")
        raise


def find_breakout_points(df: pd.DataFrame,
                        channel_name: str = 'channel1',
                        breakout_types: Optional[List[BreakoutType]] = None) -> Dict[str, List[int]]:
    """
    寻找突破点
    
    Args:
        df: 包含突破数据的DataFrame
        channel_name: 通道名称
        breakout_types: 要寻找的突破类型列表，None表示所有类型
        
    Returns:
        包含突破点索引的字典
    """
    breakout_column = f'{channel_name}_breakout'
    
    if breakout_column not in df.columns:
        raise KeyError(f"突破列 '{breakout_column}' 不存在")
        
    if breakout_types is None:
        breakout_types = [BreakoutType.UPPER_BREAKOUT, BreakoutType.LOWER_BREAKOUT,
                         BreakoutType.UPPER_BREAKDOWN, BreakoutType.LOWER_BREAKDOWN]
        
    try:
        breakouts = df[breakout_column].values
        result = {}
        
        for breakout_type in breakout_types:
            type_name = breakout_type.value
            indices = [i for i, b in enumerate(breakouts) if b == type_name]
            result[type_name] = indices
            
        return result
        
    except Exception as e:
        logger.error(f"寻找突破点失败: {str(e)}")
        raise


def calculate_position_strength(df: pd.DataFrame,
                               channel_name: str = 'channel1',
                               window: int = 5) -> pd.Series:
    """
    计算位置强度（价格在某个位置的持续性）
    
    Args:
        df: 包含位置数据的DataFrame
        channel_name: 通道名称
        window: 计算窗口大小
        
    Returns:
        位置强度的Series
    """
    position_column = f'{channel_name}_position'
    
    if position_column not in df.columns:
        raise KeyError(f"位置列 '{position_column}' 不存在")
        
    try:
        positions = df[position_column]
        
        # 计算每个位置的连续天数
        strength = pd.Series(0.0, index=df.index)
        
        for i in range(len(positions)):
            current_pos = positions.iloc[i]
            consecutive_days = 1
            
            # 向前查找相同位置的连续天数
            for j in range(i-1, max(-1, i-window), -1):
                if positions.iloc[j] == current_pos:
                    consecutive_days += 1
                else:
                    break
                    
            # 向后查找相同位置的连续天数
            for j in range(i+1, min(len(positions), i+window)):
                if positions.iloc[j] == current_pos:
                    consecutive_days += 1
                else:
                    break
                    
            # 标准化强度值
            strength.iloc[i] = min(consecutive_days / window, 1.0)
            
        return strength
        
    except Exception as e:
        logger.error(f"计算位置强度失败: {str(e)}")
        raise


def _calculate_position_types(prices: np.ndarray, 
                            upper_values: np.ndarray, 
                            lower_values: np.ndarray) -> List[str]:
    """
    计算价格位置类型（优化版本）
    
    Args:
        prices: 价格数组
        upper_values: 上轨数组
        lower_values: 下轨数组
        
    Returns:
        位置类型列表
    """
    # 性能优化：使用numpy向量化操作替代循环
    # 创建默认值数组
    position_types = np.full(len(prices), PositionType.INSIDE.value, dtype=object)
    
    # 创建有效数据掩码
    valid_mask = ~(np.isnan(prices) | np.isnan(upper_values) | np.isnan(lower_values))
    
    # 向量化计算位置类型
    above_mask = valid_mask & (prices > upper_values)
    below_mask = valid_mask & (prices < lower_values)
    
    # 批量赋值
    position_types[above_mask] = PositionType.ABOVE.value
    position_types[below_mask] = PositionType.BELOW.value
    
    return position_types.tolist()


def _calculate_position_ratios(prices: np.ndarray,
                             upper_values: np.ndarray,
                             lower_values: np.ndarray) -> np.ndarray:
    """
    计算位置比率（0-1之间，0表示在下轨，1表示在上轨）
    
    Args:
        prices: 价格数组
        upper_values: 上轨数组
        lower_values: 下轨数组
        
    Returns:
        位置比率数组
    """
    # 避免除零错误
    channel_width = upper_values - lower_values
    channel_width = np.where(channel_width == 0, 1e-8, channel_width)
    
    # 计算位置比率
    position_ratios = (prices - lower_values) / channel_width
    
    # 处理超出范围的值
    position_ratios = np.clip(position_ratios, -1.0, 2.0)
    
    return position_ratios


def _calculate_distance_metrics(prices: np.ndarray,
                              upper_values: np.ndarray,
                              lower_values: np.ndarray) -> Dict[str, np.ndarray]:
    """
    计算距离指标
    
    Args:
        prices: 价格数组
        upper_values: 上轨数组
        lower_values: 下轨数组
        
    Returns:
        包含各种距离指标的字典
    """
    middle_values = (upper_values + lower_values) / 2
    
    return {
        'distance_upper': (prices - upper_values) / upper_values * 100,  # 相对上轨的百分比距离
        'distance_lower': (prices - lower_values) / lower_values * 100,  # 相对下轨的百分比距离
        'distance_middle': (prices - middle_values) / middle_values * 100  # 相对中线的百分比距离
    }


def _calculate_breakout_states(df: pd.DataFrame,
                             price_column: str,
                             channel_name: str) -> List[str]:
    """
    计算突破状态（优化版本）
    
    Args:
        df: 包含价格和通道数据的DataFrame
        price_column: 价格列名
        channel_name: 通道名称
        
    Returns:
        突破状态列表
    """
    prices = df[price_column].values
    upper_values = df[f'{channel_name}_upper'].values
    lower_values = df[f'{channel_name}_lower'].values
    
    # 性能优化：使用numpy向量化操作
    breakout_states = np.full(len(prices), BreakoutType.NONE.value, dtype=object)
    
    if len(prices) <= 1:
        return breakout_states.tolist()
    
    # 获取前一天和当前的数据
    prev_prices = prices[:-1]
    curr_prices = prices[1:]
    prev_upper = upper_values[:-1]
    curr_upper = upper_values[1:]
    prev_lower = lower_values[:-1]
    curr_lower = lower_values[1:]
    
    # 创建有效数据掩码
    valid_mask = ~(
        np.isnan(prev_prices) | np.isnan(curr_prices) |
        np.isnan(prev_upper) | np.isnan(curr_upper) |
        np.isnan(prev_lower) | np.isnan(curr_lower)
    )
    
    # 向量化计算突破条件
    upper_breakout_mask = valid_mask & (prev_prices <= prev_upper) & (curr_prices > curr_upper)
    upper_breakdown_mask = valid_mask & (prev_prices > prev_upper) & (curr_prices <= curr_upper)
    lower_breakdown_mask = valid_mask & (prev_prices >= prev_lower) & (curr_prices < curr_lower)
    lower_breakout_mask = valid_mask & (prev_prices < prev_lower) & (curr_prices >= curr_lower)
    
    # 批量赋值（从索引1开始，因为第一个点默认为NONE）
    breakout_states[1:][upper_breakout_mask] = BreakoutType.UPPER_BREAKOUT.value
    breakout_states[1:][upper_breakdown_mask] = BreakoutType.UPPER_BREAKDOWN.value
    breakout_states[1:][lower_breakdown_mask] = BreakoutType.LOWER_BREAKDOWN.value
    breakout_states[1:][lower_breakout_mask] = BreakoutType.LOWER_BREAKOUT.value
    
    return breakout_states.tolist()


def validate_position_data(df: pd.DataFrame,
                          channel_names: List[str] = ['channel1', 'channel2']) -> bool:
    """
    验证位置数据的有效性
    
    Args:
        df: 待验证的DataFrame
        channel_names: 通道名称列表
        
    Returns:
        验证是否通过
    """
    if df.empty:
        logger.error("DataFrame为空")
        return False
        
    for channel_name in channel_names:
        required_columns = [
            f'{channel_name}_position',
            f'{channel_name}_position_ratio',
            f'{channel_name}_breakout'
        ]
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"通道 {channel_name} 缺少位置数据列: {missing_columns}")
            return False
            
    return True


def get_position_summary(df: pd.DataFrame,
                        channel_name: str = 'channel1') -> Dict[str, int]:
    """
    获取位置分布统计
    
    Args:
        df: 包含位置数据的DataFrame
        channel_name: 通道名称
        
    Returns:
        位置分布统计字典
    """
    position_column = f'{channel_name}_position'
    
    if position_column not in df.columns:
        raise KeyError(f"位置列 '{position_column}' 不存在")
        
    try:
        positions = df[position_column]
        
        summary = {
            'total': len(positions),
            'above': sum(1 for p in positions if p == PositionType.ABOVE.value),
            'inside': sum(1 for p in positions if p == PositionType.INSIDE.value),
            'below': sum(1 for p in positions if p == PositionType.BELOW.value)
        }
        
        return summary
        
    except Exception as e:
        logger.error(f"获取位置统计失败: {str(e)}")
        raise