#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易日历模块

提供交易日历相关功能，包括交易日判断、时间处理等。

作者: QuantFM Team
创建时间: 2025-07-13
"""

import datetime
import pickle
import os
import sys
from typing import Optional, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger


class TradeCalendar:
    """交易日历类"""
    
    def __init__(self):
        self.logger = get_logger("TradeCalendar")
        self.trade_calendar_file = "data/trade_calendar.pkl"
        self.trade_days = self._load_trade_calendar()
        
    def _load_trade_calendar(self) -> List[datetime.date]:
        """加载交易日历"""
        try:
            if os.path.exists(self.trade_calendar_file):
                with open(self.trade_calendar_file, 'rb') as f:
                    trade_data = pickle.load(f)

                # 处理不同的数据格式
                trade_days = []

                if hasattr(trade_data, 'iloc'):  # DataFrame格式
                    import pandas as pd
                    df = trade_data

                    # 尝试不同的列名
                    date_column = None
                    for col in ['trade_date', 'date', 'cal_date', 'trade_day']:
                        if col in df.columns:
                            date_column = col
                            break

                    if date_column:
                        for _, row in df.iterrows():
                            date_val = row[date_column]

                            # 转换为日期对象
                            if isinstance(date_val, str):
                                try:
                                    # 尝试不同的日期格式
                                    for fmt in ['%Y-%m-%d', '%Y%m%d', '%m/%d/%Y']:
                                        try:
                                            date_obj = datetime.datetime.strptime(date_val, fmt).date()
                                            trade_days.append(date_obj)
                                            break
                                        except ValueError:
                                            continue
                                except:
                                    continue
                            elif hasattr(date_val, 'date'):  # datetime对象
                                trade_days.append(date_val.date())
                            elif isinstance(date_val, datetime.date):
                                trade_days.append(date_val)

                    if trade_days:
                        trade_days = sorted(list(set(trade_days)))  # 去重并排序
                        self.logger.info(f"交易日历加载成功，共 {len(trade_days)} 个交易日")
                        self.logger.info(f"日期范围: {min(trade_days)} 到 {max(trade_days)}")
                        return trade_days

                elif isinstance(trade_data, list):  # 列表格式
                    for item in trade_data:
                        if isinstance(item, datetime.date):
                            trade_days.append(item)
                        elif isinstance(item, str):
                            try:
                                date_obj = datetime.datetime.strptime(item, '%Y-%m-%d').date()
                                trade_days.append(date_obj)
                            except:
                                continue

                    if trade_days:
                        trade_days = sorted(list(set(trade_days)))
                        self.logger.info(f"交易日历加载成功，共 {len(trade_days)} 个交易日")
                        return trade_days

                # 如果无法解析，使用默认规则
                self.logger.warning("无法解析交易日历文件格式，使用默认规则")
                return self._generate_default_trade_days()
            else:
                self.logger.warning("交易日历文件不存在，使用默认规则")
                return self._generate_default_trade_days()
        except Exception as e:
            self.logger.error(f"加载交易日历失败: {e}")
            return self._generate_default_trade_days()
    
    def _generate_default_trade_days(self) -> List[datetime.date]:
        """生成默认交易日（简单规则：周一到周五）"""
        trade_days = []
        start_date = datetime.date(2020, 1, 1)
        end_date = datetime.date(2030, 12, 31)
        
        current_date = start_date
        while current_date <= end_date:
            # 周一到周五为交易日
            if current_date.weekday() < 5:
                trade_days.append(current_date)
            current_date += datetime.timedelta(days=1)
        
        self.logger.info(f"生成默认交易日历，共 {len(trade_days)} 个交易日")
        return trade_days
    
    def is_trade_day(self, date: Optional[datetime.date] = None) -> bool:
        """
        判断指定日期是否为交易日

        Args:
            date: 要检查的日期，默认为今天

        Returns:
            是否为交易日
        """
        if date is None:
            date = datetime.date.today()

        return date in self.trade_days

    def is_trade_date(self, date: Optional[datetime.date] = None) -> bool:
        """
        判断指定日期是否为交易日（兼容性方法）

        Args:
            date: 要检查的日期，默认为今天

        Returns:
            是否为交易日
        """
        return self.is_trade_day(date)
    
    def get_current_time(self) -> datetime.datetime:
        """获取当前时间"""
        return datetime.datetime.now()
    
    def get_next_trade_day(self, date: Optional[datetime.date] = None) -> Optional[datetime.date]:
        """
        获取下一个交易日
        
        Args:
            date: 基准日期，默认为今天
            
        Returns:
            下一个交易日
        """
        if date is None:
            date = datetime.date.today()
        
        for trade_day in self.trade_days:
            if trade_day > date:
                return trade_day
        
        return None
    
    def get_prev_trade_day(self, date: Optional[datetime.date] = None) -> Optional[datetime.date]:
        """
        获取上一个交易日
        
        Args:
            date: 基准日期，默认为今天
            
        Returns:
            上一个交易日
        """
        if date is None:
            date = datetime.date.today()
        
        prev_day = None
        for trade_day in self.trade_days:
            if trade_day >= date:
                break
            prev_day = trade_day
        
        return prev_day
    
    def get_trade_days_between(self, start_date: datetime.date, 
                              end_date: datetime.date) -> List[datetime.date]:
        """
        获取两个日期之间的所有交易日
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            交易日列表
        """
        trade_days = []
        for trade_day in self.trade_days:
            if start_date <= trade_day <= end_date:
                trade_days.append(trade_day)
        
        return trade_days
    
    def is_trading_time(self, time: Optional[datetime.datetime] = None) -> bool:
        """
        判断是否为交易时间
        
        Args:
            time: 要检查的时间，默认为当前时间
            
        Returns:
            是否为交易时间
        """
        if time is None:
            time = datetime.datetime.now()
        
        # 检查是否为交易日
        if not self.is_trade_day(time.date()):
            return False
        
        # 检查是否在交易时间段内
        time_str = time.strftime("%H:%M")
        
        # 上午交易时间：9:30-11:30
        morning_start = "09:30"
        morning_end = "11:30"
        
        # 下午交易时间：13:00-15:00
        afternoon_start = "13:00"
        afternoon_end = "15:00"
        
        return ((morning_start <= time_str <= morning_end) or 
                (afternoon_start <= time_str <= afternoon_end))
    
    def update_trade_calendar(self, trade_days: List[datetime.date]) -> bool:
        """
        更新交易日历
        
        Args:
            trade_days: 新的交易日列表
            
        Returns:
            是否更新成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.trade_calendar_file), exist_ok=True)
            
            # 保存交易日历
            with open(self.trade_calendar_file, 'wb') as f:
                pickle.dump(trade_days, f)
            
            # 更新内存中的交易日历
            self.trade_days = trade_days
            
            self.logger.info(f"交易日历更新成功，共 {len(trade_days)} 个交易日")
            return True
            
        except Exception as e:
            self.logger.error(f"更新交易日历失败: {e}")
            return False
    
    def get_stats(self) -> dict:
        """获取交易日历统计信息"""
        today = datetime.date.today()
        
        # 计算今年的交易日数量
        year_start = datetime.date(today.year, 1, 1)
        year_end = datetime.date(today.year, 12, 31)
        year_trade_days = self.get_trade_days_between(year_start, year_end)
        
        return {
            'total_trade_days': len(self.trade_days),
            'year_trade_days': len(year_trade_days),
            'is_today_trade_day': self.is_trade_day(today),
            'is_trading_time': self.is_trading_time(),
            'next_trade_day': self.get_next_trade_day(today),
            'prev_trade_day': self.get_prev_trade_day(today)
        }
