#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信号连续性管理器

负责跟踪和管理连续信号状态，实现连续周期出信号的判断逻辑。

功能特点：
1. 信号状态跟踪
2. 连续信号逻辑
3. 周期管理
4. 信号去重
5. 状态持久化
6. 连续性统计

作者: QuantFM Team
创建时间: 2025-08-08
"""

from datetime import datetime, timedelta, time as dt_time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import threading
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger


class SignalPeriodType(Enum):
    """信号周期类型"""
    OPENING = "OPENING"      # 开盘期 (09:30-09:45)
    INTRADAY = "INTRADAY"    # 盘中期 (09:45-15:00)
    OFF = "OFF"              # 非交易时间


@dataclass
class SignalState:
    """信号状态"""
    stock_code: str
    period_type: SignalPeriodType
    current_period_id: str  # 当前周期标识
    continuous_count: int   # 连续次数
    last_signal_time: Optional[datetime]
    last_period_signaled: bool  # 上一周期是否出过信号
    total_signals: int      # 总信号数
    max_continuous: int     # 最大连续次数
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'stock_code': self.stock_code,
            'period_type': self.period_type.value,
            'current_period_id': self.current_period_id,
            'continuous_count': self.continuous_count,
            'last_signal_time': self.last_signal_time,
            'last_period_signaled': self.last_period_signaled,
            'total_signals': self.total_signals,
            'max_continuous': self.max_continuous
        }


class SignalContinuityManager:
    """信号连续性管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化信号连续性管理器
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.logger = get_logger("SignalContinuityManager")
        
        # 配置参数
        self.enable_continuity_check = config.get("enable_continuity_check", True)
        self.max_signal_gap_seconds = config.get("max_signal_gap_seconds", 300)
        self.reset_on_discontinuity = config.get("reset_on_discontinuity", True)
        
        # 信号状态存储
        self.signal_states: Dict[str, SignalState] = {}
        
        # 周期管理
        self.current_period_type = SignalPeriodType.OFF
        self.current_period_id = ""
        
        # 线程锁
        self._state_lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'total_signals_processed': 0,
            'signals_allowed': 0,
            'signals_blocked': 0,
            'continuity_breaks': 0,
            'period_switches': 0
        }
        
        self.logger.info("信号连续性管理器初始化完成")
    
    def check_signal_continuity(self, stock_code: str, signal_type: str, 
                              timestamp: datetime) -> bool:
        """
        检查信号连续性
        
        Args:
            stock_code: 股票代码
            signal_type: 信号类型 ('OPENING' 或 'INTRADAY')
            timestamp: 信号时间戳
            
        Returns:
            是否允许发出信号
        """
        if not self.enable_continuity_check:
            return True
        
        self.stats['total_signals_processed'] += 1
        
        with self._state_lock:
            # 更新当前周期
            self._update_current_period(timestamp)
            
            # 获取或创建信号状态
            signal_state = self._get_or_create_signal_state(stock_code, signal_type, timestamp)
            
            # 检查是否在同一周期内已经出过信号
            current_period_id = self._get_period_id(timestamp, signal_type)
            
            if signal_state.current_period_id == current_period_id:
                # 同一周期内已经出过信号，阻止重复
                self.stats['signals_blocked'] += 1
                self.logger.debug(f"阻止重复信号: {stock_code} 在周期 {current_period_id} 内已出过信号")
                return False
            
            # 检查连续性
            is_continuous = self._check_continuity(signal_state, current_period_id, timestamp)
            
            if is_continuous:
                # 连续信号，增加计数
                signal_state.continuous_count += 1
            else:
                # 不连续，重置计数
                if self.reset_on_discontinuity:
                    signal_state.continuous_count = 1
                    self.stats['continuity_breaks'] += 1
                    self.logger.debug(f"信号连续性中断，重置计数: {stock_code}")
            
            # 更新信号状态
            signal_state.current_period_id = current_period_id
            signal_state.last_signal_time = timestamp
            signal_state.last_period_signaled = True
            signal_state.total_signals += 1
            signal_state.max_continuous = max(signal_state.max_continuous, signal_state.continuous_count)
            
            self.stats['signals_allowed'] += 1
            self.logger.info(f"允许信号: {stock_code}, 连续次数: {signal_state.continuous_count}")
            
            return True
    
    def _update_current_period(self, timestamp: datetime):
        """更新当前周期"""
        new_period_type = self._get_period_type(timestamp)
        
        if new_period_type != self.current_period_type:
            old_period = self.current_period_type
            self.current_period_type = new_period_type
            self.current_period_id = self._get_period_id(timestamp, new_period_type.value)
            
            self.stats['period_switches'] += 1
            self.logger.info(f"周期切换: {old_period.value} -> {new_period_type.value}")
            
            # 周期切换时，重置所有股票的周期信号标记
            self._reset_period_signals()
    
    def _get_period_type(self, timestamp: datetime) -> SignalPeriodType:
        """获取周期类型"""
        current_time = timestamp.time()
        
        opening_start = dt_time(9, 30, 0)
        opening_end = dt_time(9, 45, 0)
        intraday_start = dt_time(9, 45, 0)
        intraday_end = dt_time(15, 0, 0)
        
        if opening_start <= current_time < opening_end:
            return SignalPeriodType.OPENING
        elif intraday_start <= current_time < intraday_end:
            return SignalPeriodType.INTRADAY
        else:
            return SignalPeriodType.OFF
    
    def _get_period_id(self, timestamp: datetime, signal_type: str) -> str:
        """获取周期标识"""
        date_str = timestamp.strftime("%Y%m%d")
        
        if signal_type == "OPENING":
            # 开盘期：整个15分钟为一个周期
            return f"{date_str}_OPENING"
        elif signal_type == "INTRADAY":
            # 盘中期：每5分钟为一个周期
            minute = timestamp.minute
            aligned_minute = (minute // 5) * 5
            time_str = timestamp.replace(minute=aligned_minute, second=0, microsecond=0).strftime("%H%M")
            return f"{date_str}_INTRADAY_{time_str}"
        else:
            return f"{date_str}_OFF"
    
    def _get_or_create_signal_state(self, stock_code: str, signal_type: str, 
                                  timestamp: datetime) -> SignalState:
        """获取或创建信号状态"""
        if stock_code not in self.signal_states:
            period_type = SignalPeriodType(signal_type)
            current_period_id = self._get_period_id(timestamp, signal_type)
            
            self.signal_states[stock_code] = SignalState(
                stock_code=stock_code,
                period_type=period_type,
                current_period_id="",  # 初始为空，表示还未出过信号
                continuous_count=0,
                last_signal_time=None,
                last_period_signaled=False,
                total_signals=0,
                max_continuous=0
            )
        
        return self.signal_states[stock_code]
    
    def _check_continuity(self, signal_state: SignalState, current_period_id: str, 
                         timestamp: datetime) -> bool:
        """检查信号连续性"""
        if signal_state.last_signal_time is None:
            # 第一次信号，算作连续
            return True
        
        # 检查时间间隔
        time_gap = (timestamp - signal_state.last_signal_time).total_seconds()
        if time_gap > self.max_signal_gap_seconds:
            self.logger.debug(f"信号时间间隔过大: {time_gap}s > {self.max_signal_gap_seconds}s")
            return False
        
        # 检查周期连续性
        last_period_id = signal_state.current_period_id
        if not self._is_consecutive_period(last_period_id, current_period_id):
            self.logger.debug(f"周期不连续: {last_period_id} -> {current_period_id}")
            return False
        
        return True
    
    def _is_consecutive_period(self, last_period_id: str, current_period_id: str) -> bool:
        """判断是否为连续周期"""
        if not last_period_id or not current_period_id:
            return False
        
        # 解析周期ID
        try:
            last_parts = last_period_id.split('_')
            current_parts = current_period_id.split('_')
            
            # 检查日期是否相同
            if last_parts[0] != current_parts[0]:
                return False
            
            # 检查周期类型
            if len(last_parts) >= 2 and len(current_parts) >= 2:
                last_type = last_parts[1]
                current_type = current_parts[1]
                
                if last_type == "OPENING" and current_type == "INTRADAY":
                    # 从开盘期到盘中期，算作连续
                    return True
                elif last_type == "INTRADAY" and current_type == "INTRADAY":
                    # 盘中期内的连续5分钟周期
                    if len(last_parts) >= 3 and len(current_parts) >= 3:
                        last_time = last_parts[2]
                        current_time = current_parts[2]
                        
                        # 计算时间差
                        last_hour = int(last_time[:2])
                        last_minute = int(last_time[2:])
                        current_hour = int(current_time[:2])
                        current_minute = int(current_time[2:])
                        
                        last_total_minutes = last_hour * 60 + last_minute
                        current_total_minutes = current_hour * 60 + current_minute
                        
                        # 5分钟间隔算作连续
                        return current_total_minutes - last_total_minutes == 5
            
            return False
            
        except Exception as e:
            self.logger.error(f"解析周期ID失败: {e}")
            return False
    
    def _reset_period_signals(self):
        """重置周期信号标记"""
        with self._state_lock:
            for signal_state in self.signal_states.values():
                signal_state.last_period_signaled = False
    
    def get_signal_state(self, stock_code: str) -> Optional[SignalState]:
        """获取信号状态"""
        return self.signal_states.get(stock_code)
    
    def get_continuous_count(self, stock_code: str) -> int:
        """获取连续次数"""
        signal_state = self.get_signal_state(stock_code)
        return signal_state.continuous_count if signal_state else 0
    
    def reset_signal_state(self, stock_code: str):
        """重置信号状态"""
        if stock_code in self.signal_states:
            del self.signal_states[stock_code]
            self.logger.info(f"重置信号状态: {stock_code}")
    
    def clear_all_states(self):
        """清空所有信号状态"""
        with self._state_lock:
            self.signal_states.clear()
            self.logger.info("清空所有信号状态")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._state_lock:
            total_stocks = len(self.signal_states)
            active_continuous = sum(1 for state in self.signal_states.values() 
                                  if state.continuous_count > 0)
            max_continuous_overall = max((state.max_continuous for state in self.signal_states.values()), 
                                       default=0)
            
            return {
                'total_signals_processed': self.stats['total_signals_processed'],
                'signals_allowed': self.stats['signals_allowed'],
                'signals_blocked': self.stats['signals_blocked'],
                'continuity_breaks': self.stats['continuity_breaks'],
                'period_switches': self.stats['period_switches'],
                'total_stocks_tracked': total_stocks,
                'active_continuous_stocks': active_continuous,
                'max_continuous_overall': max_continuous_overall,
                'current_period_type': self.current_period_type.value,
                'current_period_id': self.current_period_id
            }
