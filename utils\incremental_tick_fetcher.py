#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量Tick数据获取器

实现高效的增量数据查询，避免重复获取已处理的数据。

作者: QuantFM Team
创建时间: 2025-07-13
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class IncrementalTickFetcher:
    """增量Tick数据获取器"""
    
    def __init__(self, db_manager, logger, stock_list: List[str]):
        self.db_manager = db_manager
        self.logger = logger
        self.stock_list = stock_list
        
        # 查询优化
        self.batch_size = 100  # 每次查询的股票数量
        
    def fetch_incremental_ticks(self, last_tick_times: Dict[str, datetime]) -> pd.DataFrame:
        """
        增量获取Tick数据
        
        Args:
            last_tick_times: 每只股票的最后tick时间
            
        Returns:
            新的Tick数据DataFrame
        """
        try:
            all_ticks = []
            
            # 分批查询，避免SQL语句过长
            for i in range(0, len(self.stock_list), self.batch_size):
                batch_stocks = self.stock_list[i:i + self.batch_size]
                batch_ticks = self._fetch_batch_ticks(batch_stocks, last_tick_times)
                
                if not batch_ticks.empty:
                    all_ticks.append(batch_ticks)
            
            # 合并所有批次的数据
            if all_ticks:
                result_df = pd.concat(all_ticks, ignore_index=True)
                result_df = result_df.sort_values(['trade_time', 'stock_code'])
                
                self.logger.debug(f"增量获取到 {len(result_df)} 条新Tick数据")
                return result_df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"增量获取Tick数据失败: {e}")
            return pd.DataFrame()
    
    def _fetch_batch_ticks(self, stock_codes: List[str], 
                          last_tick_times: Dict[str, datetime]) -> pd.DataFrame:
        """获取单个批次的Tick数据"""
        try:
            # 构建查询条件
            conditions = []
            params = []
            
            for stock_code in stock_codes:
                last_time = last_tick_times.get(stock_code)
                if last_time:
                    conditions.append("(stock_code = %s AND trade_time > %s)")
                    params.extend([stock_code, last_time])
                else:
                    # 首次查询，获取当天数据
                    today = datetime.now().date()
                    conditions.append("(stock_code = %s AND trade_time >= %s)")
                    params.extend([stock_code, today])
            
            if not conditions:
                return pd.DataFrame()
            
            # 执行查询
            query = f"""
                SELECT trade_time, stock_code, price, volume, amount, cur_vol
                FROM stock_tick_data 
                WHERE ({' OR '.join(conditions)})
                  AND trade_time <= NOW()
                ORDER BY trade_time, stock_code
                LIMIT 10000
            """
            
            result = self.db_manager.execute_query(query, params)
            
            if not result:
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(result, columns=[
                'trade_time', 'stock_code', 'price', 'volume', 'amount', 'cur_vol'
            ])
            
            # 数据类型转换
            df['trade_time'] = pd.to_datetime(df['trade_time'])
            df['price'] = df['price'].astype(float)
            df['volume'] = df['volume'].astype(int)
            df['amount'] = df['amount'].astype(float)
            df['cur_vol'] = df['cur_vol'].astype(int)
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取批次Tick数据失败: {e}")
            return pd.DataFrame()
    
    def get_today_tick_count(self, stock_code: str) -> int:
        """获取指定股票今天的tick数量"""
        try:
            today = datetime.now().date()
            query = """
                SELECT COUNT(*) 
                FROM stock_tick_data 
                WHERE stock_code = %s 
                  AND trade_time >= %s 
                  AND trade_time < %s + INTERVAL '1 day'
            """
            
            result = self.db_manager.execute_query(query, (stock_code, today, today))
            
            if result and result[0]:
                return result[0][0]
            return 0
            
        except Exception as e:
            self.logger.error(f"获取今天tick数量失败: {e}")
            return 0
    
    def get_latest_tick_time(self, stock_code: str) -> Optional[datetime]:
        """获取指定股票的最新tick时间"""
        try:
            query = """
                SELECT MAX(trade_time) 
                FROM stock_tick_data 
                WHERE stock_code = %s 
                  AND trade_time >= CURRENT_DATE
            """
            
            result = self.db_manager.execute_query(query, (stock_code,))
            
            if result and result[0] and result[0][0]:
                return result[0][0]
            return None
            
        except Exception as e:
            self.logger.error(f"获取最新tick时间失败: {e}")
            return None
