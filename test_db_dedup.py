#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库去重功能

测试修复后的数据库批量插入去重功能。

作者: QuantFM Team
创建时间: 2025-08-11
"""

import sys
import os
from datetime import datetime, timedelta
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import get_db_manager
from utils.logger import get_logger


def test_stock_tick_data_dedup():
    """测试stock_tick_data表的去重功能"""
    logger = get_logger("TestDBDedup")
    
    try:
        db_manager = get_db_manager()
        
        # 创建测试数据，包含重复记录
        test_data = []
        base_time = datetime.now().replace(second=0, microsecond=0)
        
        # 添加正常记录
        for i in range(3):
            test_data.append({
                'trade_time': base_time + timedelta(seconds=i),
                'stock_code': '000001',
                'price': 12.40 + i * 0.01,
                'volume': 1000 + i * 100,
                'amount': 12400.0 + i * 124,
                'open': 12.40,
                'high': 12.42,
                'low': 12.38,
                'last_close': 12.39,
                'cur_vol': 100 + i * 10,
                'bid1': 12.39,
                'ask1': 12.41,
                'bid_vol1': 500,
                'ask_vol1': 600,
                'bid2': 12.38,
                'ask2': 12.42,
                'bid_vol2': 400,
                'ask_vol2': 700,
                'bid3': 12.37,
                'ask3': 12.43,
                'bid_vol3': 300,
                'ask_vol3': 800,
                'bid4': 12.36,
                'ask4': 12.44,
                'bid_vol4': 200,
                'ask_vol4': 900,
                'bid5': 12.35,
                'ask5': 12.45,
                'bid_vol5': 100,
                'ask_vol5': 1000
            })
        
        # 添加重复记录（相同的trade_time和stock_code）
        duplicate_record = test_data[0].copy()
        duplicate_record['price'] = 12.50  # 不同的价格
        duplicate_record['volume'] = 2000  # 不同的成交量
        test_data.append(duplicate_record)
        
        # 再添加一个重复记录
        duplicate_record2 = test_data[1].copy()
        duplicate_record2['price'] = 12.60
        test_data.append(duplicate_record2)
        
        print(f"测试数据总数: {len(test_data)}")
        print(f"预期去重后数量: 3 (原始3条记录)")
        
        # 测试插入
        print("\n开始测试批量插入...")
        
        on_conflict = "(trade_time, stock_code) DO UPDATE SET price = EXCLUDED.price, volume = EXCLUDED.volume"
        
        result = db_manager.insert_many(
            table='stock_tick_data',
            data_list=test_data,
            on_conflict=on_conflict,
            batch_size=100
        )
        
        print(f"插入结果: {'✅ 成功' if result else '❌ 失败'}")
        
        # 验证插入的数据
        print("\n验证插入的数据...")
        verify_sql = """
            SELECT trade_time, stock_code, price, volume 
            FROM stock_tick_data 
            WHERE trade_time >= %s 
            ORDER BY trade_time
        """
        
        verify_result = db_manager.fetch_all(verify_sql, (base_time,))
        
        print(f"数据库中的记录数: {len(verify_result)}")
        
        for i, row in enumerate(verify_result):
            if isinstance(row, dict):
                trade_time = row.get('trade_time')
                stock_code = row.get('stock_code')
                price = row.get('price')
                volume = row.get('volume')
            else:
                trade_time, stock_code, price, volume = row
            
            print(f"  记录 {i+1}: {trade_time} - {stock_code} - 价格:{price} - 成交量:{volume}")
        
        # 清理测试数据
        print("\n清理测试数据...")
        cleanup_sql = "DELETE FROM stock_tick_data WHERE trade_time >= %s"
        db_manager.execute_query(cleanup_sql, (base_time,))
        
        return result
        
    except Exception as e:
        logger.error(f"测试去重功能失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_strategy_signals_dedup():
    """测试strategy_signals表的去重功能"""
    logger = get_logger("TestStrategySignalsDedup")
    
    try:
        db_manager = get_db_manager()
        
        # 创建测试数据
        base_time = datetime.now().replace(second=0, microsecond=0)
        
        test_data = []
        
        # 添加正常记录
        for i in range(2):
            test_data.append({
                'signal_time': base_time + timedelta(minutes=i),
                'stock_code': '000001',
                'stock_name': '平安银行',
                'strategy_name': '测试策略',
                'signal_type': 'strategy_signal',
                'signal_strength': 0.8 + i * 0.1,
                'signal_price': 12.40 + i * 0.05,
                'latest_volume': 1000000,
                'avg_volume': 800000,
                'volume_ratio': 1.25,
                'latest_close': 12.40,
                'max_high_20d': 13.50,
                'breakout_ratio': 0.15,
                'strategy_data': '{}',
                'signal_note': f'测试信号{i+1}'
            })
        
        # 添加重复记录
        duplicate = test_data[0].copy()
        duplicate['signal_strength'] = 0.95  # 不同的强度
        test_data.append(duplicate)
        
        print(f"策略信号测试数据总数: {len(test_data)}")
        print(f"预期去重后数量: 2")
        
        # 测试插入
        print("\n开始测试策略信号批量插入...")
        
        on_conflict = "(stock_code, strategy_name, signal_time::DATE) DO UPDATE SET signal_strength = EXCLUDED.signal_strength"
        
        result = db_manager.insert_many(
            table='strategy_signals',
            data_list=test_data,
            on_conflict=on_conflict
        )
        
        print(f"插入结果: {'✅ 成功' if result else '❌ 失败'}")
        
        # 清理测试数据
        print("\n清理测试数据...")
        cleanup_sql = "DELETE FROM strategy_signals WHERE signal_time >= %s AND strategy_name = '测试策略'"
        db_manager.execute_query(cleanup_sql, (base_time,))
        
        return result
        
    except Exception as e:
        logger.error(f"测试策略信号去重功能失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("数据库去重功能测试")
    print("=" * 60)
    
    try:
        # 测试stock_tick_data去重
        print("1. 测试stock_tick_data表去重功能")
        print("-" * 40)
        tick_result = test_stock_tick_data_dedup()
        
        print("\n" + "=" * 60)
        
        # 测试strategy_signals去重
        print("2. 测试strategy_signals表去重功能")
        print("-" * 40)
        strategy_result = test_strategy_signals_dedup()
        
        # 总结
        print("\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        print(f"{'✅' if tick_result else '❌'} stock_tick_data去重: {'通过' if tick_result else '失败'}")
        print(f"{'✅' if strategy_result else '❌'} strategy_signals去重: {'通过' if strategy_result else '失败'}")
        
        all_passed = tick_result and strategy_result
        print(f"\n总体结果: {'🎉 全部通过' if all_passed else '⚠️ 部分失败'}")
        
        if all_passed:
            print("\n🎯 修复验证:")
            print("✅ 批量插入时自动去重功能正常")
            print("✅ 避免了'ON CONFLICT DO UPDATE command cannot affect row a second time'错误")
            print("✅ 支持多种表的不同唯一键配置")
            print("✅ 保持了数据的完整性和一致性")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
