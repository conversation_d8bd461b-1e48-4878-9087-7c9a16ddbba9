#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复策略配置

重新插入正确的策略配置数据。

作者: QuantFM Team
创建时间: 2025-08-08
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import get_db_manager
from utils.logger import get_logger


def fix_strategy_config():
    """修复策略配置"""
    logger = get_logger("FixStrategyConfig")
    
    try:
        db_manager = get_db_manager()
        
        # 删除错误的配置
        print("1. 清理错误的配置数据...")
        db_manager.execute_query("DELETE FROM strategy_configs")
        
        # 插入正确的配置
        print("2. 插入正确的策略配置...")
        
        config_data = {
            "ema_short_1": 144,
            "ema_long_1": 169,
            "ema_short_2": 576,
            "ema_long_2": 676,
            "volume_ratio": 1.5,
            "min_days": 1,
            "max_days": 60,
            "max_intrusion_days": 3,
            "pre_check_days": 60,
            "pivot_window": 5
        }
        
        sql = """
            INSERT INTO strategy_configs (
                strategy_name, strategy_class, config_data, is_enabled, description
            ) VALUES (
                %s, %s, %s, %s, %s
            )
        """
        
        params = (
            '双通道斐波那契突破',
            'strategies.trending.dual_channel_fibonacci.DualChannelFibonacciStrategy',
            json.dumps(config_data, ensure_ascii=False),
            True,
            '基于两个EMA通道的突破策略，检测从通道1下方开始，依次突破通道1和通道2的完整周期'
        )
        
        db_manager.execute_query(sql, params)
        
        # 验证插入结果
        print("3. 验证配置插入结果...")
        verify_sql = "SELECT strategy_name, config_data, is_enabled FROM strategy_configs WHERE strategy_name = %s"
        result = db_manager.fetch_one(verify_sql, ('双通道斐波那契突破',))
        
        if result:
            strategy_name, config_data_str, is_enabled = result
            print(f"✅ 策略名称: {strategy_name}")
            print(f"✅ 是否启用: {is_enabled}")
            
            # 解析配置数据
            try:
                config = json.loads(config_data_str)
                print(f"✅ 配置数据解析成功，包含 {len(config)} 个配置项:")
                for key, value in config.items():
                    print(f"   {key}: {value}")
            except Exception as e:
                print(f"❌ 配置数据解析失败: {e}")
                return False
        else:
            print("❌ 配置插入失败")
            return False
        
        print("\n✅ 策略配置修复完成！")
        return True
        
    except Exception as e:
        logger.error(f"修复策略配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = fix_strategy_config()
    if success:
        print("🎉 策略配置修复成功！")
    else:
        print("❌ 策略配置修复失败！")
        sys.exit(1)
