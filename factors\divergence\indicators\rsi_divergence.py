#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RSI背离检测器

专门处理RSI指标的背离分析，重点关注极值区域和中线突破。

作者: QuantFM Team
创建时间: 2025-08-11
"""

import numpy as np
from dataclasses import dataclass
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


@dataclass
class IndicatorResult:
    """指标分析结果"""
    patterns: List[Dict[str, Any]]      # 检测到的模式
    summary: Dict[str, Any]             # 汇总信息
    signals: List[Dict[str, Any]]       # 信号列表
    metadata: Dict[str, Any]            # 元数据


class RSIDivergenceDetector:
    """RSI背离检测器
    
    核心功能:
    1. 极值区域分析（RSI > 70超买，RSI < 30超卖）
    2. 中线（50）突破确认
    3. 极值区域停滞处理
    4. 多时间框架RSI分析
    5. 区域权重差异化
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化检测器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # RSI特定参数
        self.overbought_threshold = self.config.get('overbought_threshold', 70)
        self.oversold_threshold = self.config.get('oversold_threshold', 30)
        self.centerline = self.config.get('centerline', 50)
        self.extreme_zone_weight = self.config.get('extreme_zone_weight', 1.5)
        self.middle_zone_weight = self.config.get('middle_zone_weight', 0.8)
        self.stagnation_threshold = self.config.get('stagnation_threshold', 5)  # 连续周期数
        
    def detect(self, rsi_data: Dict[str, np.ndarray], price_extremes: List[Any],
              volume: Optional[np.ndarray] = None) -> IndicatorResult:
        """检测RSI背离模式
        
        Args:
            rsi_data: RSI数据，包含rsi值
            price_extremes: 价格极值点
            volume: 成交量数据
            
        Returns:
            指标分析结果
        """
        try:
            rsi = rsi_data.get('rsi', np.array([]))
            
            if len(rsi) == 0:
                logger.warning("RSI数据为空")
                return self._get_empty_result()
            
            patterns = []
            signals = []
            
            # 1. 分析极值区域背离
            extreme_patterns = self._analyze_extreme_zone_divergence(rsi, price_extremes)
            patterns.extend(extreme_patterns)
            
            # 2. 分析中线突破
            centerline_signals = self._analyze_centerline_breaks(rsi)
            signals.extend(centerline_signals)
            
            # 3. 检测极值区域停滞
            stagnation_analysis = self._detect_extreme_zone_stagnation(rsi)
            
            # 4. 区域分析
            zone_analysis = self._analyze_rsi_zones(rsi)
            
            # 5. 趋势强度评估
            trend_strength = self._assess_trend_strength(rsi)
            
            # 生成汇总
            summary = self._generate_summary(patterns, signals, zone_analysis, trend_strength)
            
            # 构建元数据
            metadata = {
                'data_length': len(rsi),
                'zone_analysis': zone_analysis,
                'stagnation_analysis': stagnation_analysis,
                'trend_strength': trend_strength,
                'analysis_timestamp': np.datetime64('now').astype(str)
            }
            
            return IndicatorResult(
                patterns=patterns,
                summary=summary,
                signals=signals,
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"RSI背离检测失败: {e}")
            return self._get_empty_result()
    
    def _analyze_extreme_zone_divergence(self, rsi: np.ndarray, 
                                       price_extremes: List[Any]) -> List[Dict[str, Any]]:
        """分析极值区域背离"""
        patterns = []
        
        try:
            # 寻找RSI在极值区域的点
            overbought_points = self._find_zone_extremes(rsi, self.overbought_threshold, 100, 'peak')
            oversold_points = self._find_zone_extremes(rsi, 0, self.oversold_threshold, 'trough')
            
            # 与价格极值点匹配
            for price_extreme in price_extremes:
                if price_extreme.type == 'peak':
                    # 寻找对应的RSI超买区域点
                    matching_rsi = self._find_matching_zone_extreme(
                        price_extreme, overbought_points, rsi
                    )
                    if matching_rsi:
                        pattern = self._create_zone_divergence_pattern(
                            'rsi_overbought_divergence', price_extreme, matching_rsi, rsi, 'overbought'
                        )
                        if pattern:
                            patterns.append(pattern)
                
                elif price_extreme.type == 'trough':
                    # 寻找对应的RSI超卖区域点
                    matching_rsi = self._find_matching_zone_extreme(
                        price_extreme, oversold_points, rsi
                    )
                    if matching_rsi:
                        pattern = self._create_zone_divergence_pattern(
                            'rsi_oversold_divergence', price_extreme, matching_rsi, rsi, 'oversold'
                        )
                        if pattern:
                            patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            logger.warning(f"RSI极值区域背离分析失败: {e}")
            return []
    
    def _analyze_centerline_breaks(self, rsi: np.ndarray) -> List[Dict[str, Any]]:
        """分析中线突破"""
        signals = []
        
        try:
            if len(rsi) < 2:
                return signals
            
            # 检测中线突破
            for i in range(1, len(rsi)):
                prev_rsi = rsi[i-1]
                curr_rsi = rsi[i]
                
                # 向上突破中线
                if prev_rsi <= self.centerline and curr_rsi > self.centerline:
                    # 确认突破
                    confirmation = self._confirm_centerline_break(rsi, i, 'bullish')
                    if confirmation['confirmed']:
                        signal = {
                            'type': 'centerline_break_up',
                            'index': i,
                            'strength': confirmation['strength'],
                            'rsi_value': curr_rsi,
                            'trend_implication': 'bullish'
                        }
                        signals.append(signal)
                
                # 向下突破中线
                elif prev_rsi >= self.centerline and curr_rsi < self.centerline:
                    confirmation = self._confirm_centerline_break(rsi, i, 'bearish')
                    if confirmation['confirmed']:
                        signal = {
                            'type': 'centerline_break_down',
                            'index': i,
                            'strength': confirmation['strength'],
                            'rsi_value': curr_rsi,
                            'trend_implication': 'bearish'
                        }
                        signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.warning(f"RSI中线突破分析失败: {e}")
            return []
    
    def _detect_extreme_zone_stagnation(self, rsi: np.ndarray) -> Dict[str, Any]:
        """检测极值区域停滞"""
        try:
            stagnation_periods = []
            current_stagnation = None
            
            for i, rsi_value in enumerate(rsi):
                # 检查是否在极值区域
                in_extreme_zone = (rsi_value >= self.overbought_threshold or 
                                 rsi_value <= self.oversold_threshold)
                
                if in_extreme_zone:
                    if current_stagnation is None:
                        current_stagnation = {
                            'start_index': i,
                            'zone_type': 'overbought' if rsi_value >= self.overbought_threshold else 'oversold',
                            'values': [rsi_value]
                        }
                    else:
                        current_stagnation['values'].append(rsi_value)
                else:
                    if current_stagnation is not None:
                        # 结束当前停滞期
                        current_stagnation['end_index'] = i - 1
                        current_stagnation['duration'] = i - current_stagnation['start_index']
                        
                        if current_stagnation['duration'] >= self.stagnation_threshold:
                            stagnation_periods.append(current_stagnation)
                        
                        current_stagnation = None
            
            # 处理最后一个停滞期
            if current_stagnation is not None:
                current_stagnation['end_index'] = len(rsi) - 1
                current_stagnation['duration'] = len(rsi) - current_stagnation['start_index']
                if current_stagnation['duration'] >= self.stagnation_threshold:
                    stagnation_periods.append(current_stagnation)
            
            return {
                'stagnation_periods': stagnation_periods,
                'total_stagnation_periods': len(stagnation_periods),
                'longest_stagnation': max([p['duration'] for p in stagnation_periods], default=0)
            }
            
        except Exception as e:
            logger.warning(f"RSI停滞检测失败: {e}")
            return {}
    
    def _analyze_rsi_zones(self, rsi: np.ndarray) -> Dict[str, Any]:
        """分析RSI区域分布"""
        try:
            if len(rsi) == 0:
                return {}
            
            # 计算各区域的时间比例
            overbought_ratio = np.sum(rsi >= self.overbought_threshold) / len(rsi)
            oversold_ratio = np.sum(rsi <= self.oversold_threshold) / len(rsi)
            middle_ratio = np.sum((rsi > self.oversold_threshold) & 
                                (rsi < self.overbought_threshold)) / len(rsi)
            
            # 当前RSI位置
            current_rsi = rsi[-1]
            if current_rsi >= self.overbought_threshold:
                current_zone = 'overbought'
            elif current_rsi <= self.oversold_threshold:
                current_zone = 'oversold'
            else:
                current_zone = 'middle'
            
            # 趋势方向
            if len(rsi) > 1:
                trend = 'up' if rsi[-1] > rsi[-2] else 'down'
            else:
                trend = 'neutral'
            
            return {
                'current_rsi': current_rsi,
                'current_zone': current_zone,
                'trend': trend,
                'overbought_ratio': overbought_ratio,
                'oversold_ratio': oversold_ratio,
                'middle_ratio': middle_ratio,
                'zone_reliability': self._calculate_zone_reliability(current_zone)
            }
            
        except Exception as e:
            logger.warning(f"RSI区域分析失败: {e}")
            return {}
    
    def _assess_trend_strength(self, rsi: np.ndarray) -> Dict[str, Any]:
        """评估趋势强度"""
        try:
            if len(rsi) < 10:
                return {'strength': 0, 'direction': 'neutral'}
            
            # 计算RSI的移动平均
            window = min(10, len(rsi))
            recent_rsi = rsi[-window:]
            rsi_ma = np.mean(recent_rsi)
            
            # 趋势方向和强度
            if rsi_ma > self.centerline:
                direction = 'bullish'
                strength = (rsi_ma - self.centerline) / (100 - self.centerline)
            else:
                direction = 'bearish'
                strength = (self.centerline - rsi_ma) / self.centerline
            
            # 趋势一致性
            trend_consistency = self._calculate_trend_consistency(recent_rsi)
            
            return {
                'strength': strength,
                'direction': direction,
                'consistency': trend_consistency,
                'rsi_ma': rsi_ma
            }
            
        except Exception as e:
            logger.warning(f"RSI趋势强度评估失败: {e}")
            return {'strength': 0, 'direction': 'neutral'}
    
    def _find_zone_extremes(self, rsi: np.ndarray, zone_min: float, zone_max: float, 
                           extreme_type: str) -> List[Dict[str, Any]]:
        """寻找特定区域的极值点"""
        extremes = []
        
        try:
            # 筛选在指定区域内的点
            zone_mask = (rsi >= zone_min) & (rsi <= zone_max)
            zone_indices = np.where(zone_mask)[0]
            
            if len(zone_indices) == 0:
                return extremes
            
            # 在区域内寻找极值
            window = 3
            for i in zone_indices:
                if i < window or i >= len(rsi) - window:
                    continue
                
                is_extreme = True
                current_value = rsi[i]
                
                # 检查是否为极值
                for j in range(i - window, i + window + 1):
                    if j == i or j < 0 or j >= len(rsi):
                        continue
                    
                    if extreme_type == 'peak':
                        if current_value <= rsi[j]:
                            is_extreme = False
                            break
                    else:  # trough
                        if current_value >= rsi[j]:
                            is_extreme = False
                            break
                
                if is_extreme:
                    extremes.append({
                        'index': i,
                        'value': current_value,
                        'type': extreme_type,
                        'zone': 'overbought' if zone_min >= self.overbought_threshold else 'oversold'
                    })
            
            return extremes

        except Exception as e:
            logger.warning(f"RSI区域极值检测失败: {e}")
            return []

    def _find_matching_zone_extreme(self, price_extreme: Any, rsi_extremes: List[Dict[str, Any]],
                                   rsi: np.ndarray) -> Optional[Dict[str, Any]]:
        """寻找匹配的RSI区域极值"""
        if not rsi_extremes:
            return None

        # 寻找时间上最接近的极值点
        min_distance = float('inf')
        best_match = None

        for rsi_extreme in rsi_extremes:
            distance = abs(rsi_extreme['index'] - price_extreme.index)
            if distance < min_distance and distance <= 10:  # 最大时间间隔
                min_distance = distance
                best_match = rsi_extreme

        return best_match

    def _create_zone_divergence_pattern(self, pattern_type: str, price_extreme: Any,
                                      rsi_extreme: Dict[str, Any], rsi: np.ndarray,
                                      zone_type: str) -> Optional[Dict[str, Any]]:
        """创建区域背离模式"""
        try:
            # 计算背离强度
            time_diff = abs(rsi_extreme['index'] - price_extreme.index)
            if time_diff > 10:  # 时间差太大
                return None

            # 区域权重
            zone_weight = (self.extreme_zone_weight if zone_type in ['overbought', 'oversold']
                          else self.middle_zone_weight)

            # 背离强度计算
            base_strength = max(0, 1.0 - time_diff / 10.0) * price_extreme.strength
            adjusted_strength = base_strength * zone_weight

            return {
                'type': pattern_type,
                'strength': min(adjusted_strength, 1.0),
                'zone_type': zone_type,
                'zone_weight': zone_weight,
                'price_extreme': {
                    'index': price_extreme.index,
                    'price': price_extreme.price,
                    'type': price_extreme.type
                },
                'rsi_extreme': rsi_extreme,
                'time_synchronization': 1.0 - time_diff / 10.0,
                'reliability': self._calculate_zone_reliability(zone_type)
            }

        except Exception as e:
            logger.warning(f"RSI区域背离模式创建失败: {e}")
            return None

    def _calculate_zone_reliability(self, zone_type: str) -> float:
        """计算区域可靠性"""
        # 极值区域的信号更可靠
        if zone_type in ['overbought', 'oversold']:
            return 0.8
        else:
            return 0.5

    def _confirm_centerline_break(self, rsi: np.ndarray, break_index: int,
                                direction: str) -> Dict[str, Any]:
        """确认中线突破"""
        try:
            # 检查后续几个周期是否保持突破状态
            confirmed_periods = 0
            max_check_periods = min(3, len(rsi) - break_index - 1)

            for i in range(1, max_check_periods + 1):
                if break_index + i >= len(rsi):
                    break

                if direction == 'bullish':
                    if rsi[break_index + i] > self.centerline:
                        confirmed_periods += 1
                else:  # bearish
                    if rsi[break_index + i] < self.centerline:
                        confirmed_periods += 1

            confirmation_ratio = confirmed_periods / max_check_periods if max_check_periods > 0 else 0

            return {
                'confirmed': confirmation_ratio >= 0.6,
                'strength': confirmation_ratio,
                'confirmed_periods': confirmed_periods
            }

        except Exception as e:
            logger.warning(f"RSI中线突破确认失败: {e}")
            return {'confirmed': False, 'strength': 0.0, 'confirmed_periods': 0}

    def _calculate_trend_consistency(self, rsi_values: np.ndarray) -> float:
        """计算趋势一致性"""
        try:
            if len(rsi_values) < 3:
                return 0.5

            # 计算连续变化方向的一致性
            changes = np.diff(rsi_values)
            positive_changes = np.sum(changes > 0)
            negative_changes = np.sum(changes < 0)
            total_changes = len(changes)

            if total_changes == 0:
                return 0.5

            # 一致性 = 主导方向的比例
            consistency = max(positive_changes, negative_changes) / total_changes

            return consistency

        except Exception as e:
            logger.warning(f"RSI趋势一致性计算失败: {e}")
            return 0.5

    def _generate_summary(self, patterns: List[Dict[str, Any]], signals: List[Dict[str, Any]],
                         zone_analysis: Dict[str, Any], trend_strength: Dict[str, Any]) -> Dict[str, Any]:
        """生成汇总信息"""
        try:
            total_patterns = len(patterns)
            confirmed_patterns = len([p for p in patterns if p.get('strength', 0) > 0.5])
            max_strength = max([p.get('strength', 0) for p in patterns], default=0)

            # 基于区域和趋势强度生成建议
            current_zone = zone_analysis.get('current_zone', 'middle')
            trend_direction = trend_strength.get('direction', 'neutral')
            trend_str = trend_strength.get('strength', 0)

            if current_zone == 'overbought' and trend_direction == 'bearish':
                recommendation = 'strong_sell'
            elif current_zone == 'oversold' and trend_direction == 'bullish':
                recommendation = 'strong_buy'
            elif max_strength > 0.7:
                recommendation = 'moderate_signal'
            else:
                recommendation = 'weak_signal'

            return {
                'total_patterns': total_patterns,
                'confirmed_patterns': confirmed_patterns,
                'max_strength': max_strength,
                'recommendation': recommendation,
                'current_zone': current_zone,
                'trend_direction': trend_direction,
                'trend_strength': trend_str,
                'centerline_breaks': len(signals),
                'zone_reliability': zone_analysis.get('zone_reliability', 0.5)
            }

        except Exception as e:
            logger.warning(f"RSI汇总生成失败: {e}")
            return {
                'total_patterns': 0,
                'confirmed_patterns': 0,
                'max_strength': 0,
                'recommendation': 'no_signal'
            }

    def _get_empty_result(self) -> IndicatorResult:
        """获取空结果"""
        return IndicatorResult(
            patterns=[],
            summary={
                'total_patterns': 0,
                'confirmed_patterns': 0,
                'max_strength': 0,
                'recommendation': 'no_signal'
            },
            signals=[],
            metadata={'error': 'No valid data'}
        )
