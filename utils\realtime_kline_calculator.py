#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时K线计算器

基于docs/5min_kline_synthesis_logic.md实现5分钟K线实时计算。

作者: QuantFM Team
创建时间: 2025-07-13
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class RealTimeKlineCalculator:
    """实时K线计算器"""
    
    def __init__(self, logger):
        self.logger = logger
        
    def calculate_realtime_klines(self, tick_data: pd.DataFrame, 
                                current_bucket: datetime) -> Dict[str, Dict]:
        """
        实时计算5分钟K线
        
        Args:
            tick_data: Tick数据DataFrame
            current_bucket: 当前5分钟时间桶
            
        Returns:
            K线数据字典 {stock_code: kline_data}
        """
        try:
            if tick_data.empty:
                return {}
            
            klines = {}
            
            # 按股票分组处理
            for stock_code in tick_data['stock_code'].unique():
                stock_ticks = tick_data[tick_data['stock_code'] == stock_code]
                
                # 计算该股票的K线
                kline = self._calculate_stock_kline(stock_ticks, current_bucket)
                
                if kline:
                    klines[stock_code] = kline
            
            self.logger.debug(f"计算了 {len(klines)} 只股票的5分钟K线")
            return klines
            
        except Exception as e:
            self.logger.error(f"计算实时K线失败: {e}")
            return {}
    
    def _calculate_stock_kline(self, stock_ticks: pd.DataFrame, 
                              current_bucket: datetime) -> Optional[Dict]:
        """
        计算单只股票的5分钟K线
        
        Args:
            stock_ticks: 单只股票的Tick数据
            current_bucket: 当前时间桶
            
        Returns:
            K线数据字典
        """
        try:
            if stock_ticks.empty:
                return None
            
            # 时间对齐：将tick数据对齐到5分钟桶
            aligned_ticks = self._align_ticks_to_bucket(stock_ticks, current_bucket)
            
            if aligned_ticks.empty:
                return None
            
            # 按时间排序
            aligned_ticks = aligned_ticks.sort_values('trade_time')
            
            # 计算OHLC
            open_price = aligned_ticks.iloc[0]['price']  # 第一个价格
            close_price = aligned_ticks.iloc[-1]['price']  # 最后一个价格
            high_price = aligned_ticks['price'].max()  # 最高价格
            low_price = aligned_ticks['price'].min()  # 最低价格
            
            # 计算成交量和成交额
            total_volume = aligned_ticks['cur_vol'].sum()
            total_amount = (aligned_ticks['cur_vol'] * aligned_ticks['price']).sum()
            
            # 构建K线数据
            kline_data = {
                'trade_time': current_bucket,
                'stock_code': stock_ticks.iloc[0]['stock_code'],
                'open': float(open_price),
                'high': float(high_price),
                'low': float(low_price),
                'close': float(close_price),
                'volume': int(total_volume),
                'amount': float(total_amount)
            }
            
            return kline_data
            
        except Exception as e:
            self.logger.error(f"计算股票K线失败: {e}")
            return None
    
    def _align_ticks_to_bucket(self, stock_ticks: pd.DataFrame, 
                              current_bucket: datetime) -> pd.DataFrame:
        """
        将tick数据对齐到5分钟时间桶
        
        Args:
            stock_ticks: 股票tick数据
            current_bucket: 当前时间桶
            
        Returns:
            对齐后的tick数据
        """
        try:
            # 计算时间桶的结束时间
            bucket_end = current_bucket + timedelta(minutes=5)
            
            # 筛选属于当前时间桶的tick数据
            mask = (stock_ticks['trade_time'] >= current_bucket) & \
                   (stock_ticks['trade_time'] < bucket_end)
            
            aligned_ticks = stock_ticks[mask].copy()
            
            return aligned_ticks
            
        except Exception as e:
            self.logger.error(f"对齐tick数据到时间桶失败: {e}")
            return pd.DataFrame()
    
    def align_time_to_5min(self, timestamp: datetime) -> datetime:
        """
        时间向上对齐到5分钟整点
        
        示例:
        09:35:01 -> 09:40:00
        09:37:30 -> 09:40:00
        09:40:00 -> 09:40:00 (已对齐)
        
        Args:
            timestamp: 原始时间戳
            
        Returns:
            对齐后的时间戳
        """
        try:
            minute = timestamp.minute
            aligned_minute = ((minute // 5) + 1) * 5
            
            if aligned_minute >= 60:
                # 跨小时处理
                return timestamp.replace(
                    hour=timestamp.hour + 1, 
                    minute=0, 
                    second=0, 
                    microsecond=0
                )
            else:
                return timestamp.replace(
                    minute=aligned_minute, 
                    second=0, 
                    microsecond=0
                )
                
        except Exception as e:
            self.logger.error(f"时间对齐失败: {e}")
            return timestamp
    
    def get_current_5min_bucket(self) -> datetime:
        """获取当前5分钟时间桶"""
        now = datetime.now()
        current_minute = now.minute
        
        # 对齐到5分钟
        aligned_minute = (current_minute // 5) * 5
        bucket_time = now.replace(minute=aligned_minute, second=0, microsecond=0)
        
        return bucket_time
    
    def calculate_klines_from_database(self, stock_codes: List[str], 
                                     start_time: datetime, 
                                     end_time: datetime) -> Dict[str, List[Dict]]:
        """
        从数据库直接计算5分钟K线（使用TimescaleDB优化）
        
        Args:
            stock_codes: 股票代码列表
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            K线数据字典 {stock_code: [kline1, kline2, ...]}
        """
        try:
            # 使用TimescaleDB的time_bucket函数
            query = """
                SELECT 
                    time_bucket('5 minutes', trade_time) as bucket_time,
                    stock_code,
                    first(price, trade_time) as open,
                    max(price) as high,
                    min(price) as low,
                    last(price, trade_time) as close,
                    sum(cur_vol) as volume,
                    sum(cur_vol * price) as amount
                FROM stock_tick_data 
                WHERE trade_time >= %s 
                  AND trade_time <= %s
                  AND stock_code = ANY(%s)
                GROUP BY bucket_time, stock_code
                ORDER BY stock_code, bucket_time
            """
            
            # 注意：这里需要数据库管理器，但为了保持类的独立性，
            # 这个方法可能需要在调用时传入db_manager
            # 或者移到其他地方实现
            
            self.logger.info("数据库直接计算K线功能需要db_manager支持")
            return {}
            
        except Exception as e:
            self.logger.error(f"从数据库计算K线失败: {e}")
            return {}
    
    def validate_kline_data(self, kline: Dict) -> bool:
        """
        验证K线数据的有效性
        
        Args:
            kline: K线数据
            
        Returns:
            是否有效
        """
        try:
            # 基本字段检查
            required_fields = ['trade_time', 'stock_code', 'open', 'high', 'low', 'close', 'volume', 'amount']
            for field in required_fields:
                if field not in kline:
                    return False
            
            # 价格逻辑检查
            open_price = kline['open']
            high_price = kline['high']
            low_price = kline['low']
            close_price = kline['close']
            
            # 高价应该是最高的
            if high_price < max(open_price, close_price):
                return False
            
            # 低价应该是最低的
            if low_price > min(open_price, close_price):
                return False
            
            # 成交量应该为正数
            if kline['volume'] <= 0:
                return False
            
            # 成交额应该为正数
            if kline['amount'] <= 0:
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证K线数据失败: {e}")
            return False
