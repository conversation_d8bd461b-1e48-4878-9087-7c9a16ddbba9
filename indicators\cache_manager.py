"""
缓存管理模块

提供指标计算结果的缓存功能，避免重复计算。
主要功能：
- 内存缓存机制
- 缓存键生成
- 缓存清理和管理
"""

import hashlib
import time
from typing import Any, Dict, Optional, Tuple
import pandas as pd
import numpy as np
import logging

logger = logging.getLogger(__name__)


class IndicatorCache:
    """指标计算结果缓存类"""
    
    def __init__(self, max_size: int = 100, ttl_seconds: int = 3600):
        """
        初始化缓存
        
        Args:
            max_size: 最大缓存条目数
            ttl_seconds: 缓存生存时间（秒）
        """
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self._cache: Dict[str, Tuple[Any, float]] = {}  # key -> (value, timestamp)
        self._access_order: Dict[str, float] = {}  # key -> last_access_time
        
    def _generate_cache_key(self, data_hash: str, params: Dict[str, Any]) -> str:
        """
        生成缓存键
        
        Args:
            data_hash: 数据哈希值
            params: 计算参数
            
        Returns:
            缓存键字符串
        """
        # 将参数转换为可哈希的字符串
        param_str = str(sorted(params.items()))
        combined = f"{data_hash}_{param_str}"
        return hashlib.md5(combined.encode()).hexdigest()
    
    def _get_data_hash(self, df: pd.DataFrame, columns: list = None) -> str:
        """
        计算DataFrame的哈希值
        
        Args:
            df: 输入DataFrame
            columns: 要计算哈希的列名列表，None表示所有列
            
        Returns:
            数据哈希值
        """
        try:
            if columns:
                data_to_hash = df[columns]
            else:
                data_to_hash = df
            
            # 使用DataFrame的值和索引生成哈希
            data_bytes = pd.util.hash_pandas_object(data_to_hash, index=True).values.tobytes()
            return hashlib.md5(data_bytes).hexdigest()
            
        except Exception as e:
            logger.warning(f"计算数据哈希失败: {e}")
            # 回退到简单的哈希方法
            return hashlib.md5(str(len(df)).encode()).hexdigest()
    
    def get(self, df: pd.DataFrame, params: Dict[str, Any], 
            columns: list = None) -> Optional[pd.DataFrame]:
        """
        从缓存获取计算结果
        
        Args:
            df: 输入DataFrame
            params: 计算参数
            columns: 用于计算哈希的列名列表
            
        Returns:
            缓存的结果或None
        """
        try:
            data_hash = self._get_data_hash(df, columns)
            cache_key = self._generate_cache_key(data_hash, params)
            
            if cache_key in self._cache:
                cached_value, timestamp = self._cache[cache_key]
                current_time = time.time()
                
                # 检查是否过期
                if current_time - timestamp <= self.ttl_seconds:
                    # 更新访问时间
                    self._access_order[cache_key] = current_time
                    logger.debug(f"缓存命中: {cache_key[:8]}...")
                    return cached_value.copy()  # 返回副本避免修改缓存
                else:
                    # 过期，删除缓存
                    self._remove_cache_entry(cache_key)
                    logger.debug(f"缓存过期: {cache_key[:8]}...")
            
            return None
            
        except Exception as e:
            logger.warning(f"获取缓存失败: {e}")
            return None
    
    def put(self, df: pd.DataFrame, params: Dict[str, Any], 
            result: pd.DataFrame, columns: list = None) -> None:
        """
        将计算结果存入缓存
        
        Args:
            df: 输入DataFrame
            params: 计算参数
            result: 计算结果
            columns: 用于计算哈希的列名列表
        """
        try:
            data_hash = self._get_data_hash(df, columns)
            cache_key = self._generate_cache_key(data_hash, params)
            current_time = time.time()
            
            # 检查缓存大小，如果超过限制则清理
            if len(self._cache) >= self.max_size:
                self._evict_oldest()
            
            # 存储结果副本避免外部修改影响缓存
            self._cache[cache_key] = (result.copy(), current_time)
            self._access_order[cache_key] = current_time
            
            logger.debug(f"缓存存储: {cache_key[:8]}...")
            
        except Exception as e:
            logger.warning(f"存储缓存失败: {e}")
    
    def _remove_cache_entry(self, cache_key: str) -> None:
        """删除缓存条目"""
        if cache_key in self._cache:
            del self._cache[cache_key]
        if cache_key in self._access_order:
            del self._access_order[cache_key]
    
    def _evict_oldest(self) -> None:
        """清理最旧的缓存条目"""
        if not self._access_order:
            return
            
        # 找到最旧的条目
        oldest_key = min(self._access_order.keys(), 
                        key=lambda k: self._access_order[k])
        self._remove_cache_entry(oldest_key)
        logger.debug(f"清理旧缓存: {oldest_key[:8]}...")
    
    def clear(self) -> None:
        """清空所有缓存"""
        self._cache.clear()
        self._access_order.clear()
        logger.debug("清空所有缓存")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计字典
        """
        current_time = time.time()
        expired_count = sum(
            1 for _, timestamp in self._cache.values()
            if current_time - timestamp > self.ttl_seconds
        )
        
        return {
            'total_entries': len(self._cache),
            'expired_entries': expired_count,
            'valid_entries': len(self._cache) - expired_count,
            'max_size': self.max_size,
            'ttl_seconds': self.ttl_seconds
        }
    
    def cleanup_expired(self) -> int:
        """
        清理过期的缓存条目
        
        Returns:
            清理的条目数量
        """
        current_time = time.time()
        expired_keys = [
            key for key, (_, timestamp) in self._cache.items()
            if current_time - timestamp > self.ttl_seconds
        ]
        
        for key in expired_keys:
            self._remove_cache_entry(key)
        
        if expired_keys:
            logger.debug(f"清理了 {len(expired_keys)} 个过期缓存条目")
        
        return len(expired_keys)


# 全局缓存实例
_global_cache = IndicatorCache(max_size=200, ttl_seconds=1800)  # 30分钟TTL


def get_global_cache() -> IndicatorCache:
    """获取全局缓存实例"""
    return _global_cache


def clear_global_cache() -> None:
    """清空全局缓存"""
    _global_cache.clear()


def get_cache_stats() -> Dict[str, Any]:
    """获取全局缓存统计信息"""
    return _global_cache.get_stats()


def cleanup_expired_cache() -> int:
    """清理全局缓存中的过期条目"""
    return _global_cache.cleanup_expired()