"""
工具模块初始化

包含各种工具函数和装饰器，用于简化代码和提高效率。
"""

# 时间工具
try:
    from .time import (
        get_current_time, parse_datetime, parse_date, format_datetime,
        is_weekend, is_workday, format_time_diff,
        get_trade_calendar, is_trade_date, is_trading_time,
        get_next_trade_date, get_prev_trade_date, get_latest_trade_dates,
        wait_until_next_trading, refresh_trade_calendar, get_previous_trade_dates
    )
except ImportError:
    pass

# 数据库管理器
try:
    from .db_manager import (
        get_db_manager, get_db_config, close_all_db_connections
    )
except ImportError:
    pass

# 导出日志功能
from .logger import (
    get_logger, setup_logger, Logger, CustomLogger,
    debug, info, warning, error, critical, exception,
    configure_root_logger
)
