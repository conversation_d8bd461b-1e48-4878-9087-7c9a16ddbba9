#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CCI背离检测器

专门处理CCI指标的背离分析，重点关注±100突破区域。

作者: QuantFM Team
创建时间: 2025-08-11
"""

import numpy as np
from dataclasses import dataclass
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


@dataclass
class IndicatorResult:
    """指标分析结果"""
    patterns: List[Dict[str, Any]]      # 检测到的模式
    summary: Dict[str, Any]             # 汇总信息
    signals: List[Dict[str, Any]]       # 信号列表
    metadata: Dict[str, Any]            # 元数据


class CCIDivergenceDetector:
    """CCI背离检测器
    
    核心功能:
    1. ±100突破区域作为主要信号
    2. 正常区域（-100到+100）与极值区域差异化分析
    3. 回归正常区域的确认信号
    4. 基于CCI绝对值的趋势强度评估
    5. 多时间框架支持
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化检测器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # CCI特定参数
        self.upper_threshold = self.config.get('upper_threshold', 100)
        self.lower_threshold = self.config.get('lower_threshold', -100)
        self.extreme_upper = self.config.get('extreme_upper', 200)
        self.extreme_lower = self.config.get('extreme_lower', -200)
        self.normal_zone_weight = self.config.get('normal_zone_weight', 0.7)
        self.extreme_zone_weight = self.config.get('extreme_zone_weight', 1.3)
        self.regression_confirmation_periods = self.config.get('regression_confirmation_periods', 3)
        
    def detect(self, cci_data: Dict[str, np.ndarray], price_extremes: List[Any],
              volume: Optional[np.ndarray] = None) -> IndicatorResult:
        """检测CCI背离模式
        
        Args:
            cci_data: CCI数据，包含cci值
            price_extremes: 价格极值点
            volume: 成交量数据
            
        Returns:
            指标分析结果
        """
        try:
            cci = cci_data.get('cci', np.array([]))
            
            if len(cci) == 0:
                logger.warning("CCI数据为空")
                return self._get_empty_result()
            
            patterns = []
            signals = []
            
            # 1. 分析正常区域背离
            normal_patterns = self._analyze_normal_zone_divergence(cci, price_extremes)
            patterns.extend(normal_patterns)
            
            # 2. 分析极值区域背离
            extreme_patterns = self._analyze_extreme_zone_divergence(cci, price_extremes)
            patterns.extend(extreme_patterns)
            
            # 3. 检测突破信号
            breakout_signals = self._detect_zone_breakouts(cci)
            signals.extend(breakout_signals)
            
            # 4. 检测回归信号
            regression_signals = self._detect_zone_regression(cci)
            signals.extend(regression_signals)
            
            # 5. 区域分析
            zone_analysis = self._analyze_cci_zones(cci)
            
            # 6. 趋势强度评估
            trend_strength = self._assess_trend_strength_from_cci(cci)
            
            # 生成汇总
            summary = self._generate_summary(patterns, signals, zone_analysis, trend_strength)
            
            # 构建元数据
            metadata = {
                'data_length': len(cci),
                'zone_analysis': zone_analysis,
                'trend_strength': trend_strength,
                'analysis_timestamp': np.datetime64('now').astype(str)
            }
            
            return IndicatorResult(
                patterns=patterns,
                summary=summary,
                signals=signals,
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"CCI背离检测失败: {e}")
            return self._get_empty_result()
    
    def _analyze_normal_zone_divergence(self, cci: np.ndarray, 
                                      price_extremes: List[Any]) -> List[Dict[str, Any]]:
        """分析正常区域背离"""
        patterns = []
        
        try:
            # 寻找正常区域内的CCI极值点
            normal_peaks = self._find_zone_extremes(cci, self.lower_threshold, self.upper_threshold, 'peak')
            normal_troughs = self._find_zone_extremes(cci, self.lower_threshold, self.upper_threshold, 'trough')
            
            # 与价格极值点匹配
            for price_extreme in price_extremes:
                if price_extreme.type == 'peak':
                    matching_cci = self._find_matching_zone_extreme(price_extreme, normal_peaks, cci)
                    if matching_cci:
                        pattern = self._create_zone_divergence_pattern(
                            'cci_normal_zone_divergence', price_extreme, matching_cci, cci, 'normal'
                        )
                        if pattern:
                            patterns.append(pattern)
                
                elif price_extreme.type == 'trough':
                    matching_cci = self._find_matching_zone_extreme(price_extreme, normal_troughs, cci)
                    if matching_cci:
                        pattern = self._create_zone_divergence_pattern(
                            'cci_normal_zone_divergence', price_extreme, matching_cci, cci, 'normal'
                        )
                        if pattern:
                            patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            logger.warning(f"CCI正常区域背离分析失败: {e}")
            return []
    
    def _analyze_extreme_zone_divergence(self, cci: np.ndarray, 
                                       price_extremes: List[Any]) -> List[Dict[str, Any]]:
        """分析极值区域背离"""
        patterns = []
        
        try:
            # 寻找极值区域的CCI点
            extreme_high_points = self._find_extreme_zone_points(cci, self.upper_threshold, 'high')
            extreme_low_points = self._find_extreme_zone_points(cci, self.lower_threshold, 'low')
            
            # 与价格极值点匹配
            for price_extreme in price_extremes:
                if price_extreme.type == 'peak':
                    matching_cci = self._find_matching_zone_extreme(price_extreme, extreme_high_points, cci)
                    if matching_cci:
                        pattern = self._create_zone_divergence_pattern(
                            'cci_extreme_zone_divergence', price_extreme, matching_cci, cci, 'extreme'
                        )
                        if pattern:
                            patterns.append(pattern)
                
                elif price_extreme.type == 'trough':
                    matching_cci = self._find_matching_zone_extreme(price_extreme, extreme_low_points, cci)
                    if matching_cci:
                        pattern = self._create_zone_divergence_pattern(
                            'cci_extreme_zone_divergence', price_extreme, matching_cci, cci, 'extreme'
                        )
                        if pattern:
                            patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            logger.warning(f"CCI极值区域背离分析失败: {e}")
            return []
    
    def _detect_zone_breakouts(self, cci: np.ndarray) -> List[Dict[str, Any]]:
        """检测区域突破"""
        signals = []
        
        try:
            if len(cci) < 2:
                return signals
            
            # 检测突破点
            for i in range(1, len(cci)):
                prev_cci = cci[i-1]
                curr_cci = cci[i]
                
                # 向上突破+100
                if prev_cci <= self.upper_threshold and curr_cci > self.upper_threshold:
                    confirmation = self._confirm_breakout(cci, i, 'upward')
                    if confirmation['confirmed']:
                        signal = {
                            'type': 'cci_upward_breakout',
                            'index': i,
                            'strength': confirmation['strength'],
                            'cci_value': curr_cci,
                            'breakout_magnitude': curr_cci - self.upper_threshold,
                            'trend_implication': 'strong_bullish'
                        }
                        signals.append(signal)
                
                # 向下突破-100
                elif prev_cci >= self.lower_threshold and curr_cci < self.lower_threshold:
                    confirmation = self._confirm_breakout(cci, i, 'downward')
                    if confirmation['confirmed']:
                        signal = {
                            'type': 'cci_downward_breakout',
                            'index': i,
                            'strength': confirmation['strength'],
                            'cci_value': curr_cci,
                            'breakout_magnitude': self.lower_threshold - curr_cci,
                            'trend_implication': 'strong_bearish'
                        }
                        signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.warning(f"CCI区域突破检测失败: {e}")
            return []
    
    def _detect_zone_regression(self, cci: np.ndarray) -> List[Dict[str, Any]]:
        """检测回归正常区域信号"""
        signals = []
        
        try:
            if len(cci) < 2:
                return signals
            
            # 检测回归点
            for i in range(1, len(cci)):
                prev_cci = cci[i-1]
                curr_cci = cci[i]
                
                # 从上方回归到正常区域
                if prev_cci > self.upper_threshold and curr_cci <= self.upper_threshold:
                    confirmation = self._confirm_regression(cci, i, 'from_above')
                    if confirmation['confirmed']:
                        signal = {
                            'type': 'cci_regression_from_above',
                            'index': i,
                            'strength': confirmation['strength'],
                            'cci_value': curr_cci,
                            'regression_depth': self.upper_threshold - curr_cci,
                            'trend_implication': 'bearish_confirmation'
                        }
                        signals.append(signal)
                
                # 从下方回归到正常区域
                elif prev_cci < self.lower_threshold and curr_cci >= self.lower_threshold:
                    confirmation = self._confirm_regression(cci, i, 'from_below')
                    if confirmation['confirmed']:
                        signal = {
                            'type': 'cci_regression_from_below',
                            'index': i,
                            'strength': confirmation['strength'],
                            'cci_value': curr_cci,
                            'regression_depth': curr_cci - self.lower_threshold,
                            'trend_implication': 'bullish_confirmation'
                        }
                        signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.warning(f"CCI回归检测失败: {e}")
            return []
    
    def _analyze_cci_zones(self, cci: np.ndarray) -> Dict[str, Any]:
        """分析CCI区域分布"""
        try:
            if len(cci) == 0:
                return {}
            
            # 计算各区域的时间比例
            extreme_high_ratio = np.sum(cci > self.upper_threshold) / len(cci)
            extreme_low_ratio = np.sum(cci < self.lower_threshold) / len(cci)
            normal_ratio = np.sum((cci >= self.lower_threshold) & (cci <= self.upper_threshold)) / len(cci)
            
            # 当前CCI位置和区域
            current_cci = cci[-1]
            if current_cci > self.upper_threshold:
                current_zone = 'extreme_high'
            elif current_cci < self.lower_threshold:
                current_zone = 'extreme_low'
            else:
                current_zone = 'normal'
            
            # 趋势方向
            if len(cci) > 1:
                trend = 'up' if cci[-1] > cci[-2] else 'down'
            else:
                trend = 'neutral'
            
            # 极端值统计
            max_cci = np.max(cci)
            min_cci = np.min(cci)
            
            return {
                'current_cci': current_cci,
                'current_zone': current_zone,
                'trend': trend,
                'extreme_high_ratio': extreme_high_ratio,
                'extreme_low_ratio': extreme_low_ratio,
                'normal_ratio': normal_ratio,
                'max_cci': max_cci,
                'min_cci': min_cci,
                'zone_reliability': self._calculate_zone_reliability(current_zone, current_cci)
            }
            
        except Exception as e:
            logger.warning(f"CCI区域分析失败: {e}")
            return {}
    
    def _assess_trend_strength_from_cci(self, cci: np.ndarray) -> Dict[str, Any]:
        """基于CCI评估趋势强度"""
        try:
            if len(cci) < 5:
                return {'strength': 0, 'direction': 'neutral'}
            
            # 使用CCI绝对值评估趋势强度
            recent_cci = cci[-5:]  # 最近5个周期
            avg_abs_cci = np.mean(np.abs(recent_cci))
            
            # 趋势方向
            avg_cci = np.mean(recent_cci)
            if avg_cci > 0:
                direction = 'bullish'
            elif avg_cci < 0:
                direction = 'bearish'
            else:
                direction = 'neutral'
            
            # 强度标准化（基于±100阈值）
            strength = min(avg_abs_cci / 100, 2.0) / 2.0  # 标准化到0-1
            
            # 趋势一致性
            consistency = self._calculate_cci_trend_consistency(recent_cci)
            
            return {
                'strength': strength,
                'direction': direction,
                'consistency': consistency,
                'avg_abs_cci': avg_abs_cci,
                'avg_cci': avg_cci
            }
            
        except Exception as e:
            logger.warning(f"CCI趋势强度评估失败: {e}")
            return {'strength': 0, 'direction': 'neutral'}
    
    def _find_zone_extremes(self, cci: np.ndarray, zone_min: float, zone_max: float, 
                           extreme_type: str) -> List[Dict[str, Any]]:
        """寻找特定区域的极值点"""
        extremes = []
        
        try:
            # 筛选在指定区域内的点
            zone_mask = (cci >= zone_min) & (cci <= zone_max)
            zone_indices = np.where(zone_mask)[0]
            
            if len(zone_indices) == 0:
                return extremes
            
            # 在区域内寻找极值
            window = 3
            for i in zone_indices:
                if i < window or i >= len(cci) - window:
                    continue
                
                is_extreme = True
                current_value = cci[i]
                
                # 检查是否为极值
                for j in range(i - window, i + window + 1):
                    if j == i or j < 0 or j >= len(cci):
                        continue
                    
                    if extreme_type == 'peak':
                        if current_value <= cci[j]:
                            is_extreme = False
                            break
                    else:  # trough
                        if current_value >= cci[j]:
                            is_extreme = False
                            break
                
                if is_extreme:
                    extremes.append({
                        'index': i,
                        'value': current_value,
                        'type': extreme_type,
                        'zone': 'normal'
                    })
            
            return extremes

        except Exception as e:
            logger.warning(f"CCI区域极值检测失败: {e}")
            return []

    def _find_extreme_zone_points(self, cci: np.ndarray, threshold: float, zone_type: str) -> List[Dict[str, Any]]:
        """寻找极值区域的点"""
        points = []

        try:
            if zone_type == 'high':
                # 寻找超过上阈值的点
                extreme_indices = np.where(cci > threshold)[0]
            else:  # low
                # 寻找低于下阈值的点
                extreme_indices = np.where(cci < threshold)[0]

            # 在极值区域内寻找局部极值
            window = 2
            for i in extreme_indices:
                if i < window or i >= len(cci) - window:
                    continue

                is_local_extreme = True
                current_value = cci[i]

                # 检查是否为局部极值
                for j in range(i - window, i + window + 1):
                    if j == i or j < 0 or j >= len(cci):
                        continue

                    if zone_type == 'high':
                        if current_value <= cci[j]:
                            is_local_extreme = False
                            break
                    else:  # low
                        if current_value >= cci[j]:
                            is_local_extreme = False
                            break

                if is_local_extreme:
                    points.append({
                        'index': i,
                        'value': current_value,
                        'type': 'peak' if zone_type == 'high' else 'trough',
                        'zone': 'extreme'
                    })

            return points

        except Exception as e:
            logger.warning(f"CCI极值区域点检测失败: {e}")
            return []

    def _find_matching_zone_extreme(self, price_extreme: Any, cci_extremes: List[Dict[str, Any]],
                                   cci: np.ndarray) -> Optional[Dict[str, Any]]:
        """寻找匹配的CCI区域极值"""
        if not cci_extremes:
            return None

        # 寻找时间上最接近的极值点
        min_distance = float('inf')
        best_match = None

        for cci_extreme in cci_extremes:
            distance = abs(cci_extreme['index'] - price_extreme.index)
            if distance < min_distance and distance <= 12:  # CCI时间窗口稍大
                min_distance = distance
                best_match = cci_extreme

        return best_match

    def _create_zone_divergence_pattern(self, pattern_type: str, price_extreme: Any,
                                      cci_extreme: Dict[str, Any], cci: np.ndarray,
                                      zone_type: str) -> Optional[Dict[str, Any]]:
        """创建CCI区域背离模式"""
        try:
            # 计算背离强度
            time_diff = abs(cci_extreme['index'] - price_extreme.index)
            if time_diff > 12:  # 时间差太大
                return None

            # 区域权重
            zone_weight = (self.extreme_zone_weight if zone_type == 'extreme'
                          else self.normal_zone_weight)

            # 背离强度计算
            base_strength = max(0, 1.0 - time_diff / 12.0) * price_extreme.strength
            adjusted_strength = base_strength * zone_weight

            return {
                'type': pattern_type,
                'strength': min(adjusted_strength, 1.0),
                'zone_type': zone_type,
                'zone_weight': zone_weight,
                'price_extreme': {
                    'index': price_extreme.index,
                    'price': price_extreme.price,
                    'type': price_extreme.type
                },
                'cci_extreme': cci_extreme,
                'time_synchronization': 1.0 - time_diff / 12.0,
                'reliability': self._calculate_zone_reliability(zone_type, cci_extreme['value'])
            }

        except Exception as e:
            logger.warning(f"CCI区域背离模式创建失败: {e}")
            return None

    def _calculate_zone_reliability(self, zone_type: str, cci_value: float) -> float:
        """计算区域可靠性"""
        if zone_type == 'extreme':
            # 极值区域的信号更可靠
            if abs(cci_value) > 150:  # 非常极端的值
                return 0.9
            else:
                return 0.8
        else:
            # 正常区域的信号可靠性中等
            return 0.6

    def _confirm_breakout(self, cci: np.ndarray, breakout_index: int, direction: str) -> Dict[str, Any]:
        """确认突破信号"""
        try:
            # 检查后续几个周期是否保持突破状态
            confirmed_periods = 0
            max_check_periods = min(3, len(cci) - breakout_index - 1)

            for i in range(1, max_check_periods + 1):
                if breakout_index + i >= len(cci):
                    break

                if direction == 'upward':
                    if cci[breakout_index + i] > self.upper_threshold:
                        confirmed_periods += 1
                else:  # downward
                    if cci[breakout_index + i] < self.lower_threshold:
                        confirmed_periods += 1

            confirmation_ratio = confirmed_periods / max_check_periods if max_check_periods > 0 else 0

            return {
                'confirmed': confirmation_ratio >= 0.6,
                'strength': confirmation_ratio,
                'confirmed_periods': confirmed_periods
            }

        except Exception as e:
            logger.warning(f"CCI突破确认失败: {e}")
            return {'confirmed': False, 'strength': 0.0, 'confirmed_periods': 0}

    def _confirm_regression(self, cci: np.ndarray, regression_index: int, direction: str) -> Dict[str, Any]:
        """确认回归信号"""
        try:
            # 检查后续几个周期是否保持在正常区域
            confirmed_periods = 0
            max_check_periods = min(self.regression_confirmation_periods, len(cci) - regression_index - 1)

            for i in range(1, max_check_periods + 1):
                if regression_index + i >= len(cci):
                    break

                current_cci = cci[regression_index + i]
                if self.lower_threshold <= current_cci <= self.upper_threshold:
                    confirmed_periods += 1

            confirmation_ratio = confirmed_periods / max_check_periods if max_check_periods > 0 else 0

            return {
                'confirmed': confirmation_ratio >= 0.7,  # 回归确认要求更高
                'strength': confirmation_ratio,
                'confirmed_periods': confirmed_periods
            }

        except Exception as e:
            logger.warning(f"CCI回归确认失败: {e}")
            return {'confirmed': False, 'strength': 0.0, 'confirmed_periods': 0}

    def _calculate_cci_trend_consistency(self, cci_values: np.ndarray) -> float:
        """计算CCI趋势一致性"""
        try:
            if len(cci_values) < 3:
                return 0.5

            # 计算连续变化方向的一致性
            changes = np.diff(cci_values)
            positive_changes = np.sum(changes > 0)
            negative_changes = np.sum(changes < 0)
            total_changes = len(changes)

            if total_changes == 0:
                return 0.5

            # 一致性 = 主导方向的比例
            consistency = max(positive_changes, negative_changes) / total_changes

            return consistency

        except Exception as e:
            logger.warning(f"CCI趋势一致性计算失败: {e}")
            return 0.5

    def _generate_summary(self, patterns: List[Dict[str, Any]], signals: List[Dict[str, Any]],
                         zone_analysis: Dict[str, Any], trend_strength: Dict[str, Any]) -> Dict[str, Any]:
        """生成汇总信息"""
        try:
            total_patterns = len(patterns)
            confirmed_patterns = len([p for p in patterns if p.get('strength', 0) > 0.5])
            max_strength = max([p.get('strength', 0) for p in patterns], default=0)

            # 基于区域和趋势强度生成建议
            current_zone = zone_analysis.get('current_zone', 'normal')
            trend_direction = trend_strength.get('direction', 'neutral')
            trend_str = trend_strength.get('strength', 0)

            # 突破信号统计
            upward_breakouts = len([s for s in signals if s['type'] == 'cci_upward_breakout'])
            downward_breakouts = len([s for s in signals if s['type'] == 'cci_downward_breakout'])

            if current_zone == 'extreme_high' and trend_direction == 'bearish':
                recommendation = 'strong_sell'
            elif current_zone == 'extreme_low' and trend_direction == 'bullish':
                recommendation = 'strong_buy'
            elif upward_breakouts > 0 and trend_str > 0.6:
                recommendation = 'buy'
            elif downward_breakouts > 0 and trend_str > 0.6:
                recommendation = 'sell'
            elif max_strength > 0.7:
                recommendation = 'moderate_signal'
            else:
                recommendation = 'weak_signal'

            return {
                'total_patterns': total_patterns,
                'confirmed_patterns': confirmed_patterns,
                'max_strength': max_strength,
                'recommendation': recommendation,
                'current_zone': current_zone,
                'trend_direction': trend_direction,
                'trend_strength': trend_str,
                'upward_breakouts': upward_breakouts,
                'downward_breakouts': downward_breakouts,
                'zone_reliability': zone_analysis.get('zone_reliability', 0.5)
            }

        except Exception as e:
            logger.warning(f"CCI汇总生成失败: {e}")
            return {
                'total_patterns': 0,
                'confirmed_patterns': 0,
                'max_strength': 0,
                'recommendation': 'no_signal'
            }

    def _get_empty_result(self) -> IndicatorResult:
        """获取空结果"""
        return IndicatorResult(
            patterns=[],
            summary={
                'total_patterns': 0,
                'confirmed_patterns': 0,
                'max_strength': 0,
                'recommendation': 'no_signal'
            },
            signals=[],
            metadata={'error': 'No valid data'}
        )
