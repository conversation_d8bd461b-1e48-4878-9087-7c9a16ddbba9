# 成交量激增处理器

基于事件驱动的连续监控系统，用于检测开盘期100倍和盘中期10倍的成交量激增情绪因子。

## 功能特点

- ✅ **开盘期100倍阈值检测** - 基于历史10天9:30-9:45时段平均成交量
- ✅ **盘中期10倍阈值检测** - 基于5分钟K线历史平均成交量
- ✅ **连续监控机制** - 事件驱动，不使用定时间隔
- ✅ **连续信号管理** - 同一周期内只出一次信号，支持连续计数
- ✅ **多级缓存优化** - 提高数据访问效率
- ✅ **飞书通知集成** - 支持新的信号格式和连续次数显示
- ✅ **完整的测试覆盖** - 单元测试、集成测试、性能测试

## 系统架构

```
factors/                          # 因子模块
├── __init__.py                   # 模块初始化
├── volume_surge_factor.py        # 成交量激增因子引擎
├── signal_continuity_manager.py  # 信号连续性管理器
└── volume_data_manager.py        # 成交量数据管理器

processes/
└── volume_surge_processor.py     # 主处理器进程

config/
└── volume_surge_processor.toml   # 专用配置文件

tests/
└── test_volume_surge_factors.py  # 测试套件
```

## 时间调度

- **09:27** - 预热启动，预加载历史数据
- **09:30-09:45** - 开盘期连续监控 (100倍阈值)
- **09:45-15:00** - 盘中期连续监控 (10倍阈值)
- **15:05** - 自动停止

## 配置说明

### 主要配置项 (config/volume_surge_processor.toml)

```toml
[factor_config]
opening_ratio_threshold = 100    # 开盘期成交量比值阈值
intraday_ratio_threshold = 10     # 盘中期成交量比值阈值
historical_days = 10              # 历史数据统计天数

[time_windows]
preheat_time = "09:27:00"         # 预热开始时间
opening_start = "09:30:00"        # 开盘期开始时间
opening_end = "09:45:00"          # 开盘期结束时间
intraday_start = "09:45:00"       # 盘中期开始时间
intraday_end = "15:00:00"         # 盘中期结束时间

[signal_continuity]
enable_continuity_check = true    # 启用连续性检查
max_signal_gap_seconds = 300      # 最大信号间隔秒数
reset_on_discontinuity = true     # 不连续时重置计数
```

## 使用方法

### 1. 运行测试

```bash
python tests/test_volume_surge_factors.py
```

### 2. 功能演示

```bash
python demo_volume_surge.py
```

### 3. 启动处理器

```python
from processes.volume_surge_processor import VolumeSurgeProcessor

# 创建处理器实例
processor = VolumeSurgeProcessor()

# 启动处理器
processor.start()

# 获取状态
status = processor.get_status()
print(status)

# 停止处理器
processor.stop()
```

### 4. 集成到主程序

处理器已集成到main.py中，会在交易时间自动启动：

```bash
python main.py
```

## 核心组件

### 1. VolumeSurgeFactorEngine
- 实现开盘期和盘中期的激增检测算法
- 支持批量处理和向量化计算
- 提供置信度计算和数据验证

### 2. SignalContinuityManager
- 管理信号连续性状态
- 实现同一周期内去重逻辑
- 支持连续信号计数和统计

### 3. VolumeDataManager
- 提供多级缓存机制
- 支持历史数据预加载
- 实现批量数据查询优化

### 4. VolumeSurgeProcessor
- 协调各组件工作
- 实现事件驱动的连续监控
- 提供完整的生命周期管理

## 信号格式

### 激增信号结构
```python
{
    'stock_code': '000001.SZ',
    'signal_type': 'OPENING',  # 或 'INTRADAY'
    'current_volume': 1000000,
    'historical_avg_volume': 10000,
    'surge_ratio': 100.0,
    'confidence': 0.85,
    'continuous_count': 3,
    'timestamp': datetime.now(),
    'period_info': '开盘期(09:35:00)'
}
```

## 性能监控

系统提供完整的性能监控指标：

- 处理延迟统计
- 缓存命中率
- 信号生成统计
- 连续性管理统计
- 数据库查询统计

## 飞书通知

支持富媒体飞书通知，包含：

- 激增级别显示
- 连续次数统计
- 置信度评估
- 历史对比数据

## 故障排除

### 常见问题

1. **导入错误**
   - 确保所有依赖包已安装
   - 检查Python路径配置

2. **数据库连接失败**
   - 检查数据库配置
   - 确认TimescaleDB服务运行正常

3. **飞书通知失败**
   - 检查飞书配置文件
   - 验证webhook_url和secret

### 调试模式

在配置文件中启用调试模式：

```toml
[volume_surge_processor]
debug_mode = true

[logging]
log_level = "DEBUG"
enable_detailed_logging = true
```

## 扩展开发

### 添加新的因子类型

1. 在`factors/`目录下创建新的因子文件
2. 继承基础因子接口
3. 在`factors/__init__.py`中导出
4. 在主处理器中集成

### 自定义信号处理

1. 继承`VolumeSurgeProcessor`类
2. 重写`_process_signals`方法
3. 实现自定义的信号处理逻辑

## 版本信息

- **版本**: 1.0.0
- **作者**: QuantFM Team
- **创建时间**: 2025-08-08
- **Python版本**: 3.8+
- **依赖**: pandas, numpy, TimescaleDB

## 许可证

内部项目，仅供团队使用。
