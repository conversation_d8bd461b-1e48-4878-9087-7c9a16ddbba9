#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
盘后策略选股调度器

实现盘后策略执行，从stock_info表获取股票列表，
从stock_kline_day表获取K线数据，执行所有策略选股，
将命中的信号存储到stock_signals表中。

作者: QuantFM Team
创建时间: 2025-01-05
"""

import sys
import os
import logging
import time
import threading
from datetime import datetime, date
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入数据库管理器和配置
from data.db_manager import get_db_manager
from data.strategy_db_manager import get_strategy_db_manager
from data.models import Signal
from config.config_manager import get_config
from services.feishu_notifier import FeishuNotifier

# 导入策略模块
try:
    from strategies.trending.dual_channel_fibonacci import DualChannelFibonacciStrategy
    DUAL_CHANNEL_STRATEGY_AVAILABLE = True
except ImportError as e:
    DUAL_CHANNEL_STRATEGY_AVAILABLE = False
    print(f"警告: 双通道斐波那契策略导入失败: {e}")


class AfterMarketScheduler:
    """盘后策略选股调度器"""

    def __init__(self):
        self.logger = self._setup_logging()
        self.db_manager = None
        self.strategy_db_manager = None
        self.feishu_notifier = None
        self.signals = []  # 存储所有信号
        self.lock = threading.Lock()  # 线程锁

        # 配置参数
        self.max_workers = 4  # 线程池大小
        self.batch_size = 50  # 每批处理的股票数量

        # 初始化数据库连接
        if not self._init_database():
            raise Exception("数据库初始化失败")

        # 策略实例
        self.dual_channel_strategy = None
        self._init_strategies()

        self.logger.info("盘后策略选股调度器初始化完成")
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger("after_market_scheduler")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _init_database(self) -> bool:
        """初始化数据库连接"""
        try:
            self.db_manager = get_db_manager()
            self.strategy_db_manager = get_strategy_db_manager()

            # 测试连接
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()

            if result:
                self.logger.info("数据库连接初始化成功")
                return True
            else:
                raise Exception("数据库连接测试失败")

        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            return False

    def _init_feishu_notifier(self) -> bool:
        """初始化飞书通知器"""
        try:
            # 从配置文件获取飞书配置
            config = get_config()
            feishu_config = config.get('feishu', {})

            webhook_url = feishu_config.get('webhook_url', '')
            secret = feishu_config.get('secret', '')

            if webhook_url and secret:
                self.feishu_notifier = FeishuNotifier(webhook_url, secret, self.logger)

                # 测试连接
                if self.feishu_notifier.test_connection():
                    self.logger.info("飞书通知器初始化成功")
                    return True
                else:
                    self.logger.warning("飞书连接测试失败，将跳过通知功能")
                    self.feishu_notifier = None
                    return True  # 不影响主流程
            else:
                self.logger.warning("飞书配置不完整，将跳过通知功能")
                return True  # 不影响主流程

        except Exception as e:
            self.logger.error(f"飞书通知器初始化失败: {e}")
            self.feishu_notifier = None
            return True  # 不影响主流程
    
    def _init_strategies(self) -> bool:
        """初始化策略实例"""
        try:
            # 初始化双通道斐波那契策略
            if DUAL_CHANNEL_STRATEGY_AVAILABLE:
                try:
                    # 从配置文件获取策略启用状态
                    config = get_config()
                    strategy_config = config.get('strategies', {})
                    dual_channel_enabled = strategy_config.get('dual_channel_fibonacci_enabled', True)
                    
                    if dual_channel_enabled:
                        self.dual_channel_strategy = DualChannelFibonacciStrategy()
                        self.logger.info("双通道斐波那契策略初始化成功")
                    else:
                        self.logger.info("双通道斐波那契策略已禁用")
                        self.dual_channel_strategy = None
                        
                except Exception as e:
                    self.logger.error(f"双通道斐波那契策略初始化失败: {e}")
                    self.dual_channel_strategy = None
                    # 策略初始化失败不影响主流程
            else:
                self.logger.warning("双通道斐波那契策略不可用，跳过初始化")
                self.dual_channel_strategy = None
            
            return True
            
        except Exception as e:
            self.logger.error(f"策略初始化过程发生异常: {e}")
            # 确保所有策略实例都设置为None
            self.dual_channel_strategy = None
            return True  # 不影响主流程

    def get_stock_list(self) -> List[Dict[str, str]]:
        """从stock_info表获取股票列表"""
        try:
            query = "SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code"
            result = self.db_manager.fetch_all(query)
            
            stocks = []
            for row in result:
                stocks.append({
                    'stock_code': row['stock_code'] if isinstance(row, dict) else row[0],
                    'stock_name': (row['stock_name'] if isinstance(row, dict) else row[1]) or f'股票{row["stock_code"] if isinstance(row, dict) else row[0]}'
                })
            
            self.logger.info(f"获取到 {len(stocks)} 只股票")
            return stocks
            
        except Exception as e:
            self.logger.error(f"获取股票列表失败: {e}")
            return []
    
    def get_stock_kline_data(self, stock_code: str) -> Optional[List[Dict]]:
        """获取股票的所有日K线数据"""
        try:
            query = """
            SELECT trade_time, open, high, low, close, volume, amount 
            FROM stock_kline_day 
            WHERE stock_code = %s 
            ORDER BY trade_time ASC
            """
            
            result = self.db_manager.fetch_all(query, (stock_code,))
            
            if result:
                self.logger.debug(f"股票 {stock_code} 获取到 {len(result)} 条K线数据")
                return result
            else:
                self.logger.warning(f"股票 {stock_code} 没有K线数据")
                return None
                
        except Exception as e:
            self.logger.error(f"获取股票 {stock_code} K线数据失败: {e}")
            return None
    
    def preprocess_kline_data(self, stock_code: str, kline_data: List[Dict]) -> Optional[pd.DataFrame]:
        """
        K线数据预处理函数
        将List[Dict]格式转换为pandas DataFrame，并添加数据验证和基础指标计算
        
        Args:
            stock_code: 股票代码
            kline_data: K线数据列表
            
        Returns:
            预处理后的DataFrame或None（如果数据无效）
        """
        try:
            if not kline_data:
                self.logger.warning(f"股票 {stock_code} K线数据为空")
                return None
            
            # 数据长度验证（降低要求，支持更多股票）
            if len(kline_data) < 700:  # 降低到700条，确保EMA_676能计算
                self.logger.warning(f"股票 {stock_code} K线数据不足700条，当前: {len(kline_data)}")
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(kline_data)
            
            # 数据格式验证
            required_columns = ['trade_time', 'open', 'high', 'low', 'close', 'volume', 'amount']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                self.logger.error(f"股票 {stock_code} 缺少必要字段: {missing_columns}")
                return None
            
            # 数据类型转换
            df['trade_time'] = pd.to_datetime(df['trade_time'])
            df['open'] = pd.to_numeric(df['open'], errors='coerce')
            df['high'] = pd.to_numeric(df['high'], errors='coerce')
            df['low'] = pd.to_numeric(df['low'], errors='coerce')
            df['close'] = pd.to_numeric(df['close'], errors='coerce')
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
            df['amount'] = pd.to_numeric(df['amount'], errors='coerce')
            
            # 检查是否有无效数据
            if df[['open', 'high', 'low', 'close', 'volume']].isnull().any().any():
                self.logger.warning(f"股票 {stock_code} 存在无效数据，将进行清理")
                # 删除包含NaN的行
                df = df.dropna(subset=['open', 'high', 'low', 'close', 'volume'])
                
                if len(df) < 1000:
                    self.logger.warning(f"股票 {stock_code} 清理后数据不足1000条")
                    return None
            
            # 数据合理性验证
            invalid_rows = (
                (df['high'] < df['low']) |
                (df['high'] < df['open']) |
                (df['high'] < df['close']) |
                (df['low'] > df['open']) |
                (df['low'] > df['close']) |
                (df['volume'] < 0) |
                (df['amount'] < 0)
            )
            
            if invalid_rows.any():
                self.logger.warning(f"股票 {stock_code} 存在 {invalid_rows.sum()} 行不合理数据，将进行清理")
                df = df[~invalid_rows]
                
                if len(df) < 1000:
                    self.logger.warning(f"股票 {stock_code} 清理后数据不足1000条")
                    return None
            
            # 按时间排序
            df = df.sort_values('trade_time').reset_index(drop=True)
            
            # 检查时间连续性（允许少量缺失）
            # date_diff = df['trade_time'].diff().dt.days
            # large_gaps = date_diff > 7  # 超过7天的间隔
            # if large_gaps.sum() > 10:  # 如果有超过10个大间隔
            #     self.logger.warning(f"股票 {stock_code} 存在较多时间间隔，可能影响指标计算")
            
            # 集成基础指标计算
            df = self._calculate_indicators(df, stock_code)
            if df is None:
                return None
            
            self.logger.debug(f"股票 {stock_code} 数据预处理完成，有效数据: {len(df)} 条")
            return df
            
        except Exception as e:
            self.logger.error(f"股票 {stock_code} 数据预处理失败: {e}")
            return None
    
    def _calculate_indicators(self, df: pd.DataFrame, stock_code: str) -> Optional[pd.DataFrame]:
        """
        计算所有基础指标
        
        Args:
            df: 原始K线DataFrame
            stock_code: 股票代码
            
        Returns:
            包含所有指标的DataFrame或None（如果计算失败）
        """
        try:
            # 导入indicators模块
            from indicators.talib_wrapper import calculate_ema_series, calculate_volume_ma
            from indicators.channel_indicators import calculate_dual_channels
            from indicators.position_indicators import calculate_price_position
            
            # 1. 计算EMA指标
            ema_periods = [144, 169, 576, 676]
            df = calculate_ema_series(df, ema_periods, 'close')
            
            # 2. 计算20周期成交量移动平均
            try:
                from indicators.talib_wrapper import calculate_volume_ma
                df = calculate_volume_ma(df, [20], 'volume')

                # 验证volume_ma_20列是否成功创建
                if 'volume_ma_20' in df.columns:
                    self.logger.debug(f"股票 {stock_code} volume_ma_20计算成功")
                else:
                    self.logger.warning(f"股票 {stock_code} volume_ma_20列未创建")

            except Exception as e:
                self.logger.warning(f"股票 {stock_code} 成交量移动平均计算失败: {e}")
                # 不中断处理，继续后续步骤

            # 3. 计算双通道指标
            channel1_params = {'upper': 144, 'lower': 169}
            channel2_params = {'upper': 576, 'lower': 676}
            df = calculate_dual_channels(df, channel1_params, channel2_params, 'close')

            # 4. 计算价格位置关系
            df = calculate_price_position(df, 'close', ['channel1', 'channel2'])

            # 5. 计算成交量比率（策略需要的指标）
            if 'volume_ma_20' in df.columns:
                df['volume_ratio'] = df['volume'] / df['volume_ma_20']
                self.logger.debug(f"股票 {stock_code} volume_ratio计算成功")
            else:
                self.logger.warning(f"股票 {stock_code} 无法计算volume_ratio，缺少volume_ma_20")

            # 验证指标计算结果（分为关键指标和可选指标）
            critical_indicators = [
                'ema_144', 'ema_169', 'ema_576', 'ema_676',
                'channel1_upper', 'channel1_lower', 'channel2_upper', 'channel2_lower'
            ]

            optional_indicators = [
                'volume_ma_20', 'volume_ratio',
                'channel1_position', 'channel2_position'
            ]

            # 检查关键指标
            missing_critical = [col for col in critical_indicators if col not in df.columns]
            if missing_critical:
                self.logger.error(f"股票 {stock_code} 关键指标计算失败，缺少: {missing_critical}")
                return None

            # 检查可选指标（缺失时给出警告但不中断处理）
            missing_optional = [col for col in optional_indicators if col not in df.columns]
            if missing_optional:
                self.logger.warning(f"股票 {stock_code} 可选指标缺失: {missing_optional}")
                # 为缺失的可选指标提供默认值
                for col in missing_optional:
                    if col == 'volume_ma_20':
                        df[col] = df['volume'].rolling(window=20, min_periods=1).mean()
                        self.logger.info(f"股票 {stock_code} 已补充 {col} 指标")
                    elif col == 'volume_ratio':
                        # 如果volume_ma_20存在，计算volume_ratio
                        if 'volume_ma_20' in df.columns:
                            df[col] = df['volume'] / df['volume_ma_20']
                        else:
                            # 如果volume_ma_20也不存在，先计算volume_ma_20再计算ratio
                            df['volume_ma_20'] = df['volume'].rolling(window=20, min_periods=1).mean()
                            df[col] = df['volume'] / df['volume_ma_20']
                        self.logger.info(f"股票 {stock_code} 已补充 {col} 指标")
                    elif col in ['channel1_position', 'channel2_position']:
                        # 这些指标如果缺失，可以设置为默认值
                        df[col] = 0.5  # 中性位置
                        self.logger.info(f"股票 {stock_code} 已补充 {col} 指标（默认值）")
            
            # 智能检查指标数据有效性
            self._validate_ema_indicators_quality(df, stock_code)
            
            self.logger.debug(f"股票 {stock_code} 指标计算完成，包含 {len([col for col in df.columns if col not in ['trade_time', 'open', 'high', 'low', 'close', 'volume', 'amount']])} 个指标")
            return df
            
        except ImportError as e:
            self.logger.error(f"股票 {stock_code} 导入indicators模块失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"股票 {stock_code} 指标计算失败: {e}")
            return None

    def _validate_ema_indicators_quality(self, df: pd.DataFrame, stock_code: str) -> None:
        """
        智能验证EMA指标数据质量

        Args:
            df: 包含EMA指标的DataFrame
            stock_code: 股票代码
        """
        try:
            total_rows = len(df)

            # EMA指标及其对应的预热期
            ema_indicators = {
                'ema_144': 144,
                'ema_169': 169,
                'ema_576': 576,
                'ema_676': 676
            }

            for col, warmup_period in ema_indicators.items():
                if col not in df.columns:
                    continue

                # 计算理论上应该有有效值的行数
                expected_valid_rows = max(0, total_rows - warmup_period)

                if expected_valid_rows <= 0:
                    # 数据长度不足以计算该EMA
                    self.logger.debug(f"股票 {stock_code} 数据长度 {total_rows} 不足以计算 {col}（需要 {warmup_period}）")
                    continue

                # 检查实际有效值数量
                valid_values = df[col].notna().sum()
                invalid_values = df[col].isna().sum()

                # 计算有效值比例
                if expected_valid_rows > 0:
                    valid_ratio = valid_values / total_rows
                    expected_ratio = expected_valid_rows / total_rows

                    # 如果有效值比例明显低于预期，给出警告
                    if valid_ratio < expected_ratio * 0.8:  # 允许20%的容差
                        self.logger.warning(
                            f"股票 {stock_code} 指标 {col} 数据质量较差: "
                            f"有效值 {valid_values}/{total_rows} ({valid_ratio:.1%}), "
                            f"预期 {expected_valid_rows}/{total_rows} ({expected_ratio:.1%})"
                        )
                    else:
                        self.logger.debug(
                            f"股票 {stock_code} 指标 {col} 数据质量良好: "
                            f"有效值 {valid_values}/{total_rows} ({valid_ratio:.1%})"
                        )

        except Exception as e:
            self.logger.error(f"股票 {stock_code} EMA指标质量验证失败: {e}")

    def execute_strategies(self, stock_code: str, stock_name: str,
                          kline_data: Optional[List[Dict]] = None,
                          preprocessed_df: Optional[pd.DataFrame] = None) -> List[Signal]:
        """
        执行所有策略，返回命中的信号
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            kline_data: 原始K线数据（用于向后兼容）
            preprocessed_df: 预处理后的DataFrame（包含所有指标）
            
        Returns:
            信号列表
            
        Note:
            优先使用preprocessed_df，如果为None则使用kline_data（向后兼容）
        """
        signals = []
        strategy_execution_stats = {}
        
        try:
            # 数据格式验证和准备
            df_for_strategies, kline_data_for_legacy = self._prepare_strategy_data(
                stock_code, preprocessed_df, kline_data
            )
            
            if df_for_strategies is None and kline_data_for_legacy is None:
                self.logger.error(f"股票 {stock_code} 没有提供有效的数据")
                return signals
            
            # 记录数据使用模式
            data_mode = "preprocessed" if df_for_strategies is not None else "legacy"
            self.logger.debug(f"股票 {stock_code} 使用 {data_mode} 数据模式")
            

            
            # 执行新策略（使用预处理后的DataFrame）
            if df_for_strategies is not None:
                # 双通道斐波那契策略
                if self.dual_channel_strategy is not None:
                    try:
                        dual_channel_signal = self.dual_channel_strategy.analyze(
                            stock_code, stock_name, preprocessed_df=df_for_strategies
                        )
                        if dual_channel_signal:
                            signals.append(dual_channel_signal)
                            strategy_execution_stats["双通道斐波那契"] = "成功"
                            self.logger.debug(f"股票 {stock_code} 双通道斐波那契策略产生信号")
                        else:
                            strategy_execution_stats["双通道斐波那契"] = "无信号"
                    except Exception as e:
                        self.logger.error(f"股票 {stock_code} 双通道斐波那契策略执行失败: {e}")
                        strategy_execution_stats["双通道斐波那契"] = f"失败: {str(e)}"
                        # 记录详细的异常信息用于调试
                        import traceback
                        self.logger.debug(f"双通道策略异常详情: {traceback.format_exc()}")
                else:
                    self.logger.debug(f"股票 {stock_code} 双通道斐波那契策略未初始化，跳过执行")
            
            # 记录策略执行统计
            if signals:
                self.logger.info(f"股票 {stock_code} 产生 {len(signals)} 个信号")
                for strategy_name, status in strategy_execution_stats.items():
                    if status == "成功":
                        self.logger.debug(f"  {strategy_name}: {status}")
            else:
                self.logger.debug(f"股票 {stock_code} 无信号产生")
                
        except Exception as e:
            self.logger.error(f"股票 {stock_code} 策略执行过程发生异常: {e}")
            # 记录详细的异常信息用于调试
            import traceback
            self.logger.debug(f"股票 {stock_code} 异常详情: {traceback.format_exc()}")
        
        return signals
    
    def _prepare_strategy_data(self, stock_code: str, 
                              preprocessed_df: Optional[pd.DataFrame], 
                              kline_data: Optional[List[Dict]]) -> tuple[Optional[pd.DataFrame], Optional[List[Dict]]]:
        """
        准备策略执行所需的数据格式
        
        Args:
            stock_code: 股票代码
            preprocessed_df: 预处理后的DataFrame
            kline_data: 原始K线数据
            
        Returns:
            (df_for_strategies, kline_data_for_legacy) 元组
        """
        try:
            df_for_strategies = None
            kline_data_for_legacy = None
            
            if preprocessed_df is not None:
                # 验证预处理数据的完整性
                if self._validate_preprocessed_data(preprocessed_df, stock_code):
                    df_for_strategies = preprocessed_df
                    # 为现有策略转换数据格式
                    kline_data_for_legacy = self._convert_df_to_dict_list(preprocessed_df)
                    if not kline_data_for_legacy:
                        self.logger.warning(f"股票 {stock_code} DataFrame转换为Dict列表失败，回退到原始数据")
                        df_for_strategies = None
                else:
                    self.logger.warning(f"股票 {stock_code} 预处理数据验证失败，回退到原始数据")
            
            # 如果预处理数据不可用，使用原始数据
            if df_for_strategies is None and kline_data is not None:
                if self._validate_raw_kline_data(kline_data, stock_code):
                    kline_data_for_legacy = kline_data
                    self.logger.debug(f"股票 {stock_code} 使用原始数据格式（向后兼容模式）")
                else:
                    self.logger.error(f"股票 {stock_code} 原始数据验证失败")
            
            return df_for_strategies, kline_data_for_legacy
            
        except Exception as e:
            self.logger.error(f"股票 {stock_code} 数据准备失败: {e}")
            return None, None
    
    def _validate_preprocessed_data(self, df: pd.DataFrame, stock_code: str) -> bool:
        """
        验证预处理后的DataFrame数据完整性
        
        Args:
            df: 预处理后的DataFrame
            stock_code: 股票代码
            
        Returns:
            验证是否通过
        """
        try:
            # 检查基础字段
            required_basic_columns = ['trade_time', 'open', 'high', 'low', 'close', 'volume', 'amount']
            missing_basic = [col for col in required_basic_columns if col not in df.columns]
            if missing_basic:
                self.logger.error(f"股票 {stock_code} 预处理数据缺少基础字段: {missing_basic}")
                return False
            
            # 检查数据长度
            if len(df) < 20:
                self.logger.warning(f"股票 {stock_code} 预处理数据长度不足: {len(df)}")
                return False
            
            # 检查指标字段（如果存在）
            expected_indicator_columns = [
                'ema_144', 'ema_169', 'ema_576', 'ema_676',
                'volume_ma_20',
                'channel1_upper', 'channel1_lower', 'channel2_upper', 'channel2_lower'
            ]
            
            missing_indicators = [col for col in expected_indicator_columns if col not in df.columns]
            if missing_indicators:
                self.logger.debug(f"股票 {stock_code} 预处理数据缺少部分指标: {missing_indicators}")
                # 指标缺失不影响基础策略执行，只记录调试信息
            
            # 检查数据有效性
            if df[required_basic_columns].isnull().all(axis=1).any():
                self.logger.warning(f"股票 {stock_code} 预处理数据存在完全无效的行")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"股票 {stock_code} 预处理数据验证异常: {e}")
            return False
    
    def _validate_raw_kline_data(self, kline_data: List[Dict], stock_code: str) -> bool:
        """
        验证原始K线数据的完整性
        
        Args:
            kline_data: 原始K线数据
            stock_code: 股票代码
            
        Returns:
            验证是否通过
        """
        try:
            if not kline_data:
                self.logger.error(f"股票 {stock_code} 原始K线数据为空")
                return False
            
            if len(kline_data) < 20:
                self.logger.warning(f"股票 {stock_code} 原始K线数据长度不足: {len(kline_data)}")
                return False
            
            # 检查数据格式
            required_fields = ['trade_time', 'open', 'high', 'low', 'close', 'volume', 'amount']
            first_row = kline_data[0]
            
            missing_fields = [field for field in required_fields if field not in first_row]
            if missing_fields:
                self.logger.error(f"股票 {stock_code} 原始数据缺少必要字段: {missing_fields}")
                return False
            
            # 检查数据类型（抽样检查前几行）
            for i, row in enumerate(kline_data[:min(5, len(kline_data))]):
                try:
                    float(row['open'])
                    float(row['high'])
                    float(row['low'])
                    float(row['close'])
                    float(row['volume'])
                    float(row['amount'])
                except (ValueError, TypeError) as e:
                    self.logger.error(f"股票 {stock_code} 第{i+1}行数据类型错误: {e}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"股票 {stock_code} 原始数据验证异常: {e}")
            return False
    
    def _convert_df_to_dict_list(self, df: pd.DataFrame) -> List[Dict]:
        """
        将DataFrame转换为Dict列表格式，供现有策略使用
        
        Args:
            df: 包含K线数据的DataFrame
            
        Returns:
            Dict格式的K线数据列表
        """
        try:
            # 只保留基础K线数据字段
            basic_columns = ['trade_time', 'open', 'high', 'low', 'close', 'volume', 'amount']
            df_basic = df[basic_columns].copy()
            
            # 转换为字典列表
            kline_data = []
            for _, row in df_basic.iterrows():
                kline_dict = {
                    'trade_time': row['trade_time'],
                    'open': float(row['open']),
                    'high': float(row['high']),
                    'low': float(row['low']),
                    'close': float(row['close']),
                    'volume': int(row['volume']),
                    'amount': float(row['amount'])
                }
                kline_data.append(kline_dict)
            
            return kline_data
            
        except Exception as e:
            self.logger.error(f"DataFrame转换为Dict列表失败: {e}")
            return []
    


    def process_stock_batch(self, stocks: List[Dict[str, str]]) -> List[Signal]:
        """处理一批股票"""
        batch_signals = []

        for stock in stocks:
            try:
                stock_code = stock['stock_code']
                stock_name = stock['stock_name']

                # 获取K线数据
                kline_data = self.get_stock_kline_data(stock_code)
                if not kline_data or len(kline_data) < 1000:
                    continue

                # 数据预处理（包含指标计算）
                preprocessed_df = self.preprocess_kline_data(stock_code, kline_data)
                if preprocessed_df is None:
                    # 如果预处理失败，回退到原始数据格式
                    self.logger.warning(f"股票 {stock_code} 预处理失败，使用原始数据格式")
                    signals = self.execute_strategies(stock_code, stock_name, kline_data=kline_data)
                else:
                    # 使用预处理后的数据
                    signals = self.execute_strategies(stock_code, stock_name, preprocessed_df=preprocessed_df)
                
                batch_signals.extend(signals)
                self.logger.debug(f"处理股票 {stock_code} 完成，产生 {len(signals)} 个信号")

            except Exception as e:
                self.logger.error(f"处理股票 {stock.get('stock_code', 'unknown')} 失败: {e}")

        return batch_signals

    def save_signals(self, signals: List) -> bool:
        """批量保存信号到数据库"""
        if not signals:
            return True

        try:
            saved_count = 0

            for signal in signals:
                try:
                    # 检查信号类型并使用相应的保存方法
                    if hasattr(signal, 'break_t1_date') and hasattr(signal, 'break_t2_date'):
                        # 这是EnhancedSignal（双通道斐波那契信号）
                        # 保存到专用表
                        if self.strategy_db_manager.save_dual_channel_signal(signal):
                            saved_count += 1

                        # 同时保存到通用表
                        self.strategy_db_manager.save_strategy_signal(signal)

                    else:
                        # 这是普通Signal，使用通用保存方法
                        if self.strategy_db_manager.save_strategy_signal(signal):
                            saved_count += 1

                except Exception as e:
                    self.logger.error(f"保存信号失败: {getattr(signal, 'stock_code', 'unknown')} - {e}")
                    continue

            self.logger.info(f"成功保存 {saved_count}/{len(signals)} 个信号到数据库")

            # 发送飞书通知
            if self.feishu_notifier and signals:
                try:
                    self.feishu_notifier.send_strategy_signals(signals)
                except Exception as e:
                    self.logger.error(f"发送飞书通知失败: {e}")

            return saved_count > 0

        except Exception as e:
            self.logger.error(f"批量保存信号失败: {e}")
            return False



    def run(self) -> bool:
        """执行盘后策略选股"""
        start_time = time.time()
        self.logger.info("开始执行盘后策略选股")

        try:
            # 1. 初始化数据库
            if not self._init_database():
                return False

            # 2. 初始化飞书通知器
            if not self._init_feishu_notifier():
                return False

            # 3. 获取股票列表
            stocks = self.get_stock_list()
            if not stocks:
                self.logger.warning("没有获取到股票列表")
                return False

            # 4. 分批处理股票
            total_signals = []
            stock_batches = [stocks[i:i + self.batch_size] for i in range(0, len(stocks), self.batch_size)]

            self.logger.info(f"开始处理 {len(stocks)} 只股票，分为 {len(stock_batches)} 批")

            # 使用线程池并行处理
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有批次任务
                future_to_batch = {
                    executor.submit(self.process_stock_batch, batch): i
                    for i, batch in enumerate(stock_batches)
                }

                # 收集结果（增加中断处理）
                try:
                    for future in as_completed(future_to_batch, timeout=600):  # 10分钟超时
                        batch_index = future_to_batch[future]
                        try:
                            batch_signals = future.result()
                            total_signals.extend(batch_signals)
                            self.logger.info(f"批次 {batch_index + 1}/{len(stock_batches)} 完成，产生 {len(batch_signals)} 个信号")
                        except Exception as e:
                            self.logger.error(f"批次 {batch_index + 1} 处理失败: {e}")

                except KeyboardInterrupt:
                    self.logger.warning("收到中断信号，正在优雅停止...")
                    # 取消未完成的任务
                    for future in future_to_batch:
                        if not future.done():
                            future.cancel()
                            self.logger.debug(f"已取消批次 {future_to_batch[future] + 1} 的任务")

                    # 保存已完成的信号
                    if total_signals:
                        self.logger.info(f"保存已完成的 {len(total_signals)} 个信号...")
                        self.save_signals(total_signals)

                    return False  # 返回失败状态，但已保存部分结果

            # 5. 保存所有信号
            if total_signals:
                success = self.save_signals(total_signals)
                if not success:
                    return False

            # 6. 输出统计信息
            elapsed_time = time.time() - start_time
            self.logger.info(f"盘后策略选股完成！")
            self.logger.info(f"处理股票数量: {len(stocks)}")
            self.logger.info(f"产生信号数量: {len(total_signals)}")
            self.logger.info(f"执行耗时: {elapsed_time:.2f}秒")

            # 按策略统计信号
            strategy_stats = {}
            for signal in total_signals:
                strategy_name = signal.strategy_name
                if strategy_name not in strategy_stats:
                    strategy_stats[strategy_name] = 0
                strategy_stats[strategy_name] += 1

            self.logger.info("策略信号统计:")
            for strategy, count in strategy_stats.items():
                self.logger.info(f"  {strategy}: {count} 个信号")

            return True

        except Exception as e:
            self.logger.error(f"盘后策略选股执行失败: {e}")
            return False


def main():
    """主函数"""
    try:
        scheduler = AfterMarketScheduler()
        success = scheduler.run()

        if success:
            print("盘后策略选股执行成功")
            return 0
        else:
            print("盘后策略选股执行失败")
            return 1

    except Exception as e:
        print(f"程序执行异常: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
