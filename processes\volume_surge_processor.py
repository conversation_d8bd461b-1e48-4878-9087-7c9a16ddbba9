#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成交量激增处理器主进程

负责协调各个组件实现连续监控，检测开盘期100倍和盘中期10倍的成交量激增。

功能特点：
1. 连续监控循环
2. 组件协调
3. 时间窗口管理
4. 股票列表管理
5. 信号输出
6. 性能监控
7. 异常处理

作者: QuantFM Team
创建时间: 2025-08-08
"""

import threading
import time
from datetime import datetime, time as dt_time
from typing import List, Dict, Any, Optional
import sys
import os
import signal

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger
from config.config_manager import get_config
from factors.volume_surge_factor import VolumeSurgeFactorEngine
from factors.signal_continuity_manager import SignalContinuityManager
from factors.volume_data_manager import VolumeDataManager
from services.feishu_notifier import FeishuNotifier


class VolumeSurgeProcessor:
    """成交量激增处理器主进程"""
    
    def __init__(self):
        """初始化成交量激增处理器"""
        self.logger = get_logger("VolumeSurgeProcessor")
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化组件
        self.factor_engine = VolumeSurgeFactorEngine(self.config.get("factor_config", {}))
        self.continuity_manager = SignalContinuityManager(self.config.get("signal_continuity", {}))
        self.data_manager = VolumeDataManager(self.config.get("processing", {}))
        
        # 飞书通知器
        try:
            # 尝试从配置加载飞书参数
            feishu_config = get_config("config/feishu.toml")
            webhook_url = feishu_config.get("webhook_url", "")
            secret = feishu_config.get("secret", "")

            if webhook_url and secret:
                self.feishu_notifier = FeishuNotifier(webhook_url, secret, self.logger)
            else:
                self.logger.warning("飞书配置不完整，禁用飞书通知")
                self.feishu_notifier = None
        except Exception as e:
            self.logger.warning(f"加载飞书配置失败: {e}，禁用飞书通知")
            self.feishu_notifier = None
        
        # 股票列表
        self.stock_list = []
        
        # 运行状态
        self.is_running = False
        self.monitoring_thread = None
        self.stop_event = threading.Event()
        
        # 时间窗口配置
        time_windows = self.config.get("time_windows", {})
        self.preheat_time = self._parse_time(time_windows.get("preheat_time", "09:27:00"))
        self.opening_start = self._parse_time(time_windows.get("opening_start", "09:30:00"))
        self.opening_end = self._parse_time(time_windows.get("opening_end", "09:45:00"))
        self.intraday_start = self._parse_time(time_windows.get("intraday_start", "09:45:00"))
        self.intraday_end = self._parse_time(time_windows.get("intraday_end", "15:00:00"))
        
        # 处理配置
        processing_config = self.config.get("processing", {})
        self.continuous_monitoring = processing_config.get("continuous_monitoring", True)
        self.max_concurrent_stocks = processing_config.get("max_concurrent_stocks", 200)
        self.event_driven = processing_config.get("event_driven", True)
        
        # 性能监控
        self.performance_config = self.config.get("performance", {})
        self.enable_monitoring = self.performance_config.get("enable_monitoring", True)
        self.target_latency_ms = self.performance_config.get("target_latency_ms", 500)
        
        # 统计信息
        self.stats = {
            'start_time': None,
            'signals_generated': 0,
            'data_events_processed': 0,
            'errors_count': 0,
            'last_processing_time': 0,
            'avg_processing_time': 0
        }
        
        self.logger.info("成交量激增处理器初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            config = get_config("config/volume_surge_processor.toml")
            self.logger.info("成功加载配置文件")
            return config
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            # 返回默认配置
            return {
                "factor_config": {
                    "opening_ratio_threshold": 100,
                    "intraday_ratio_threshold": 10,
                    "historical_days": 10
                },
                "time_windows": {
                    "preheat_time": "09:27:00",
                    "opening_start": "09:30:00",
                    "opening_end": "09:45:00",
                    "intraday_start": "09:45:00",
                    "intraday_end": "15:00:00"
                },
                "processing": {
                    "continuous_monitoring": True,
                    "max_concurrent_stocks": 200,
                    "event_driven": True
                },
                "signal_continuity": {
                    "enable_continuity_check": True,
                    "max_signal_gap_seconds": 300,
                    "reset_on_discontinuity": True
                },
                "performance": {
                    "enable_monitoring": True,
                    "target_latency_ms": 500
                }
            }
    
    def _parse_time(self, time_str: str) -> dt_time:
        """解析时间字符串"""
        try:
            hour, minute, second = map(int, time_str.split(':'))
            return dt_time(hour, minute, second)
        except Exception as e:
            self.logger.error(f"解析时间字符串失败: {time_str}, 错误: {e}")
            return dt_time(9, 30, 0)  # 默认时间
    
    def _load_stock_list(self) -> List[str]:
        """加载股票列表"""
        try:
            # 尝试从配置文件加载
            try:
                stock_config = get_config("config/stock_list.toml")
                stock_list = []
                
                # 从配置中提取股票代码
                for category in stock_config.values():
                    if isinstance(category, dict) and 'stocks' in category:
                        for stock in category['stocks']:
                            if isinstance(stock, dict) and 'code' in stock:
                                stock_list.append(stock['code'])
                
                if stock_list:
                    self.logger.info(f"从配置文件加载了 {len(stock_list)} 只股票")
                    return stock_list
                    
            except Exception as e:
                self.logger.warning(f"从配置文件加载股票列表失败: {e}")
            
            # 从数据库加载
            from data.db_manager import get_db_manager
            db_manager = get_db_manager()
            
            query = "SELECT stock_code FROM stock_info WHERE is_active = true ORDER BY stock_code"
            result = db_manager.fetch_all(query)
            
            stock_list = [row[0] for row in result]
            self.logger.info(f"从数据库加载了 {len(stock_list)} 只股票")
            return stock_list
            
        except Exception as e:
            self.logger.error(f"加载股票列表失败: {e}")
            return []
    
    def start(self):
        """启动处理器"""
        if self.is_running:
            self.logger.warning("处理器已在运行中")
            return
        
        try:
            self.logger.info("启动成交量激增处理器")
            
            # 加载股票列表
            self.stock_list = self._load_stock_list()
            if not self.stock_list:
                raise Exception("无法加载股票列表")
            
            # 初始化数据管理器
            self.data_manager.initialize(self.stock_list)
            
            # 设置运行状态
            self.is_running = True
            self.stop_event.clear()
            self.stats['start_time'] = datetime.now()
            
            # 启动监控线程
            if self.continuous_monitoring:
                self.monitoring_thread = threading.Thread(target=self._continuous_monitoring_loop, daemon=True)
                self.monitoring_thread.start()
                self.logger.info("启动连续监控线程")
            
            # 发送启动通知
            if self.feishu_notifier:
                try:
                    self.feishu_notifier.send_startup_notification("成交量激增处理器", {
                        'stock_count': len(self.stock_list),
                        'opening_threshold': self.factor_engine.opening_threshold,
                        'intraday_threshold': self.factor_engine.intraday_threshold
                    })
                except Exception as e:
                    self.logger.error(f"发送启动通知失败: {e}")
            
            self.logger.info("成交量激增处理器启动完成")
            
        except Exception as e:
            self.logger.error(f"启动处理器失败: {e}")
            self.is_running = False
            raise
    
    def stop(self):
        """停止处理器"""
        if not self.is_running:
            return
        
        self.logger.info("停止成交量激增处理器")
        
        # 设置停止标志
        self.is_running = False
        self.stop_event.set()
        
        # 等待监控线程结束
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=10)
        
        # 停止数据管理器
        self.data_manager.stop_continuous_monitoring()
        
        # 发送停止通知
        if self.feishu_notifier:
            try:
                runtime = datetime.now() - self.stats['start_time'] if self.stats['start_time'] else None
                self.feishu_notifier.send_shutdown_notification("成交量激增处理器", {
                    'runtime': str(runtime) if runtime else "未知",
                    'signals_generated': self.stats['signals_generated'],
                    'data_events_processed': self.stats['data_events_processed']
                })
            except Exception as e:
                self.logger.error(f"发送停止通知失败: {e}")
        
        self.logger.info("成交量激增处理器已停止")
    
    def _continuous_monitoring_loop(self):
        """连续监控主循环"""
        self.logger.info("连续监控循环开始")
        
        # 启动数据管理器的连续监控
        self.data_manager.start_continuous_monitoring()
        
        while self.is_running and not self.stop_event.is_set():
            try:
                current_time = datetime.now()
                
                # 检查是否在交易时间内
                if not self._is_trading_time(current_time.time()):
                    time.sleep(60)  # 非交易时间，等待1分钟
                    continue
                
                # 处理数据事件
                self._process_current_data_events(current_time)
                
                # 短暂休眠，避免过度占用CPU
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"连续监控循环异常: {e}")
                self.stats['errors_count'] += 1
                time.sleep(5)  # 异常时等待5秒
        
        self.logger.info("连续监控循环结束")

    def _process_current_data_events(self, current_time: datetime):
        """处理当前数据事件"""
        try:
            start_time = time.time()

            current_time_obj = current_time.time()

            if self._is_opening_period(current_time_obj):
                # 开盘期处理
                self._process_opening_period(current_time)
            elif self._is_intraday_period(current_time_obj):
                # 盘中期处理
                self._process_intraday_period(current_time)

            # 记录处理时间
            processing_time = (time.time() - start_time) * 1000  # 转换为毫秒
            self.stats['last_processing_time'] = processing_time

            # 更新平均处理时间
            if self.stats['data_events_processed'] > 0:
                self.stats['avg_processing_time'] = (
                    (self.stats['avg_processing_time'] * self.stats['data_events_processed'] + processing_time) /
                    (self.stats['data_events_processed'] + 1)
                )
            else:
                self.stats['avg_processing_time'] = processing_time

            self.stats['data_events_processed'] += 1

            # 性能监控
            if self.enable_monitoring and processing_time > self.target_latency_ms:
                self.logger.warning(f"处理延迟超过目标: {processing_time:.2f}ms > {self.target_latency_ms}ms")

        except Exception as e:
            self.logger.error(f"处理数据事件失败: {e}")
            self.stats['errors_count'] += 1

    def _process_opening_period(self, current_time: datetime):
        """处理开盘期"""
        try:
            # 批量获取当前成交量
            stock_volumes = self.data_manager.batch_get_current_volumes(self.stock_list)

            # 获取历史平均数据
            historical_data = {}
            for stock_code in self.stock_list:
                historical_data[stock_code] = self.data_manager.get_opening_historical_avg(stock_code)

            # 检测激增信号
            signals = self.factor_engine.opening_surge_detection(stock_volumes, historical_data)

            # 处理信号
            self._process_signals(signals, current_time)

        except Exception as e:
            self.logger.error(f"处理开盘期失败: {e}")

    def _process_intraday_period(self, current_time: datetime):
        """处理盘中期"""
        try:
            # 获取当前K线数据
            kline_data = {}
            historical_data = {}

            for stock_code in self.stock_list:
                kline = self.data_manager.get_current_kline(stock_code)
                if kline:
                    kline_data[stock_code] = kline
                    historical_data[stock_code] = self.data_manager.get_intraday_avg(stock_code)

            # 检测激增信号
            if kline_data:
                signals = self.factor_engine.intraday_surge_detection(kline_data, historical_data)

                # 处理信号
                self._process_signals(signals, current_time)

        except Exception as e:
            self.logger.error(f"处理盘中期失败: {e}")

    def _process_signals(self, signals: List, current_time: datetime):
        """处理信号"""
        try:
            for signal in signals:
                # 检查信号连续性
                is_allowed = self.continuity_manager.check_signal_continuity(
                    signal.stock_code,
                    signal.signal_type,
                    current_time
                )

                if is_allowed:
                    # 更新连续次数
                    continuous_count = self.continuity_manager.get_continuous_count(signal.stock_code)
                    signal.continuous_count = continuous_count

                    # 发送信号通知
                    self._send_signal_notification(signal)

                    self.stats['signals_generated'] += 1

                    self.logger.info(f"处理信号: {signal.stock_code}, 类型: {signal.signal_type}, "
                                   f"比值: {signal.surge_ratio:.2f}x, 连续: {continuous_count}")
                else:
                    self.logger.debug(f"信号被连续性管理器阻止: {signal.stock_code}")

        except Exception as e:
            self.logger.error(f"处理信号失败: {e}")

    def _send_signal_notification(self, signal):
        """发送信号通知"""
        try:
            if self.feishu_notifier:
                # 构造通知数据
                notification_data = {
                    'stock_code': signal.stock_code,
                    'signal_type': signal.signal_type,
                    'surge_ratio': signal.surge_ratio,
                    'current_volume': signal.current_volume,
                    'historical_avg_volume': signal.historical_avg_volume,
                    'confidence': signal.confidence,
                    'continuous_count': signal.continuous_count,
                    'timestamp': signal.timestamp,
                    'period_info': signal.period_info
                }

                self.feishu_notifier.send_volume_surge_alert(notification_data)

        except Exception as e:
            self.logger.error(f"发送信号通知失败: {e}")

    def _is_trading_time(self, current_time) -> bool:
        """检查是否为交易时间"""
        return (self.preheat_time <= current_time <= self.intraday_end)

    def _is_opening_period(self, current_time) -> bool:
        """判断是否为开盘期"""
        return self.opening_start <= current_time < self.opening_end

    def _is_intraday_period(self, current_time) -> bool:
        """判断是否为盘中期"""
        return self.intraday_start <= current_time < self.intraday_end

    def get_status(self) -> Dict[str, Any]:
        """获取处理器状态"""
        current_time = datetime.now()
        runtime = current_time - self.stats['start_time'] if self.stats['start_time'] else None

        return {
            'is_running': self.is_running,
            'start_time': self.stats['start_time'],
            'runtime': str(runtime) if runtime else None,
            'stock_count': len(self.stock_list),
            'signals_generated': self.stats['signals_generated'],
            'data_events_processed': self.stats['data_events_processed'],
            'errors_count': self.stats['errors_count'],
            'last_processing_time_ms': self.stats['last_processing_time'],
            'avg_processing_time_ms': self.stats['avg_processing_time'],
            'current_period': self._get_current_period_name(current_time.time()),
            'factor_engine_stats': self.factor_engine.get_statistics(),
            'continuity_manager_stats': self.continuity_manager.get_statistics(),
            'data_manager_stats': self.data_manager.get_cache_stats()
        }

    def _get_current_period_name(self, current_time) -> str:
        """获取当前时期名称"""
        if self._is_opening_period(current_time):
            return "开盘期"
        elif self._is_intraday_period(current_time):
            return "盘中期"
        else:
            return "非交易时间"

    def force_process_stock(self, stock_code: str) -> Dict[str, Any]:
        """强制处理指定股票（用于调试）"""
        try:
            current_time = datetime.now()
            current_time_obj = current_time.time()

            result = {
                'stock_code': stock_code,
                'timestamp': current_time,
                'period': self._get_current_period_name(current_time_obj),
                'signals': []
            }

            if self._is_opening_period(current_time_obj):
                # 开盘期处理
                current_volume = self.data_manager.get_current_volume(stock_code)
                historical_avg = self.data_manager.get_opening_historical_avg(stock_code)

                if current_volume > 0 and historical_avg > 0:
                    signals = self.factor_engine.opening_surge_detection(
                        {stock_code: current_volume},
                        {stock_code: historical_avg}
                    )
                    result['signals'] = [signal.to_dict() for signal in signals]
                    result['current_volume'] = current_volume
                    result['historical_avg'] = historical_avg
                    result['ratio'] = current_volume / historical_avg

            elif self._is_intraday_period(current_time_obj):
                # 盘中期处理
                kline = self.data_manager.get_current_kline(stock_code)
                historical_avg = self.data_manager.get_intraday_avg(stock_code)

                if kline and historical_avg > 0:
                    signals = self.factor_engine.intraday_surge_detection(
                        {stock_code: kline},
                        {stock_code: historical_avg}
                    )
                    result['signals'] = [signal.to_dict() for signal in signals]
                    result['kline'] = kline
                    result['historical_avg'] = historical_avg
                    result['ratio'] = kline['volume'] / historical_avg

            return result

        except Exception as e:
            self.logger.error(f"强制处理股票失败 {stock_code}: {e}")
            return {'error': str(e)}


def main():
    """主函数"""
    processor = VolumeSurgeProcessor()

    def signal_handler(signum, frame):
        """信号处理器"""
        processor.logger.info(f"接收到信号 {signum}，正在停止处理器...")
        processor.stop()
        sys.exit(0)

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        processor.start()

        # 保持主线程运行
        while processor.is_running:
            time.sleep(1)

    except KeyboardInterrupt:
        processor.logger.info("接收到键盘中断，正在停止...")
    except Exception as e:
        processor.logger.error(f"处理器运行异常: {e}")
    finally:
        processor.stop()


if __name__ == "__main__":
    main()
