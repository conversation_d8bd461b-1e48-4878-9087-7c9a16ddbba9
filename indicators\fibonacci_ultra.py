"""
斐波那契技术分析通用工具模块

提供基于斐波那契数列的通用技术分析工具。
主要功能:

1. 回调位策略 - 基于斐波那契回调位的支撑和阻力
2. 扩展位策略 - 基于斐波那契扩展位的目标价格
3. 支撑阻力策略 - 识别关键支撑和阻力位
4. 时间区策略 - 基于斐波那契时间区的时间分析
5. 扇形线策略 - 基于斐波那契扇形线的趋势分析
6. 弧线策略 - 基于斐波那契弧线的价格运动分析
7. 综合分析 - 斐波那契分析通用函数
8. 位置分析 - 价格位置分析辅助函数
9. 信号生成 - 交易建议生成函数

Author: Xzh
Date: 2025-06-18
"""


from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from utils.logger import get_logger

logger = get_logger("fibonacci_ultra")

# 回调位策略
def calculate_fibonacci_retracement(
    start_price: float,
    end_price: float,
    levels: Optional[List[float]] = None,
    prefix: str = 'fib_',
    include_full_range: bool = True,
    trend: Optional[str] = None,
    precision: int = 4
) -> Dict[float, float]:
    """
    通用斐波那契回调位计算函数 - 优化版

    Args:
        start_price: 起始价格
        end_price: 结束价格
        levels: 回调位水平列表，默认为常用水平
        prefix: 输出列前缀
        include_full_range: 是否包含0.0和1.0级别
        trend: 价格趋势，可选值为"up"或"down"，如果为None则自动计算
        precision: 价格精度，小数点位数

    Returns:
        回调位水平到价格的字典
    """
    # 设置默认回调位水平
    if levels is None:
        if include_full_range:
            levels = [0.0, 0.236, 0.382, 0.5, 0.618, 0.786, 1.0]
        else:
            levels = [0.236, 0.382, 0.5, 0.618, 0.786]
    elif include_full_range and not (0.0 in levels and 1.0 in levels):
        # 确保包含完整范围时，0.0和1.0在levels中
        if 0.0 not in levels:
            levels = [0.0] + levels
        if 1.0 not in levels:
            levels = levels + [1.0]

    # 确定趋势方向
    if trend is None:
        trend = "up" if end_price > start_price else "down"

    # 计算价格差
    price_diff = abs(end_price - start_price)

    # 计算各个回调位水平对应的价格
    retracement_prices = {}

    if trend == "up":
        # 上升趋势：从低到高，回调位从高向低
        for level in levels:
            price = round(end_price - price_diff * level, precision)
            retracement_prices[level] = price
    else:
        # 下降趋势：从高到低，回调位从低向高
        for level in levels:
            price = round(start_price + price_diff * level, precision)
            retracement_prices[level] = price

    return retracement_prices

# 扩展位策略
def calculate_fibonacci_extension(
    start_price: float,
    end_price: float,
    retracement_price: Optional[float] = None,
    levels: Optional[List[float]] = None,
    prefix: str = 'fib_ext_',
    trend: Optional[str] = None,
    precision: int = 4
) -> Dict[float, float]:
    """
    通用斐波那契扩展位计算函数 - 优化版

    Args:
        start_price: 起始价格
        end_price: 中间价格（回调起点）
        retracement_price: 回调价格，如果为None则只计算简单扩展
        levels: 扩展位水平列表，默认为常用水平
        prefix: 输出列前缀
        trend: 价格趋势，可选值为"up"或"down"，如果为None则自动计算
        precision: 价格精度，小数点位数

    Returns:
        扩展位水平到价格的字典
    """
    # 设置默认扩展位水平
    if levels is None:
        levels = [1.0, 1.272, 1.382, 1.618, 2.0, 2.618, 3.618, 4.236]

    # 确定趋势方向
    if trend is None:
        trend = "up" if end_price > start_price else "down"

    # 计算价格差
    ab_diff = abs(end_price - start_price)

    # 计算各个扩展位水平对应的价格
    extension_prices = {}

    if retracement_price is None:
        # 简单扩展（不考虑回调）
        if trend == "up":
            # 上升趋势：从低到高，扩展位继续向上
            for level in levels:
                price = round(end_price + ab_diff * (level - 1.0), precision)
                extension_prices[level] = price
        else:
            # 下降趋势：从高到低，扩展位继续向下
            for level in levels:
                price = round(end_price - ab_diff * (level - 1.0), precision)
                extension_prices[level] = price
    else:
        # 完整的回调后扩展（ABC模式）
        bc_diff = abs(retracement_price - end_price)

        if trend == "up":
            # 上升趋势：从低到高，回调后扩展
            for level in levels:
                price = round(retracement_price + bc_diff * level, precision)
                extension_prices[level] = price
        else:
            # 下降趋势：从高到低，回调后扩展
            for level in levels:
                price = round(retracement_price - bc_diff * level, precision)
                extension_prices[level] = price

    return extension_prices

# 支撑阻力策略
def identify_support_resistance(
    current_price: float,
    fibonacci_levels: Dict[float, float],
    threshold_percent: float = 1.0,
    max_levels: int = 3
) -> Dict[str, Any]:
    """
    基于斐波那契水平识别支撑位和阻力位 - 优化版

    Args:
        current_price: 当前价格
        fibonacci_levels: 斐波那契水平字典
        threshold_percent: 距离当前价格的阈值百分比
        max_levels: 返回的最大支撑/阻力位数量

    Returns:
        包含支撑位和阻力位信息的字典
    """
    support_levels = []
    resistance_levels = []

    for level, price in fibonacci_levels.items():
        # 计算距离百分比
        distance_percent = abs(price - current_price) / current_price * 100
        level_info = {
            'level': level,
            'price': price,
            'distance_percent': distance_percent,
            'distance': abs(price - current_price)
        }

        # 分类为支撑位或阻力位
        if price < current_price:
            support_levels.append(level_info)
        elif price > current_price:
            resistance_levels.append(level_info)

    # 按距离排序
    support_levels.sort(key=lambda x: x['distance_percent'])
    resistance_levels.sort(key=lambda x: x['distance_percent'])

    # 筛选距离在阈值内的水平
    valid_supports = [level for level in support_levels if level['distance_percent'] <= threshold_percent]
    valid_resistances = [level for level in resistance_levels if level['distance_percent'] <= threshold_percent]

    # 限制返回的水平数量
    valid_supports = valid_supports[:max_levels]
    valid_resistances = valid_resistances[:max_levels]

    return {
        'support_levels': valid_supports,
        'resistance_levels': valid_resistances,
        'nearest_support': valid_supports[0] if valid_supports else None,
        'nearest_resistance': valid_resistances[0] if valid_resistances else None,
        'has_nearby_support': len(valid_supports) > 0,
        'has_nearby_resistance': len(valid_resistances) > 0
    }

# 时间区策略
def calculate_fibonacci_time_zones(
    start_time: datetime,
    end_time: Optional[datetime] = None,
    levels: Optional[List[int]] = None,
    time_unit: str = 'D',
    max_periods: int = 89
) -> Dict[int, datetime]:
    """
    通用斐波那契时间区计算函数 - 优化版

    Args:
        start_time: 起始时间
        end_time: 结束时间，如果为None则使用start_time
        levels: 斐波那契数列水平，默认使用经典斐波那契数列
        time_unit: 时间单位 ('D'=天, 'W'=周, 'M'=月, 'H'=小时)
        max_periods: 最大周期数

    Returns:
        斐波那契数列值到时间戳的字典
    """
    # 设置默认的斐波那契数列
    if levels is None:
        levels = [1, 2, 3, 5, 8, 13, 21, 34, 55, 89]
        # 限制最大周期
        levels = [level for level in levels if level <= max_periods]

    # 如果没有提供end_time，使用start_time
    if end_time is None:
        end_time = start_time

    time_zones = {}

    for level in levels:
        if time_unit == 'D':
            time_zones[level] = end_time + timedelta(days=level)
        elif time_unit == 'W':
            time_zones[level] = end_time + timedelta(weeks=level)
        elif time_unit == 'H':
            time_zones[level] = end_time + timedelta(hours=level)
        elif time_unit == 'M':
            # 月份计算需要特殊处理
            year = end_time.year
            month = end_time.month + level
            day = min(end_time.day, 28)  # 避免月份天数问题

            # 处理月份溢出
            years_to_add = (month - 1) // 12
            month = ((month - 1) % 12) + 1
            year += years_to_add

            time_zones[level] = datetime(year, month, day)

    return time_zones

# 新增：价格位置分析函数
def analyze_price_position(
    current_price: float,
    fibonacci_levels: Dict[float, float],
    tolerance_percent: float = 0.5
) -> Dict[str, Any]:
    """
    分析当前价格相对于斐波那契水平的位置

    Args:
        current_price: 当前价格
        fibonacci_levels: 斐波那契水平字典
        tolerance_percent: 价格接近斐波那契水平的容差百分比

    Returns:
        价格位置分析结果
    """
    # 将水平按价格排序
    sorted_levels = sorted([(level, price) for level, price in fibonacci_levels.items()],
                           key=lambda x: x[1])

    # 找到当前价格所在的区间
    current_zone = None
    for i in range(len(sorted_levels) - 1):
        lower_level, lower_price = sorted_levels[i]
        upper_level, upper_price = sorted_levels[i + 1]

        if lower_price <= current_price <= upper_price:
            current_zone = {
                'lower_level': lower_level,
                'lower_price': lower_price,
                'upper_level': upper_level,
                'upper_price': upper_price,
                'zone_size': upper_price - lower_price,
                'position_in_zone': (current_price - lower_price) / (upper_price - lower_price)
            }
            break

    # 检查价格是否接近任何斐波那契水平
    near_levels = []
    for level, price in fibonacci_levels.items():
        distance_percent = abs(price - current_price) / current_price * 100
        if distance_percent <= tolerance_percent:
            near_levels.append({
                'level': level,
                'price': price,
                'distance_percent': distance_percent
            })

    # 排序接近的水平
    near_levels.sort(key=lambda x: x['distance_percent'])

    return {
        'current_price': current_price,
        'current_zone': current_zone,
        'near_levels': near_levels,
        'is_near_level': len(near_levels) > 0,
        'nearest_level': near_levels[0] if near_levels else None
    }


def calculate_fibonacci_extension_with_second_peak(
    low_price: float,
    second_high_price: float,
    levels: Optional[List[float]] = None,
    precision: int = 4
) -> Dict[float, float]:
    """
    基于倒数第二个高点计算斐波那契扩展位

    这个函数专门用于双通道突破策略，使用scipy找到的倒数第二个高点
    作为计算基准，提供更准确的扩展位目标。

    参数:
        low_price: 低点价格（通常是突破前的最低点）
        second_high_price: 倒数第二个高点价格（由scipy分析得出）
        levels: 扩展位水平列表，默认为[1.272, 1.382, 1.618]
        precision: 价格精度，小数点位数

    返回:
        扩展位字典 {level: price}
    """
    try:
        if levels is None:
            levels = [1.272, 1.382, 1.618]

        if low_price <= 0 or second_high_price <= 0:
            logger.error("价格必须为正数")
            return {}

        if second_high_price <= low_price:
            logger.error("倒数第二个高点价格必须高于低点价格")
            return {}

        # 计算价格差
        price_diff = second_high_price - low_price

        # 计算各个扩展位
        extension_levels = {}
        for level in levels:
            try:
                # 扩展位 = 倒数第二高点 + (价格差 * 扩展比例)
                extension_price = second_high_price + (price_diff * (level - 1.0))
                extension_levels[level] = round(extension_price, precision)

                logger.debug(f"扩展位 {level}: {extension_price:.{precision}f}")

            except Exception as e:
                logger.warning(f"计算扩展位 {level} 时出错: {e}")
                extension_levels[level] = None

        logger.info(f"基于倒数第二高点计算扩展位完成: 低点={low_price}, 第二高点={second_high_price}")

        return extension_levels

    except Exception as e:
        logger.error(f"计算基于倒数第二高点的斐波那契扩展位失败: {e}")
        return {}


def calculate_fibonacci_retracement_with_second_peak(
    low_price: float,
    second_high_price: float,
    levels: Optional[List[float]] = None,
    precision: int = 4
) -> Dict[float, float]:
    """
    基于倒数第二个高点计算斐波那契回调位

    参数:
        low_price: 低点价格
        second_high_price: 倒数第二个高点价格
        levels: 回调位水平列表，默认为[0.236, 0.382, 0.5, 0.618, 0.786]
        precision: 价格精度

    返回:
        回调位字典 {level: price}
    """
    try:
        if levels is None:
            levels = [0.236, 0.382, 0.5, 0.618, 0.786]

        # 使用现有的回调位计算函数
        return calculate_fibonacci_retracement(
            start_price=low_price,
            end_price=second_high_price,
            levels=levels,
            precision=precision
        )

    except Exception as e:
        logger.error(f"基于倒数第二高点计算回调位失败: {e}")
        return {}
