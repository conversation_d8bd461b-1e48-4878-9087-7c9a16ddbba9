#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
默认配置参数

定义背离因子的默认配置参数和快速配置模板。

作者: QuantFM Team
创建时间: 2025-08-11
"""

# 默认配置
DEFAULT_CONFIG = {
    # 极值检测配置
    'extreme_detection': {
        'base_distance': 5,                    # 基础距离参数
        'base_prominence_ratio': 0.02,         # 基础突出度比率
        'min_strength_threshold': 0.3,         # 最小强度阈值
        'adaptive_volatility_factor': 1.5,     # 自适应波动率因子
        'max_time_gap': 20,                    # 最大时间间隔
        'completion_probability_threshold': 0.6 # 完成概率阈值
    },
    
    # 指标配置
    'indicators': {
        'macd': {
            'fast_period': 12,
            'slow_period': 26,
            'signal_period': 9,
            'zero_line_significance': 1.2,
            'histogram_threshold': 0.1,
            'cross_confirmation_periods': 3
        },
        'rsi': {
            'period': 14,
            'overbought_threshold': 70,
            'oversold_threshold': 30,
            'centerline': 50,
            'extreme_zone_weight': 1.5,
            'middle_zone_weight': 0.8
        },
        'kdj': {
            'k_period': 9,
            'd_period': 3,
            'j_period': 3,
            'overbought_threshold': 80,
            'oversold_threshold': 20,
            'cross_confirmation_periods': 2,
            'j_line_sensitivity': 1.2
        },
        'cci': {
            'period': 14,
            'upper_threshold': 100,
            'lower_threshold': -100,
            'extreme_upper': 200,
            'extreme_lower': -200,
            'normal_zone_weight': 0.7,
            'extreme_zone_weight': 1.3
        },
        'obv': {
            'smoothing_window': 5,
            'trend_line_periods': 20,
            'accumulation_threshold': 0.6,
            'distribution_threshold': -0.6,
            'volume_price_correlation_window': 15
        }
    },
    
    # 背离分析配置
    'divergence_analysis': {
        'min_divergence_strength': 0.3,        # 最小背离强度
        'confirmation_threshold': 0.6,         # 确认阈值
        'time_weight_factor': 0.8,            # 时间权重因子
        'volume_weight_factor': 0.2,          # 成交量权重因子
        'max_time_gap': 20,                   # 最大时间间隔
        'pattern_reliability_threshold': 0.5   # 模式可靠性阈值
    },
    
    # 信号生成配置
    'signal_generation': {
        'min_actionable_strength': 0.5,        # 最小可操作强度
        'stop_loss_atr_multiplier': 2.0,       # 止损ATR倍数
        'take_profit_atr_multiplier': 3.0,     # 止盈ATR倍数
        'risk_reward_min_ratio': 1.5,          # 最小风险收益比
        'signal_decay_half_life': 10           # 信号衰减半衰期
    },
    
    # 自适应参数配置
    'adaptive_params': {
        'volatility_sensitivity': 1.5,         # 波动率敏感度
        'min_distance': 2,                     # 最小距离
        'max_distance': 20,                    # 最大距离
        'min_prominence_ratio': 0.005,         # 最小突出度比率
        'max_prominence_ratio': 0.1,           # 最大突出度比率
        'trending_threshold': 0.7,             # 趋势阈值
        'volatile_threshold': 2.0              # 波动阈值
    },
    
    # 实时分析配置
    'realtime': {
        'enable_forming_detection': True,      # 启用形成中检测
        'completion_probability_threshold': 0.8, # 完成概率阈值
        'update_frequency': 1,                 # 更新频率（秒）
        'max_forming_patterns': 5,            # 最大形成中模式数
        'early_warning_threshold': 0.7        # 早期预警阈值
    }
}

# 快速配置模板
QUICK_CONFIGS = {
    # 保守配置：高阈值，低敏感度
    'conservative': {
        'extreme_detection': {
            'base_distance': 7,
            'base_prominence_ratio': 0.03,
            'min_strength_threshold': 0.5,
            'adaptive_volatility_factor': 1.2
        },
        'divergence_analysis': {
            'min_divergence_strength': 0.5,
            'confirmation_threshold': 0.8,
            'time_weight_factor': 0.9,
            'volume_weight_factor': 0.3
        },
        'signal_generation': {
            'min_actionable_strength': 0.7,
            'stop_loss_atr_multiplier': 2.5,
            'take_profit_atr_multiplier': 4.0,
            'risk_reward_min_ratio': 2.0
        },
        'indicators': {
            'rsi': {
                'overbought_threshold': 75,
                'oversold_threshold': 25,
                'extreme_zone_weight': 1.8
            },
            'kdj': {
                'overbought_threshold': 85,
                'oversold_threshold': 15,
                'cross_confirmation_periods': 3
            }
        }
    },
    
    # 平衡配置：中等阈值和敏感度
    'balanced': {
        'extreme_detection': {
            'base_distance': 5,
            'base_prominence_ratio': 0.02,
            'min_strength_threshold': 0.3,
            'adaptive_volatility_factor': 1.5
        },
        'divergence_analysis': {
            'min_divergence_strength': 0.3,
            'confirmation_threshold': 0.6,
            'time_weight_factor': 0.8,
            'volume_weight_factor': 0.2
        },
        'signal_generation': {
            'min_actionable_strength': 0.5,
            'stop_loss_atr_multiplier': 2.0,
            'take_profit_atr_multiplier': 3.0,
            'risk_reward_min_ratio': 1.5
        }
    },
    
    # 激进配置：低阈值，高敏感度
    'aggressive': {
        'extreme_detection': {
            'base_distance': 3,
            'base_prominence_ratio': 0.015,
            'min_strength_threshold': 0.2,
            'adaptive_volatility_factor': 2.0
        },
        'divergence_analysis': {
            'min_divergence_strength': 0.2,
            'confirmation_threshold': 0.4,
            'time_weight_factor': 0.7,
            'volume_weight_factor': 0.15
        },
        'signal_generation': {
            'min_actionable_strength': 0.3,
            'stop_loss_atr_multiplier': 1.5,
            'take_profit_atr_multiplier': 2.5,
            'risk_reward_min_ratio': 1.2
        },
        'indicators': {
            'rsi': {
                'overbought_threshold': 65,
                'oversold_threshold': 35,
                'extreme_zone_weight': 1.2
            },
            'kdj': {
                'overbought_threshold': 75,
                'oversold_threshold': 25,
                'cross_confirmation_periods': 1,
                'j_line_sensitivity': 1.5
            }
        },
        'realtime': {
            'completion_probability_threshold': 0.6,
            'early_warning_threshold': 0.5
        }
    }
}

# 指标权重配置
INDICATOR_WEIGHTS = {
    'default': {
        'macd': 0.25,
        'rsi': 0.25,
        'kdj': 0.2,
        'cci': 0.15,
        'obv': 0.15
    },
    'trend_focused': {
        'macd': 0.35,
        'rsi': 0.2,
        'kdj': 0.15,
        'cci': 0.15,
        'obv': 0.15
    },
    'momentum_focused': {
        'macd': 0.2,
        'rsi': 0.35,
        'kdj': 0.25,
        'cci': 0.1,
        'obv': 0.1
    },
    'volume_focused': {
        'macd': 0.2,
        'rsi': 0.2,
        'kdj': 0.15,
        'cci': 0.15,
        'obv': 0.3
    }
}

# 性能配置
PERFORMANCE_CONFIG = {
    'cache_enabled': True,
    'cache_max_size': 1000,
    'parallel_processing': True,
    'max_workers': 4,
    'batch_size': 100,
    'memory_limit_mb': 512
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'enable_file_logging': True,
    'log_file': 'divergence_factor.log',
    'max_file_size_mb': 10,
    'backup_count': 5
}
