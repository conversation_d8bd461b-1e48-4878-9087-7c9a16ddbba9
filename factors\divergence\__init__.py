#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
背离因子包

一个全面的技术指标背离分析系统，包括MACD、RSI、KDJ、CCI和OBV。
提供实时信号检测，具有自适应参数调整和市场状态感知功能。

功能特性:
- 多指标背离检测
- 实时信号处理，支持形成中模式检测
- 基于市场波动率的自适应参数调整
- 市场状态感知的信号权重分配
- 全面的模式分析，包含完成概率计算

作者: QuantFM Team
创建时间: 2025-08-11
版本: 1.0.0
"""

from .core.signal_processor import DivergenceFactor
from .config.default_params import DEFAULT_CONFIG, QUICK_CONFIGS

__version__ = "1.0.0"
__author__ = "QuantFM Team"

__all__ = [
    'DivergenceFactor',
    'DEFAULT_CONFIG',
    'QUICK_CONFIGS'
]

# 包元数据
PACKAGE_INFO = {
    'name': 'divergence_factor',
    'version': __version__,
    'description': '技术指标背离分析的综合系统',
    'author': __author__,
    'supported_indicators': ['MACD', 'RSI', 'KDJ', 'CCI', 'OBV'],
    'features': [
        '实时信号检测',
        '自适应参数调整',
        '市场状态感知',
        '模式完成概率',
        '多时间框架分析'
    ]
}
