#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证工具

提供数据验证、清洗和质量检查功能。

作者: QuantFM Team
创建时间: 2025-08-11
"""

import numpy as np
from typing import Dict, Any, List, Union, Optional
import logging

logger = logging.getLogger(__name__)


def validate_input_data(prices: np.ndarray, volumes: np.ndarray, 
                       high: Optional[np.ndarray] = None, 
                       low: Optional[np.ndarray] = None) -> Dict[str, Any]:
    """验证输入数据
    
    Args:
        prices: 价格序列
        volumes: 成交量序列
        high: 最高价序列
        low: 最低价序列
        
    Returns:
        验证结果字典
    """
    errors = []
    warnings = []
    
    try:
        # 基本数据检查
        if len(prices) == 0:
            errors.append("价格数据为空")
        if len(volumes) == 0:
            errors.append("成交量数据为空")
        
        # 长度一致性检查
        if len(prices) != len(volumes):
            errors.append(f"价格和成交量数据长度不一致: {len(prices)} vs {len(volumes)}")
        
        if high is not None and len(high) != len(prices):
            errors.append(f"最高价数据长度不一致: {len(high)} vs {len(prices)}")
        
        if low is not None and len(low) != len(prices):
            errors.append(f"最低价数据长度不一致: {len(low)} vs {len(prices)}")
        
        # 数据长度检查
        min_length = 30
        if len(prices) < min_length:
            warnings.append(f"数据长度不足，建议至少{min_length}个数据点，当前: {len(prices)}")
        
        # NaN值检查
        if np.any(np.isnan(prices)):
            errors.append("价格数据包含NaN值")
        if np.any(np.isnan(volumes)):
            errors.append("成交量数据包含NaN值")
        
        if high is not None and np.any(np.isnan(high)):
            errors.append("最高价数据包含NaN值")
        if low is not None and np.any(np.isnan(low)):
            errors.append("最低价数据包含NaN值")
        
        # 负值检查
        if np.any(prices <= 0):
            errors.append("价格数据包含非正值")
        if np.any(volumes < 0):
            errors.append("成交量数据包含负值")
        
        # OHLC逻辑一致性检查
        if high is not None and low is not None:
            if np.any(high < low):
                errors.append("存在最高价小于最低价的情况")
            if np.any(prices > high):
                errors.append("存在收盘价大于最高价的情况")
            if np.any(prices < low):
                errors.append("存在收盘价小于最低价的情况")
        
        # 异常值检查
        price_outliers = detect_price_outliers(prices)
        if price_outliers['count'] > 0:
            warnings.append(f"检测到 {price_outliers['count']} 个价格异常值")
        
        volume_outliers = detect_volume_outliers(volumes)
        if volume_outliers['count'] > 0:
            warnings.append(f"检测到 {volume_outliers['count']} 个成交量异常值")
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings,
            'data_length': len(prices),
            'has_ohlc': high is not None and low is not None
        }
        
    except Exception as e:
        logger.error(f"数据验证失败: {e}")
        return {
            'is_valid': False,
            'errors': [f"验证过程出错: {e}"],
            'warnings': [],
            'data_length': 0,
            'has_ohlc': False
        }


def clean_price_data(prices: np.ndarray, method: str = 'forward_fill') -> np.ndarray:
    """清洗价格数据
    
    Args:
        prices: 原始价格数据
        method: 清洗方法 ('forward_fill', 'interpolate', 'remove')
        
    Returns:
        清洗后的价格数据
    """
    try:
        cleaned_prices = prices.copy()
        
        # 处理NaN值
        nan_mask = np.isnan(cleaned_prices)
        if np.any(nan_mask):
            if method == 'forward_fill':
                # 前向填充
                for i in range(1, len(cleaned_prices)):
                    if nan_mask[i] and not nan_mask[i-1]:
                        cleaned_prices[i] = cleaned_prices[i-1]
            elif method == 'interpolate':
                # 线性插值
                valid_indices = np.where(~nan_mask)[0]
                if len(valid_indices) > 1:
                    cleaned_prices = np.interp(
                        np.arange(len(cleaned_prices)),
                        valid_indices,
                        cleaned_prices[valid_indices]
                    )
            elif method == 'remove':
                # 移除NaN值
                cleaned_prices = cleaned_prices[~nan_mask]
        
        # 处理非正值
        if np.any(cleaned_prices <= 0):
            logger.warning("发现非正价格值，将使用前一个有效值替换")
            for i in range(1, len(cleaned_prices)):
                if cleaned_prices[i] <= 0:
                    cleaned_prices[i] = cleaned_prices[i-1]
        
        return cleaned_prices
        
    except Exception as e:
        logger.error(f"价格数据清洗失败: {e}")
        return prices


def handle_missing_values(data: np.ndarray, strategy: str = 'forward_fill') -> np.ndarray:
    """处理缺失值
    
    Args:
        data: 输入数据
        strategy: 处理策略
        
    Returns:
        处理后的数据
    """
    try:
        if not np.any(np.isnan(data)):
            return data
        
        cleaned_data = data.copy()
        nan_mask = np.isnan(cleaned_data)
        
        if strategy == 'forward_fill':
            # 前向填充
            last_valid = None
            for i in range(len(cleaned_data)):
                if not nan_mask[i]:
                    last_valid = cleaned_data[i]
                elif last_valid is not None:
                    cleaned_data[i] = last_valid
        
        elif strategy == 'backward_fill':
            # 后向填充
            next_valid = None
            for i in range(len(cleaned_data) - 1, -1, -1):
                if not nan_mask[i]:
                    next_valid = cleaned_data[i]
                elif next_valid is not None:
                    cleaned_data[i] = next_valid
        
        elif strategy == 'mean_fill':
            # 均值填充
            valid_data = cleaned_data[~nan_mask]
            if len(valid_data) > 0:
                mean_value = np.mean(valid_data)
                cleaned_data[nan_mask] = mean_value
        
        elif strategy == 'interpolate':
            # 线性插值
            valid_indices = np.where(~nan_mask)[0]
            if len(valid_indices) > 1:
                cleaned_data = np.interp(
                    np.arange(len(cleaned_data)),
                    valid_indices,
                    cleaned_data[valid_indices]
                )
        
        return cleaned_data
        
    except Exception as e:
        logger.error(f"缺失值处理失败: {e}")
        return data


def check_data_quality(prices: np.ndarray, volumes: np.ndarray, 
                      high: Optional[np.ndarray] = None, 
                      low: Optional[np.ndarray] = None) -> float:
    """检查数据质量
    
    Args:
        prices: 价格序列
        volumes: 成交量序列
        high: 最高价序列
        low: 最低价序列
        
    Returns:
        数据质量评分 (0-1)
    """
    try:
        quality_score = 1.0
        
        # 数据完整性检查
        if len(prices) == 0 or len(volumes) == 0:
            return 0.0
        
        # NaN值比例
        nan_ratio = np.sum(np.isnan(prices)) / len(prices)
        quality_score *= (1 - nan_ratio)
        
        # 零值比例
        zero_ratio = np.sum(prices == 0) / len(prices)
        quality_score *= (1 - zero_ratio)
        
        # 异常值比例
        price_outliers = detect_price_outliers(prices)
        outlier_ratio = price_outliers['count'] / len(prices)
        quality_score *= (1 - min(outlier_ratio, 0.1) / 0.1 * 0.2)  # 最多扣20%
        
        # 数据长度评分
        if len(prices) < 30:
            length_score = len(prices) / 30
            quality_score *= length_score
        
        # OHLC一致性检查
        if high is not None and low is not None:
            consistency_issues = 0
            total_checks = len(prices)
            
            # 检查OHLC逻辑
            consistency_issues += np.sum(high < low)
            consistency_issues += np.sum(prices > high)
            consistency_issues += np.sum(prices < low)
            
            consistency_score = 1 - (consistency_issues / total_checks)
            quality_score *= consistency_score
        
        # 时间序列连续性检查
        continuity_score = check_time_series_continuity(prices)
        quality_score *= continuity_score
        
        return max(0.0, min(1.0, quality_score))
        
    except Exception as e:
        logger.error(f"数据质量检查失败: {e}")
        return 0.5


def validate_time_series_consistency(data: np.ndarray) -> Dict[str, Any]:
    """验证时间序列一致性
    
    Args:
        data: 时间序列数据
        
    Returns:
        一致性检查结果
    """
    try:
        issues = []
        
        # 检查数据长度
        if len(data) < 2:
            issues.append("数据长度不足")
            return {'is_consistent': False, 'issues': issues}
        
        # 检查突变点
        changes = np.abs(np.diff(data))
        median_change = np.median(changes)
        mad = np.median(np.abs(changes - median_change))  # 中位数绝对偏差
        
        if mad > 0:
            threshold = median_change + 5 * mad  # 5倍MAD阈值
            sudden_changes = np.sum(changes > threshold)
            if sudden_changes > len(data) * 0.05:  # 超过5%的点有突变
                issues.append(f"检测到 {sudden_changes} 个突变点")
        
        # 检查趋势异常
        if len(data) >= 10:
            # 使用滑动窗口检查局部趋势
            window_size = min(10, len(data) // 3)
            trend_changes = 0
            
            for i in range(window_size, len(data) - window_size):
                left_trend = np.mean(data[i-window_size:i]) - np.mean(data[i-window_size:i-window_size//2])
                right_trend = np.mean(data[i+window_size//2:i+window_size]) - np.mean(data[i:i+window_size//2])
                
                if (left_trend > 0 and right_trend < 0) or (left_trend < 0 and right_trend > 0):
                    trend_changes += 1
            
            if trend_changes > len(data) * 0.1:  # 趋势变化过于频繁
                issues.append(f"趋势变化过于频繁: {trend_changes} 次")
        
        return {
            'is_consistent': len(issues) == 0,
            'issues': issues,
            'sudden_changes': sudden_changes if 'sudden_changes' in locals() else 0,
            'trend_changes': trend_changes if 'trend_changes' in locals() else 0
        }
        
    except Exception as e:
        logger.error(f"时间序列一致性验证失败: {e}")
        return {'is_consistent': False, 'issues': [f"验证失败: {e}"]}


def detect_price_outliers(prices: np.ndarray, method: str = 'iqr') -> Dict[str, Any]:
    """检测价格异常值
    
    Args:
        prices: 价格数据
        method: 检测方法 ('iqr', 'zscore', 'isolation')
        
    Returns:
        异常值检测结果
    """
    try:
        outliers = []
        
        if method == 'iqr':
            # 使用四分位距方法
            q1 = np.percentile(prices, 25)
            q3 = np.percentile(prices, 75)
            iqr = q3 - q1
            
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            outlier_mask = (prices < lower_bound) | (prices > upper_bound)
            outliers = np.where(outlier_mask)[0].tolist()
        
        elif method == 'zscore':
            # 使用Z分数方法
            z_scores = np.abs((prices - np.mean(prices)) / np.std(prices))
            outlier_mask = z_scores > 3
            outliers = np.where(outlier_mask)[0].tolist()
        
        return {
            'count': len(outliers),
            'indices': outliers,
            'method': method
        }
        
    except Exception as e:
        logger.error(f"价格异常值检测失败: {e}")
        return {'count': 0, 'indices': [], 'method': method}


def detect_volume_outliers(volumes: np.ndarray) -> Dict[str, Any]:
    """检测成交量异常值
    
    Args:
        volumes: 成交量数据
        
    Returns:
        异常值检测结果
    """
    try:
        # 成交量通常使用对数变换后检测异常值
        log_volumes = np.log(volumes + 1)  # 加1避免log(0)
        
        # 使用四分位距方法
        q1 = np.percentile(log_volumes, 25)
        q3 = np.percentile(log_volumes, 75)
        iqr = q3 - q1
        
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        outlier_mask = (log_volumes < lower_bound) | (log_volumes > upper_bound)
        outliers = np.where(outlier_mask)[0].tolist()
        
        return {
            'count': len(outliers),
            'indices': outliers,
            'method': 'log_iqr'
        }
        
    except Exception as e:
        logger.error(f"成交量异常值检测失败: {e}")
        return {'count': 0, 'indices': [], 'method': 'log_iqr'}


def check_time_series_continuity(data: np.ndarray) -> float:
    """检查时间序列连续性
    
    Args:
        data: 时间序列数据
        
    Returns:
        连续性评分 (0-1)
    """
    try:
        if len(data) < 3:
            return 1.0
        
        # 计算一阶差分
        diff1 = np.diff(data)
        
        # 计算二阶差分（加速度）
        diff2 = np.diff(diff1)
        
        # 连续性评分基于二阶差分的稳定性
        if len(diff2) > 0:
            diff2_std = np.std(diff2)
            diff1_std = np.std(diff1)
            
            if diff1_std > 0:
                continuity_score = 1.0 / (1.0 + diff2_std / diff1_std)
            else:
                continuity_score = 1.0
        else:
            continuity_score = 1.0
        
        return max(0.0, min(1.0, continuity_score))
        
    except Exception as e:
        logger.error(f"时间序列连续性检查失败: {e}")
        return 0.5
