# StockFM主配置文件

[system]
# 系统基本配置
log_level = "INFO"
timezone = "Asia/Shanghai"
trade_calendar_file = "data/trade_calendar.pkl"
data_dir = "data"
work_dir = "."

# 进程控制开关
[processes]
# 市场数据获取进程
market_data_fetcher = true

# 监控进程开关
trade_signal_monitor = false    # 交易信号监控进程
concept_monitor = false         # 概念监控进程
news_monitor = false           # 新闻监控进程
risk_monitor = false           # 风险监控进程
volume_ratio_analyzer = true    # 成交量比值分析器
volume_surge_processor = true   # 成交量激增处理器

# 进程定时执行时间统一配置
[process_schedule]
# 市场数据获取器调度时间
[process_schedule.market_data_fetcher]
start_time = "09:05:00"         # 启动时间
stop_time = "15:20:00"          # 销毁时间

# 成交量比率分析器调度时间
[process_schedule.volume_ratio_analyzer]
start_time = "09:33:00"         # 启动时间（延迟等待数据就绪）
stop_time = "15:00:00"          # 销毁时间
depends_on = ["market_data_fetcher"]  # 依赖的进程

# 盘后策略调度器调度时间
[process_schedule.after_market_scheduler]
execution_time = "16:00"        # 执行时间

# 数据库配置
[database]
# 数据库类型配置
default_type = "timescaledb"    # 默认使用TimescaleDB
use_timescaledb = true          # 启用TimescaleDB时序数据库

# 连接配置
host = "*********"
port = 6668
database = "xystock"
user = "postgres"
password = "241110"
connect_timeout = 10
client_encoding = "UTF8"

# 连接池配置
pool_size = 30                  # 数据库连接池大小
min_connections = 2             # 最小连接数
max_connections = 30            # 最大连接数
acquire_timeout = 15            # 获取连接超时时间(秒)
max_lifetime = 3600             # 连接最大生命周期(秒)
max_idle_time = 600             # 连接最大空闲时间(秒)
health_check_interval = 60      # 健康检查间隔(秒)
preload_pool = true             # 是否预加载连接池

# 监控配置
enable_monitoring = true        # 启用监控
collect_stats = true            # 收集统计信息
log_slow_queries = true         # 记录慢查询
slow_query_threshold_ms = 500   # 慢查询阈值(毫秒)
enable_leak_detection = true    # 启用连接泄漏检测
leak_detection_threshold = 300  # 连接泄漏检测阈值(秒)

# 死锁重试配置
[database.deadlock_retry]
max_retries = 3                 # 最大重试次数
base_delay = 0.1                # 基础延迟时间(秒)
max_delay = 2.0                 # 最大延迟时间(秒)

# 数据库缓存配置 - 高性能缓存系统
[database_cache]
# 缓存基本配置
enable_cache = true             # 启用数据库缓存
max_memory_size_mb = 100        # 最大内存使用量（MB）
default_ttl_seconds = 30        # 默认缓存时间（秒）
cleanup_interval_seconds = 60   # 清理间隔（秒）

# 批量查询配置
batch_query_size = 100          # 批量查询大小
enable_batch_optimization = true # 启用批量查询优化

# 缓存策略配置
[database_cache.strategies]
# Tick数据缓存策略
tick_data_ttl = 30              # Tick数据缓存30秒
tick_data_priority = "HIGH"     # 高优先级

# K线数据缓存策略
kline_5min_ttl = 60            # 5分钟K线缓存1分钟
kline_5min_priority = "HIGH"   # 高优先级

kline_day_ttl = 300            # 日K线缓存5分钟
kline_day_priority = "NORMAL"  # 普通优先级

kline_2hour_ttl = 180          # 2小时K线缓存3分钟
kline_2hour_priority = "NORMAL" # 普通优先级

# 股票信息缓存策略
stock_info_ttl = 3600          # 股票信息缓存1小时
stock_info_priority = "LOW"    # 低优先级

# 缓存预热配置
[database_cache.warmup]
enable_warmup = true           # 启用缓存预热
warmup_stock_count = 100       # 预热股票数量
warmup_on_startup = true       # 启动时预热
warmup_kline_days = 20         # 预热日K线天数
warmup_5min_periods = 50       # 预热5分钟K线周期数

[market]
# 市场配置
trading_hours = [
    { start = "09:30:00", end = "11:30:00" },
    { start = "13:00:00", end = "15:00:00" }
]
stock_exchange = "SSE"
index_symbols = ["000001.SH", "399001.SZ", "399006.SZ"]

[fetch]
# 数据获取配置
tdx_server_ip = "auto"          # 自动选择最佳服务器
tdx_enable_cache = true
update_interval = 3             # 更新间隔（秒）
batch_size = 80                 # 每批获取的股票数量
retry_count = 3                 # 失败重试次数
fetch_daily_time = "15:30:00"   # 每日获取日线数据的时间
symbols_file = "config/stock_list.toml"

[notification]
# 通知配置
enabled = false
notify_level = "WARNING"

[notification.email]
enabled = false
host = "smtp.example.com"
port = 587
user = "<EMAIL>"
password = "password"
recipients = ["<EMAIL>"]

# 飞书通知配置
[feishu]
# 飞书机器人webhook配置
webhook_url = ""  # 飞书机器人webhook地址
secret = ""       # 飞书机器人密钥
enabled = false   # 是否启用飞书通知

[notification.feishu]
enabled = true
webhook1 = "https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0"
secret1  = "fvgRW9PysyqTXe1sAgwrC"

webhook2 = "https://open.feishu.cn/open-apis/bot/v2/hook/54d75029-f7d0-44c2-a742-09554fed28bf"
secret2  = "Utybfdf6V2j7kIA2XEuG2b"

webhook3 = "https://open.feishu.cn/open-apis/bot/v2/hook/e5023237-6768-4795-9328-fed854fb0645"
secret3  = "EoFu1JzYtbw8glbwFH1LWf"

# 成交量比值分析器配置
[volume_ratio_analyzer]
enable = true
depends_on = ["market_data_fetcher"]

[logging]
# 日志配置
level = "DEBUG"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
file_path = "logs/stockfm.log"
max_size = 10                   # MB
backup_count = 5

# 市场数据获取器配置
[market_data_fetcher]
use_database_cache = true       # 使用数据库缓存系统

# 性能优化配置
[performance_optimization]
# 数据访问优化
enable_data_prefetch = true          # 启用数据预取
prefetch_window_size = 100           # 预取窗口大小

# 缓存优化
enable_smart_cache = true            # 启用智能缓存
cache_hit_ratio_target = 0.9         # 目标缓存命中率90%

# 并发优化
max_concurrent_stocks = 50           # 最大并发处理股票数
enable_parallel_processing = true    # 启用并行处理

# 内存回收优化
gc_threshold_mb = 8192               # GC触发阈值（8GB）
enable_aggressive_gc = false        # 禁用激进GC（保持性能）

# 监控和诊断
enable_memory_profiling = true      # 启用内存分析
profiling_interval = 300            # 分析间隔（5分钟）
enable_performance_alerts = true    # 启用性能告警

# 成交量激增处理器调度配置
[process_schedule.volume_surge_processor]
start_time = "09:27:00"             # 预热启动时间
stop_time = "15:05:00"              # 停止时间
depends_on = ["market_data_fetcher"] # 依赖市场数据获取器
priority = "HIGH"                   # 高优先级进程
auto_restart = true                 # 启用自动重启
max_restart_count = 3               # 最大重启次数
restart_delay = 30                  # 重启延迟（秒）

# 成交量激增处理器专用数据库连接池
[database.volume_surge_pool]
pool_size = 5                       # 专用连接池大小
max_connections = 10                # 最大连接数
acquire_timeout = 5                 # 获取连接超时（秒）
