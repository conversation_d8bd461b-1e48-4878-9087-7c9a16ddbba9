#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试价格变化计算功能

测试新增的change字段计算逻辑

作者: QuantFM Team
创建时间: 2025-08-17
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from processes.market_data_fetcher import calculate_price_changes, update_tick_cache, filter_volume_increased_data


def test_price_change_calculation():
    """测试价格变化计算功能"""
    print("=" * 60)
    print("测试价格变化计算功能")
    print("=" * 60)
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'stock_code': ['000001', '000002', '000001', '000002', '000001'],
        'price': [10.50, 20.30, 10.55, 20.25, 10.60],
        'volume': [1000, 2000, 1100, 2100, 1200],
        'trade_time': pd.date_range('2025-08-17 09:30:00', periods=5, freq='1min')
    })
    
    print("原始测试数据:")
    print(test_data)
    print()
    
    # 初始化缓存
    last_tick_data = {}
    
    # 测试价格变化计算
    print("开始计算价格变化...")
    result_df = calculate_price_changes(test_data, last_tick_data)
    
    print("计算后的数据:")
    print(result_df[['stock_code', 'price', 'change']])
    print()
    
    # 更新缓存
    update_tick_cache(result_df, last_tick_data)
    
    # 验证计算结果
    expected_changes = [0.0, 0.0, 0.05, -0.05, 0.05]  # 预期的价格变化
    actual_changes = result_df['change'].tolist()
    
    print("验证结果:")
    for i, (expected, actual) in enumerate(zip(expected_changes, actual_changes)):
        stock_code = result_df.iloc[i]['stock_code']
        price = result_df.iloc[i]['price']
        status = "✓" if abs(expected - actual) < 0.001 else "✗"
        print(f"  {status} {stock_code}: 价格={price}, 预期变化={expected}, 实际变化={actual}")
    
    # 检查缓存是否正确更新
    print("\n缓存状态:")
    for stock_code, data in last_tick_data.items():
        print(f"  {stock_code}: 价格={data['price']}, 成交量={data['volume']}")
    
    print("\n测试完成!")
    return True


def test_volume_filter():
    """测试成交量过滤功能"""
    print("\n" + "=" * 60)
    print("测试成交量过滤功能")
    print("=" * 60)
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'stock_code': ['000001', '000002', '000001', '000002'],
        'price': [10.50, 20.30, 10.55, 20.25],
        'volume': [1000, 2000, 1100, 1900],  # 000002的成交量减少了
    })
    
    # 初始化缓存（模拟已有数据）
    last_tick_data = {
        '000001': {'price': 10.45, 'volume': 900},
        '000002': {'price': 20.35, 'volume': 2000}
    }
    
    print("原始数据:")
    print(test_data)
    print("\n缓存数据:")
    for code, data in last_tick_data.items():
        print(f"  {code}: 价格={data['price']}, 成交量={data['volume']}")
    
    # 过滤成交量增加的数据
    filtered_df = filter_volume_increased_data(test_data, last_tick_data)
    
    print("\n过滤后的数据:")
    print(filtered_df)
    
    # 验证过滤结果
    expected_codes = ['000001', '000001']  # 只有000001的数据应该保留
    actual_codes = filtered_df['stock_code'].tolist()
    
    print("\n验证结果:")
    if actual_codes == expected_codes:
        print("  ✓ 成交量过滤正确")
    else:
        print(f"  ✗ 成交量过滤错误，预期: {expected_codes}, 实际: {actual_codes}")
    
    return actual_codes == expected_codes


def test_integrated_workflow():
    """测试完整的工作流程"""
    print("\n" + "=" * 60)
    print("测试完整工作流程")
    print("=" * 60)
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'stock_code': ['000001', '000002', '000001', '000002', '000001'],
        'price': [10.50, 20.30, 10.55, 20.25, 10.60],
        'volume': [1000, 2000, 1100, 2100, 1200],
    })
    
    # 初始化缓存
    last_tick_data = {}
    
    print("完整工作流程测试:")
    print("1. 过滤成交量增加的数据")
    filtered_df = filter_volume_increased_data(test_data, last_tick_data)
    print(f"   过滤后数据量: {len(filtered_df)}")
    
    print("2. 计算价格变化")
    if not filtered_df.empty:
        result_df = calculate_price_changes(filtered_df, last_tick_data)
        print(f"   计算完成，包含change字段: {'change' in result_df.columns}")
        
        print("3. 更新缓存")
        update_tick_cache(result_df, last_tick_data)
        print(f"   缓存更新完成，包含 {len(last_tick_data)} 只股票")
        
        print("\n最终结果:")
        print(result_df[['stock_code', 'price', 'volume', 'change']])
        
        return True
    else:
        print("   没有数据需要处理")
        return False


if __name__ == "__main__":
    print("开始测试价格变化计算功能...")
    
    # 运行所有测试
    test1_result = test_price_change_calculation()
    test2_result = test_volume_filter()
    test3_result = test_integrated_workflow()
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    print(f"价格变化计算测试: {'✓ 通过' if test1_result else '✗ 失败'}")
    print(f"成交量过滤测试: {'✓ 通过' if test2_result else '✗ 失败'}")
    print(f"完整工作流程测试: {'✓ 通过' if test3_result else '✗ 失败'}")
    
    if all([test1_result, test2_result, test3_result]):
        print("\n🎉 所有测试通过！change字段功能实现正确。")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，请检查实现。")
        sys.exit(1)
