# 设计文档

## 概述

成交量激增数据处理器（VolumeSurgeDataProcessor）是一个基于时间窗口调度的统一成交量异动检测系统。该系统通过整合现有的数据库管理、缓存系统和实时计算组件，实现开盘期和盘中期的成交量激增检测，为交易决策提供及时的市场异动信号。

## 架构

### 系统架构图

```mermaid
graph TB
    A[VolumeSurgeDataProcessor] --> B[时间窗口调度器]
    A --> C[数据源管理器]
    A --> D[因子计算引擎]
    A --> E[缓存管理系统]
    A --> F[性能监控模块]
    
    B --> B1[开盘期调度 9:30-9:45]
    B --> B2[盘中期调度 9:45-15:00]
    B --> B3[非交易时间处理]
    
    C --> C1[TimescaleDB连接池]
    C --> C2[股票列表管理]
    C --> C3[实时Tick数据获取]
    
    D --> D1[开盘因子计算器]
    D --> D2[盘中因子计算器]
    D --> D3[成交量比值计算]
    
    E --> E1[历史数据缓存]
    E --> E2[实时K线缓存]
    E --> E3[成交量统计缓存]
    
    F --> F1[SLA监控]
    F --> F2[性能统计]
    F --> F3[异常告警]
```

### 核心组件关系

```mermaid
classDiagram
    class VolumeSurgeDataProcessor {
        +start()
        +stop()
        +process_cycle()
        -initialize_components()
        -cleanup_resources()
    }
    
    class TimeWindowScheduler {
        +get_current_strategy()
        +is_trading_time()
        +should_process()
        -calculate_time_window()
    }
    
    class DataSourceManager {
        +get_stock_list()
        +fetch_tick_data()
        +get_historical_data()
        -manage_connections()
    }
    
    class FactorCalculationEngine {
        +calculate_opening_factor()
        +calculate_intraday_factor()
        +detect_volume_surge()
        -validate_results()
    }
    
    class CacheManager {
        +get_cached_data()
        +update_cache()
        +invalidate_cache()
        -optimize_memory()
    }
    
    class PerformanceMonitor {
        +collect_metrics()
        +check_sla()
        +generate_alerts()
        -analyze_performance()
    }
    
    VolumeSurgeDataProcessor --> TimeWindowScheduler
    VolumeSurgeDataProcessor --> DataSourceManager
    VolumeSurgeDataProcessor --> FactorCalculationEngine
    VolumeSurgeDataProcessor --> CacheManager
    VolumeSurgeDataProcessor --> PerformanceMonitor
```

## 组件和接口

### 1. 时间窗口调度器 (TimeWindowScheduler)

**职责**: 根据当前时间自动选择处理策略

**接口设计**:
```python
class TimeWindowScheduler:
    def __init__(self, config: Dict):
        self.trading_hours = config.get('trading_hours')
        self.preload_time = "09:26:00"
        
    def get_current_strategy(self) -> ProcessingStrategy:
        """获取当前时间窗口的处理策略"""
        
    def is_trading_time(self) -> bool:
        """判断是否为交易时间"""
        
    def should_preload(self) -> bool:
        """判断是否应该预热数据"""
        
    def get_time_window_info(self) -> Dict:
        """获取当前时间窗口信息"""
```

**核心逻辑**:
- 9:26-9:30: 数据预热期，加载历史统计数据
- 9:30-9:45: 开盘期处理策略
- 9:45-15:00: 盘中期处理策略  
- 其他时间: 进程休眠或销毁

### 2. 数据源管理器 (DataSourceManager)

**职责**: 统一管理TimescaleDB数据访问和股票列表

**接口设计**:
```python
class DataSourceManager:
    def __init__(self, db_manager: UnifiedDatabaseManager):
        self.db_manager = db_manager
        self.stock_list = []
        
    def load_stock_list(self) -> List[str]:
        """加载股票列表，优先从stock_info表获取"""
        
    def fetch_realtime_tick_data(self, stock_codes: List[str], 
                                since_time: datetime) -> pd.DataFrame:
        """获取实时Tick数据"""
        
    def fetch_historical_volume_stats(self, stock_codes: List[str], 
                                    days: int = 10) -> Dict[str, float]:
        """获取历史成交量统计数据"""
        
    def fetch_opening_period_history(self, stock_codes: List[str], 
                                   days: int = 10) -> Dict[str, float]:
        """获取开盘期历史成交量数据"""
```

**数据获取策略**:
- 股票列表: 优先从stock_info表获取，备选config/stock_list.toml
- 实时数据: 使用IncrementalTickFetcher增量获取
- 历史数据: 利用TimescaleDB的time_bucket函数优化查询

### 3. 因子计算引擎 (FactorCalculationEngine)

**职责**: 实现开盘因子和盘中因子的计算逻辑

**接口设计**:
```python
class FactorCalculationEngine:
    def __init__(self, cache_manager: CacheManager):
        self.cache_manager = cache_manager
        self.opening_threshold = 100  # 开盘期阈值
        self.intraday_threshold = 10  # 盘中期阈值
        
    def calculate_opening_factor(self, stock_code: str, 
                               current_volume: int, 
                               current_time: datetime) -> Dict:
        """计算开盘因子"""
        
    def calculate_intraday_factor(self, stock_code: str, 
                                kline_data: Dict, 
                                current_time: datetime) -> Dict:
        """计算盘中因子"""
        
    def detect_volume_surge(self, factor_results: List[Dict]) -> List[Dict]:
        """检测成交量激增信号"""
```

**计算逻辑**:

**开盘期处理 (9:30-9:45)**:
```python
# 1. 获取当前时刻实时成交量
current_volume = get_current_tick_volume(stock_code, current_time)

# 2. 在9:27分查询历史10天的9:30-9:45平均成交量
historical_avg = get_opening_period_avg_volume(stock_code, days=10)

# 3. 计算比值
ratio = current_volume / historical_avg

# 4. 判断激增
is_surge = ratio > 100
```

**盘中期处理 (9:45-15:00)**:
```python
# 1. 流式合成5分钟K线
kline_data = synthesize_5min_kline(tick_data, current_bucket)

# 2. 获取当日所有5分钟K线
today_klines = get_today_klines(stock_code)

# 3. 分离当前K线和历史K线
current_kline = kline_data
historical_klines = today_klines[:-1]

# 4. 计算历史平均成交量
historical_avg = sum(k['volume'] for k in historical_klines) / len(historical_klines)

# 5. 计算比值
ratio = current_kline['volume'] / historical_avg

# 6. 判断激增
is_surge = ratio > 10
```

### 4. 缓存管理系统 (CacheManager)

**职责**: 高性能数据缓存，减少数据库查询

**接口设计**:
```python
class CacheManager:
    def __init__(self, config: Dict):
        self.historical_volume_cache = {}  # 历史成交量缓存
        self.kline_cache = {}             # K线数据缓存
        self.tick_cache = {}              # Tick数据缓存
        
    def get_historical_avg_volume(self, stock_code: str) -> Optional[float]:
        """获取历史平均成交量（带缓存）"""
        
    def update_kline_cache(self, stock_code: str, kline_data: Dict):
        """更新K线缓存"""
        
    def get_cached_klines(self, stock_code: str, 
                         start_time: datetime) -> List[Dict]:
        """获取缓存的K线数据"""
        
    def invalidate_expired_cache(self):
        """清理过期缓存"""
```

**缓存策略**:
- 历史成交量: 缓存1小时，每日9:26更新
- 5分钟K线: 缓存当日数据，实时更新
- Tick数据: 缓存最近30秒，滚动更新
- LRU淘汰: 内存超过阈值时自动清理

### 5. 性能监控模块 (PerformanceMonitor)

**职责**: SLA监控和性能统计

**接口设计**:
```python
class PerformanceMonitor:
    def __init__(self, config: Dict):
        self.sla_targets = config.get('sla_targets', {})
        self.metrics = {}
        
    def record_processing_time(self, operation: str, duration: float):
        """记录处理时间"""
        
    def check_sla_compliance(self) -> Dict[str, bool]:
        """检查SLA达标情况"""
        
    def generate_performance_report(self) -> Dict:
        """生成性能报告"""
        
    def trigger_alert(self, alert_type: str, message: str):
        """触发性能告警"""
```

**监控指标**:
- 处理延迟: 单次处理周期耗时 < 5秒
- 数据获取延迟: Tick数据获取 < 1秒
- 缓存命中率: > 90%
- 内存使用率: < 80%
- 错误率: < 1%

## 数据模型

### 输入数据模型

**Tick数据结构**:
```python
@dataclass
class TickData:
    trade_time: datetime
    stock_code: str
    price: float
    volume: int
    amount: float
    cur_vol: int  # 当前成交量
```

**K线数据结构**:
```python
@dataclass
class KlineData:
    trade_time: datetime
    stock_code: str
    open: float
    high: float
    low: float
    close: float
    volume: int
    amount: float
```

### 输出数据模型

**成交量激增信号**:
```python
@dataclass
class VolumeSurgeSignal:
    stock_code: str
    signal_type: str  # 'opening_surge' | 'intraday_surge'
    volume_ratio: float
    current_volume: int
    historical_avg_volume: float
    current_price: float
    price_change_pct: float
    timestamp: datetime
    confidence_score: float
    
    def to_dict(self) -> Dict:
        """转换为字典格式用于存储"""
```

### 数据库表设计

**成交量激增信号表**:
```sql
CREATE TABLE volume_surge_signals (
    id SERIAL PRIMARY KEY,
    trade_time TIMESTAMPTZ NOT NULL,
    stock_code VARCHAR(10) NOT NULL,
    signal_type VARCHAR(20) NOT NULL,
    volume_ratio DECIMAL(10,2) NOT NULL,
    current_volume BIGINT NOT NULL,
    historical_avg_volume DECIMAL(15,2) NOT NULL,
    current_price DECIMAL(10,3) NOT NULL,
    price_change_pct DECIMAL(8,4),
    confidence_score DECIMAL(5,4),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建时间分区索引（TimescaleDB）
SELECT create_hypertable('volume_surge_signals', 'trade_time');

-- 创建复合索引
CREATE INDEX idx_volume_surge_signals_stock_time 
ON volume_surge_signals (stock_code, trade_time DESC);
```

## 错误处理

### 异常分类和处理策略

**1. 数据库连接异常**:
```python
class DatabaseConnectionError(Exception):
    """数据库连接异常"""
    
def handle_db_connection_error(self, error: Exception):
    """处理数据库连接异常"""
    # 1. 记录错误日志
    # 2. 尝试重连（最多3次）
    # 3. 降级到缓存数据
    # 4. 发送告警通知
```

**2. 数据质量异常**:
```python
class DataQualityError(Exception):
    """数据质量异常"""
    
def handle_data_quality_error(self, stock_code: str, error: Exception):
    """处理数据质量异常"""
    # 1. 跳过异常股票
    # 2. 记录异常股票列表
    # 3. 继续处理其他股票
    # 4. 定期重试异常股票
```

**3. 计算异常**:
```python
class CalculationError(Exception):
    """计算异常"""
    
def handle_calculation_error(self, operation: str, error: Exception):
    """处理计算异常"""
    # 1. 使用默认值或历史值
    # 2. 降低置信度
    # 3. 记录异常详情
    # 4. 继续后续处理
```

**4. 性能异常**:
```python
class PerformanceError(Exception):
    """性能异常"""
    
def handle_performance_error(self, metric: str, current_value: float):
    """处理性能异常"""
    # 1. 触发性能告警
    # 2. 启动性能优化模式
    # 3. 减少处理股票数量
    # 4. 增加缓存使用
```

### 容错机制

**1. 熔断器模式**:
```python
class CircuitBreaker:
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
```

**2. 重试机制**:
```python
def retry_with_backoff(func, max_retries: int = 3, base_delay: float = 1.0):
    """指数退避重试"""
    for attempt in range(max_retries):
        try:
            return func()
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            delay = base_delay * (2 ** attempt)
            time.sleep(delay)
```

**3. 降级策略**:
```python
def fallback_to_cache(self, stock_code: str, operation: str):
    """降级到缓存数据"""
    # 1. 尝试从缓存获取数据
    # 2. 使用历史平均值
    # 3. 降低数据精度
    # 4. 标记为降级数据
```

## 测试策略

### 单元测试

**1. 时间窗口调度器测试**:
```python
class TestTimeWindowScheduler:
    def test_opening_period_detection(self):
        """测试开盘期检测"""
        
    def test_intraday_period_detection(self):
        """测试盘中期检测"""
        
    def test_non_trading_time_handling(self):
        """测试非交易时间处理"""
```

**2. 因子计算引擎测试**:
```python
class TestFactorCalculationEngine:
    def test_opening_factor_calculation(self):
        """测试开盘因子计算"""
        
    def test_intraday_factor_calculation(self):
        """测试盘中因子计算"""
        
    def test_volume_surge_detection(self):
        """测试成交量激增检测"""
```

### 集成测试

**1. 端到端测试**:
```python
class TestVolumeSurgeDataProcessor:
    def test_full_processing_cycle(self):
        """测试完整处理周期"""
        
    def test_data_flow_integrity(self):
        """测试数据流完整性"""
        
    def test_performance_under_load(self):
        """测试负载下的性能"""
```

**2. 数据库集成测试**:
```python
class TestDatabaseIntegration:
    def test_timescaledb_queries(self):
        """测试TimescaleDB查询"""
        
    def test_connection_pool_behavior(self):
        """测试连接池行为"""
        
    def test_transaction_handling(self):
        """测试事务处理"""
```

### 性能测试

**1. 压力测试**:
- 模拟3000只股票同时处理
- 测试高频Tick数据处理能力
- 验证内存使用和GC性能

**2. 延迟测试**:
- 测试端到端处理延迟
- 验证SLA指标达标情况
- 分析性能瓶颈点

**3. 稳定性测试**:
- 7x24小时连续运行测试
- 异常场景恢复测试
- 内存泄漏检测

### 测试数据准备

**1. 模拟数据生成**:
```python
class TestDataGenerator:
    def generate_tick_data(self, stock_codes: List[str], 
                          start_time: datetime, 
                          end_time: datetime) -> pd.DataFrame:
        """生成模拟Tick数据"""
        
    def generate_volume_surge_scenarios(self) -> List[Dict]:
        """生成成交量激增场景"""
```

**2. 历史数据回放**:
```python
class HistoricalDataReplayer:
    def replay_trading_day(self, date: datetime.date):
        """回放历史交易日数据"""
        
    def simulate_market_conditions(self, scenario: str):
        """模拟特定市场条件"""
```

通过这个全面的设计文档，成交量激增数据处理器将能够高效、稳定地检测市场中的成交量异动，为交易决策提供及时准确的信号。