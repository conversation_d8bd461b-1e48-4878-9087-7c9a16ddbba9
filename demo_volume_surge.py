#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成交量激增处理器演示脚本

演示成交量激增处理器的主要功能。

作者: QuantFM Team
创建时间: 2025-08-08
"""

import sys
import os
from datetime import datetime, time as dt_time
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from processes.volume_surge_processor import VolumeSurgeProcessor
from factors.volume_surge_factor import VolumeSurgeFactorEngine
from factors.signal_continuity_manager import SignalContinuityManager
from factors.volume_data_manager import VolumeDataManager


def demo_factor_engine():
    """演示因子引擎功能"""
    print("=" * 60)
    print("演示成交量激增因子引擎")
    print("=" * 60)
    
    # 创建因子引擎
    config = {
        "opening_ratio_threshold": 100,
        "intraday_ratio_threshold": 10,
        "historical_days": 10,
        "min_volume_threshold": 1000
    }
    
    engine = VolumeSurgeFactorEngine(config)
    
    # 演示开盘期检测
    print("\n1. 开盘期激增检测演示:")
    stock_data = {
        "000001.SZ": 1000000,  # 100万成交量
        "000002.SZ": 500000,   # 50万成交量
        "000003.SZ": 50000     # 5万成交量
    }
    
    historical_data = {
        "000001.SZ": 10000,    # 历史平均1万，比值100倍 ✓
        "000002.SZ": 100000,   # 历史平均10万，比值5倍 ✗
        "000003.SZ": 1000      # 历史平均1千，比值50倍 ✗
    }
    
    signals = engine.opening_surge_detection(stock_data, historical_data)
    
    print(f"检测到 {len(signals)} 个开盘期激增信号:")
    for signal in signals:
        print(f"  - {signal.stock_code}: {signal.surge_ratio:.1f}倍 (阈值: {engine.opening_threshold}倍)")
    
    # 演示盘中期检测
    print("\n2. 盘中期激增检测演示:")
    kline_data = {
        "000001.SZ": {"volume": 200000, "trade_time": datetime.now()},
        "000002.SZ": {"volume": 150000, "trade_time": datetime.now()},
        "000003.SZ": {"volume": 50000, "trade_time": datetime.now()}
    }
    
    historical_data = {
        "000001.SZ": 20000,    # 历史平均2万，比值10倍 ✓
        "000002.SZ": 30000,    # 历史平均3万，比值5倍 ✗
        "000003.SZ": 10000     # 历史平均1万，比值5倍 ✗
    }
    
    signals = engine.intraday_surge_detection(kline_data, historical_data)
    
    print(f"检测到 {len(signals)} 个盘中期激增信号:")
    for signal in signals:
        print(f"  - {signal.stock_code}: {signal.surge_ratio:.1f}倍 (阈值: {engine.intraday_threshold}倍)")
    
    # 显示统计信息
    print("\n3. 引擎统计信息:")
    stats = engine.get_statistics()
    for key, value in stats.items():
        print(f"  - {key}: {value}")


def demo_continuity_manager():
    """演示连续性管理器功能"""
    print("\n" + "=" * 60)
    print("演示信号连续性管理器")
    print("=" * 60)
    
    config = {
        "enable_continuity_check": True,
        "max_signal_gap_seconds": 300,
        "reset_on_discontinuity": True
    }
    
    manager = SignalContinuityManager(config)
    
    # 模拟信号序列
    stock_code = "000001.SZ"
    signal_type = "OPENING"
    
    print("\n1. 连续性检查演示:")
    
    # 第一次信号
    timestamp1 = datetime.now().replace(hour=9, minute=35, second=0)
    result1 = manager.check_signal_continuity(stock_code, signal_type, timestamp1)
    count1 = manager.get_continuous_count(stock_code)
    print(f"  第1次信号 (09:35): 允许={result1}, 连续次数={count1}")
    
    # 同一周期重复信号（应该被阻止）
    timestamp2 = datetime.now().replace(hour=9, minute=36, second=0)
    result2 = manager.check_signal_continuity(stock_code, signal_type, timestamp2)
    count2 = manager.get_continuous_count(stock_code)
    print(f"  重复信号 (09:36): 允许={result2}, 连续次数={count2}")
    
    # 下一个周期的信号（盘中期）
    timestamp3 = datetime.now().replace(hour=9, minute=50, second=0)
    result3 = manager.check_signal_continuity(stock_code, "INTRADAY", timestamp3)
    count3 = manager.get_continuous_count(stock_code)
    print(f"  盘中期信号 (09:50): 允许={result3}, 连续次数={count3}")
    
    # 显示统计信息
    print("\n2. 连续性管理器统计信息:")
    stats = manager.get_statistics()
    for key, value in stats.items():
        print(f"  - {key}: {value}")


def demo_data_manager():
    """演示数据管理器功能"""
    print("\n" + "=" * 60)
    print("演示成交量数据管理器")
    print("=" * 60)
    
    config = {
        "enable_cache": True,
        "cache_ttl": 3600,
        "max_cache_size": 10000,
        "preload_historical_data": False  # 演示时不预加载
    }
    
    data_manager = VolumeDataManager(config)
    
    print("\n1. 缓存功能演示:")
    
    # 测试缓存
    cache_key = "opening_avg_000001.SZ_10"
    test_data = 50000.0
    
    data_manager._set_cache(cache_key, test_data)
    cached_data = data_manager._get_from_cache(cache_key)
    
    print(f"  设置缓存: {cache_key} = {test_data}")
    print(f"  获取缓存: {cached_data}")
    print(f"  缓存命中: {cached_data == test_data}")
    
    # 显示缓存统计
    print("\n2. 缓存统计信息:")
    stats = data_manager.get_cache_stats()
    for key, value in stats.items():
        print(f"  - {key}: {value}")


def demo_processor_status():
    """演示处理器状态"""
    print("\n" + "=" * 60)
    print("演示成交量激增处理器状态")
    print("=" * 60)
    
    try:
        processor = VolumeSurgeProcessor()
        
        print("\n1. 处理器配置:")
        print(f"  - 开盘期阈值: {processor.factor_engine.opening_threshold}倍")
        print(f"  - 盘中期阈值: {processor.factor_engine.intraday_threshold}倍")
        print(f"  - 连续监控: {processor.continuous_monitoring}")
        print(f"  - 最大并发股票数: {processor.max_concurrent_stocks}")
        
        print("\n2. 时间窗口配置:")
        print(f"  - 预热时间: {processor.preheat_time}")
        print(f"  - 开盘期: {processor.opening_start} - {processor.opening_end}")
        print(f"  - 盘中期: {processor.intraday_start} - {processor.intraday_end}")
        
        print("\n3. 当前状态:")
        current_time = datetime.now().time()
        print(f"  - 当前时间: {current_time}")
        print(f"  - 是否交易时间: {processor._is_trading_time(current_time)}")
        print(f"  - 当前时期: {processor._get_current_period_name(current_time)}")
        
        # 模拟强制处理一只股票
        print("\n4. 强制处理演示 (模拟数据):")
        result = processor.force_process_stock("000001.SZ")
        print(f"  处理结果: {result}")
        
    except Exception as e:
        print(f"演示处理器时出错: {e}")


def main():
    """主演示函数"""
    print("成交量激增处理器功能演示")
    print("=" * 80)
    
    try:
        # 演示各个组件
        demo_factor_engine()
        demo_continuity_manager()
        demo_data_manager()
        demo_processor_status()
        
        print("\n" + "=" * 80)
        print("演示完成！")
        print("\n系统特点总结:")
        print("✅ 开盘期100倍阈值检测")
        print("✅ 盘中期10倍阈值检测")
        print("✅ 连续信号管理 (同一周期内只出一次信号)")
        print("✅ 多级缓存优化")
        print("✅ 事件驱动的连续监控")
        print("✅ 完整的性能监控和统计")
        print("=" * 80)
        
    except Exception as e:
        print(f"演示过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
