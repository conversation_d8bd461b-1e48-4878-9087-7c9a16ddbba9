#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
市场数据获取器

从通达信获取股票实时行情数据，保存到TimescaleDB数据库并更新日线数据
专注于：tick数据获取 → 数据库存储 → 日线数据更新
"""

import os
import sys
import time
import logging
import threading
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional

from mootdx.quotes import Quotes
import toml
import queue
from datetime import datetime, time as dt_time, date

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from data.db_manager import get_db_manager
from utils.logger import setup_logger, get_logger

# 设置日志
logger = logging.getLogger(__name__)

# 创建日志目录
os.makedirs("logs", exist_ok=True)

# 配置日志
# 清除现有的处理器
root_logger = logging.getLogger()
for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)

# 配置日志
logging.basicConfig(
    level=logging.INFO,  # 设置为INFO级别，减少调试信息
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/market_data_fetcher.log", mode='w'),  # 使用'w'模式，覆盖现有日志文件
        logging.StreamHandler()
    ]
)

# 设置所有日志记录器的级别
for name in logging.root.manager.loggerDict:
    logger_instance = logging.getLogger(name)
    # 对于第三方库，设置更高的日志级别
    if any(lib in name for lib in ['tdxpy', 'mootdx', 'urllib3', 'requests']):
        logger_instance.setLevel(logging.WARNING)  # 第三方库只显示警告和错误
    else:
        logger_instance.setLevel(logging.INFO)  # 项目代码显示INFO级别

logger.setLevel(logging.INFO)  # 设置为INFO级别

# 输出日志配置信息
print(f"市场数据获取进程日志文件路径: logs/market_data_fetcher.log")

# 预定义的通达信服务器列表，按响应时间排序（已验证可用）
# 检查时间: 2025-06-10 14:42:16, 成功率: 94.4% (17/18)
TDX_SERVERS = [
    ('182.118.8.4', 7709),        # 北京 - 1.88ms (最快)
    ('202.108.254.67', 7709),     # 深圳 - 13.43ms
    ('123.125.108.103', 7709),    # 北京 - 13.67ms
    ('202.108.253.158', 7709),    # 上海 - 13.89ms
    ('221.0.195.48', 7709),       # 测试服务器 - 15.73ms
    ('111.13.112.206', 7709),     # 武汉 - 19.02ms
    ('114.141.177.44', 7709),     # 杭州 - 19.04ms
    ('219.146.254.27', 7709),     # 上海 - 21.63ms
    ('120.199.2.123', 7709),      # 杭州 - 24.61ms
    ('180.153.18.170', 7709),     # 测试服务器 - 26.38ms
    ('60.12.136.250', 7709),      # 杭州 - 26.74ms
    ('120.253.221.207', 7709),    # 上海 - 30.91ms
    ('115.238.56.198', 7709),     # 上海 - 44.3ms
    ('60.191.117.167', 7709),     # 武汉 - 56.94ms
    ('183.131.224.21', 7709),     # 测试服务器 - 76.9ms
    # 已移除无效服务器: ('103.24.178.242', 7709) - 连接失败，错误代码: 111
]

class QuotesClientPool:
    """共享连接池，所有线程共享连接以提高性能"""

    def __init__(self, pool_size: int = 50, timeout: int = 5):
        """
        初始化客户端池

        Args:
            pool_size: 池大小（最大连接数）
            timeout: 连接超时时间（秒）
        """
        self.pool_size = pool_size
        self.timeout = timeout
        self.client_queue = queue.Queue(maxsize=pool_size)  # 使用队列管理连接
        self.lock = threading.Lock()
        self.server_success_count = {server: 0 for server in TDX_SERVERS}
        self.server_failure_count = {server: 0 for server in TDX_SERVERS}
        self.creation_count = 0
        self.reuse_count = 0  # 连接复用次数
        self.active_clients = 0
        self.max_active_clients = 0
        self.max_clients_warning_count = 0  # "已达到最大连接数"警告次数

        # [修复] 底层优化：连接泄漏检测和自动恢复
        self.connection_leak_threshold = 10  # 连续无法获取连接的次数阈值
        self.consecutive_get_failures = 0
        self.last_leak_check_time = time.time()
        self.leak_check_interval = 30  # 每30秒检查一次连接泄漏
        self.async_init_complete = False  # 异步初始化是否完成
        self.async_init_thread: Optional[threading.Thread] = None  # 异步初始化线程

        # 优化：缓存最佳服务器，避免每次都排序
        self.best_server: Optional[Any] = None
        self.server_last_update = 0
        self.server_cache_duration = 300  # 5分钟更新一次最佳服务器

        # [修复] Debian 系统底层优化配置
        self.debian_optimizations = {
            'socket_keepalive': True,      # 启用 TCP keepalive
            'socket_nodelay': True,        # 禁用 Nagle 算法
            'socket_timeout': timeout * 0.8,  # 更短的 socket 超时
            'connection_pool_warmup': True,    # 连接池预热
            'server_health_check': True,       # 服务器健康检查
            'adaptive_timeout': True,          # 自适应超时
            'use_ipv4_only': True,            # 强制使用 IPv4
            'tcp_user_timeout': 15000,        # TCP 用户超时 (毫秒)
        }

        # 性能监控和统计
        self.connection_stats = {
            'total_created': 0,
            'total_failed': 0,
            'total_reused': 0,
            'avg_connection_time': 0.0,
            'last_health_check': time.time(),
            'debian_optimizations_applied': 0
        }

        logger.info(f"[系统优化] Debian 系统优化已启用: {self.debian_optimizations}")

        # 预先创建连接池中的客户端
        logger.info(f"初始化连接池，大小: {pool_size}")

        # 优化：预创建少量连接，避免初始化卡顿
        preconnect_count = min(pool_size, max(2, int(pool_size * 0.2)))  # 预创建20%的连接，最少2个，最多不超过5个
        preconnect_count = min(preconnect_count, 5)  # 限制最多5个初始连接

        # 使用标准超时时间，确保连接质量
        connection_timeout = timeout

        # 创建初始连接 - 优化：使用更短的超时时间避免卡顿
        successful_connections = 0
        failed_connections = 0

        # 使用更短的超时时间进行初始连接，避免卡顿
        init_timeout = min(3, connection_timeout)  # 最多3秒超时
        logger.info(f"开始创建 {preconnect_count} 个初始连接，超时时间: {init_timeout}秒")

        for i in range(preconnect_count):
            try:
                # 使用更短的超时时间
                client = self._create_new_client(timeout=init_timeout)
                if client:
                    self.client_queue.put(client)
                    successful_connections += 1
                    logger.debug(f"成功创建连接 {i+1}/{preconnect_count}")
                else:
                    failed_connections += 1
                    logger.debug(f"创建连接 {i+1}/{preconnect_count} 失败")
            except Exception as e:
                failed_connections += 1
                logger.debug(f"创建连接 {i+1}/{preconnect_count} 异常: {e}")

            # 如果连续失败太多，提前退出避免长时间卡顿
            if failed_connections >= 5 and successful_connections == 0:
                logger.warning(f"连续失败 {failed_connections} 次，提前退出初始连接创建")
                break

        # 记录连接池初始状态
        if successful_connections > 0:
            logger.info(f"连接池初始化成功，预创建连接数: {successful_connections}/{preconnect_count}")
            if failed_connections > 0:
                logger.warning(f"连接创建失败数: {failed_connections}")
        else:
            logger.error(f"无法创建任何客户端，失败数: {failed_connections}，可能会影响数据获取")

        # 启动后台线程异步创建剩余连接
        self.async_init_thread = threading.Thread(target=self._async_create_more_clients, daemon=True)
        self.async_init_thread.start()

    def _async_create_more_clients(self):
        """异步创建更多客户端连接"""
        try:
            # 优化：创建剩余连接，达到池大小的50%即可，避免过多连接
            target_count = min(int(self.pool_size * 0.5), 10)  # 目标50%的池大小，最多10个
            current_count = self.client_queue.qsize()
            additional_count = max(0, target_count - current_count)

            if additional_count <= 0:
                self.async_init_complete = True
                logger.info(f"连接池已达到目标大小，无需创建额外连接")
                return

            logger.info(f"后台线程开始创建额外的 {additional_count} 个连接...")

            # 创建额外连接
            successful_additional = 0
            failed_additional = 0
            for _ in range(additional_count):
                if self.client_queue.qsize() >= target_count:
                    break

                # 使用较短的超时时间
                client = self._create_new_client(timeout=3)
                if client and not self.client_queue.full():
                    try:
                        self.client_queue.put(client, block=False)
                        successful_additional += 1
                    except queue.Full:
                        try:
                            if hasattr(client, 'close'):
                                client.close()
                        except Exception:
                            pass
                        break
                else:
                    failed_additional += 1
                    # 如果连续失败太多，提前退出
                    if failed_additional >= 3:
                        logger.warning(f"后台连接创建连续失败 {failed_additional} 次，提前退出")
                        break

                # 优化：减少休眠时间，加快连接创建
                time.sleep(0.1)

            logger.info(f"后台线程完成创建额外连接，成功创建: {successful_additional}，当前连接池大小: {self.client_queue.qsize()}")
        except Exception as e:
            logger.error(f"异步创建连接失败: {e}")
        finally:
            self.async_init_complete = True

    def _get_best_server(self):
        """获取最佳服务器，使用缓存机制"""
        import time
        current_time = time.time()

        # 如果缓存有效且有最佳服务器，直接返回
        if (self.best_server and
            current_time - self.server_last_update < self.server_cache_duration):
            return self.best_server

        # 更新最佳服务器缓存
        sorted_servers = sorted(TDX_SERVERS, key=lambda x: self.server_failure_count.get(x, 0))
        self.best_server = sorted_servers[0] if sorted_servers else None
        self.server_last_update = current_time

        return self.best_server

    def _create_new_client(self, timeout: Optional[int] = None):
        """
        创建新的客户端实例 - Debian 系统底层优化版本
        增强版本：添加详细的调试信息和错误处理

        Args:
            timeout: 连接超时时间（秒），如果为None则使用默认值

        Returns:
            Optional[Quotes]: 客户端实例，如果创建失败则返回None
        """
        # 使用指定的超时时间或默认值
        actual_timeout = timeout if timeout is not None else self.timeout

        # [修复] Debian 系统优化：自适应超时
        if self.debian_optimizations.get('adaptive_timeout', False):
            # 根据历史性能调整超时时间
            avg_time = self.connection_stats.get('avg_connection_time', 0)
            if avg_time > 0:
                # 如果平均连接时间较长，适当增加超时时间
                actual_timeout = min(actual_timeout * 1.5, actual_timeout + 3)
                actual_timeout = int(actual_timeout)

        logger.debug(f"开始创建新客户端，超时时间: {actual_timeout}秒")

        # 优化：优先尝试最佳服务器，然后尝试其他服务器
        best_server = self._get_best_server()
        servers_to_try = [best_server] if best_server else []

        # 添加其他服务器作为备选
        other_servers = [s for s in TDX_SERVERS if s != best_server]
        servers_to_try.extend(other_servers[:3])  # 最多尝试4个服务器

        # 尝试连接服务器
        for host, port in servers_to_try:
            connection_start_time = time.time()
            try:
                # [修复] Debian 系统底层优化：创建客户端前的网络优化
                client_config = {
                    'market': 'std',
                    'multithread': True,
                    'heartbeat': True,
                    'bestip': False,
                    'server': (host, port),
                    'timeout': actual_timeout,
                    'quiet': True
                }

                # Debian 系统特定优化
                if self.debian_optimizations.get('use_ipv4_only', False):
                    # 强制使用 IPv4，避免 IPv6 解析延迟
                    import socket
                    original_getaddrinfo = socket.getaddrinfo

                    def ipv4_only_getaddrinfo(host, port, family=0, type=0, proto=0, flags=0):
                        return original_getaddrinfo(host, port, socket.AF_INET, type, proto, flags)

                    socket.getaddrinfo = ipv4_only_getaddrinfo

                # 创建客户端
                logger.debug(f"正在创建客户端连接到 {host}:{port}")
                client = Quotes.factory(**client_config)

                # [修复] Debian 系统优化：设置底层 socket 选项
                if hasattr(client, '_client') and hasattr(client._client, 'sock'):  # type: ignore
                    self._apply_debian_socket_optimizations(client._client.sock)  # type: ignore

                # [修复] 修复：添加连接验证，确保客户端真正可用
                try:
                    logger.debug(f"验证客户端连接 {host}:{port}")
                    # 使用轻量级测试，获取单个股票数据
                    test_result = client.quotes(symbol=["000001"])  # type: ignore
                    if test_result is None or (hasattr(test_result, 'empty') and test_result.empty):
                        logger.warning(f"客户端连接 {host}:{port} 验证失败：返回数据为空")
                        if hasattr(client, 'close'):
                            client.close()
                        continue
                    logger.debug(f"客户端连接 {host}:{port} 验证成功")
                except Exception as test_e:
                    logger.warning(f"客户端连接 {host}:{port} 验证失败: {test_e}")
                    if hasattr(client, 'close'):
                        client.close()
                    continue

                connection_time = time.time() - connection_start_time

                # 更新统计信息
                self.server_success_count[(host, port)] = self.server_success_count.get((host, port), 0) + 1
                self.creation_count += 1
                self.active_clients += 1
                self.max_active_clients = max(self.max_active_clients, self.active_clients)

                # [修复] 更新性能统计
                self.connection_stats['total_created'] += 1
                old_avg = self.connection_stats['avg_connection_time']
                total_created = self.connection_stats['total_created']
                self.connection_stats['avg_connection_time'] = (old_avg * (total_created - 1) + connection_time) / total_created

                # 如果成功连接且不是最佳服务器，更新最佳服务器
                if (host, port) != self.best_server:
                    self.best_server = (host, port)
                    self.server_last_update = time.time()

                logger.debug(f"[成功] 成功创建并验证客户端连接 {host}:{port}，耗时: {connection_time:.3f}s")
                return client
            except Exception as e:
                # 更新失败统计
                self.server_failure_count[(host, port)] = self.server_failure_count.get((host, port), 0) + 1
                logger.warning(f"连接服务器 {host}:{port} 失败: {e}")

                # 如果是网络相关错误，记录更详细的信息
                import traceback
                if "timeout" in str(e).lower() or "connection" in str(e).lower():
                    logger.debug(f"网络连接错误详情: {traceback.format_exc()}")

        self.connection_stats['total_failed'] += 1
        logger.error("[失败] 所有服务器连接失败，无法创建客户端")

        # [修复] 增强：输出详细的失败统计信息
        logger.error("服务器连接失败统计:")
        for (host, port), failures in self.server_failure_count.items():
            successes = self.server_success_count.get((host, port), 0)
            logger.error(f"  {host}:{port} - 成功: {successes}, 失败: {failures}")

        return None

    def _apply_debian_socket_optimizations(self, sock):
        """
        应用 Debian 系统的 socket 底层优化

        Args:
            sock: socket 对象
        """
        try:
            import socket

            # [修复] 启用 TCP keepalive
            if self.debian_optimizations.get('socket_keepalive', False):
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)

                # 设置 keepalive 参数 (Linux 特定)
                if hasattr(socket, 'TCP_KEEPIDLE'):
                    sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPIDLE, 60)  # 60秒后开始探测
                if hasattr(socket, 'TCP_KEEPINTVL'):
                    sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPINTVL, 10)  # 每10秒探测一次
                if hasattr(socket, 'TCP_KEEPCNT'):
                    sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPCNT, 3)    # 探测3次失败后断开

            # [修复] 禁用 Nagle 算法，减少延迟
            if self.debian_optimizations.get('socket_nodelay', False):
                sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)

            # [修复] 设置 socket 超时
            socket_timeout = self.debian_optimizations.get('socket_timeout', self.timeout)
            if socket_timeout:
                sock.settimeout(socket_timeout)

            # [修复] 设置 TCP 用户超时 (Linux 特定)
            tcp_user_timeout = self.debian_optimizations.get('tcp_user_timeout', 15000)
            if hasattr(socket, 'TCP_USER_TIMEOUT') and tcp_user_timeout:
                sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_USER_TIMEOUT, tcp_user_timeout)  # type: ignore

            # [修复] 设置接收和发送缓冲区大小
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 65536)  # 64KB 接收缓冲区
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 65536)  # 64KB 发送缓冲区

            self.connection_stats['debian_optimizations_applied'] += 1
            logger.debug("[成功] Debian socket 优化已应用")

        except Exception as e:
            logger.debug(f"[警告] 应用 Debian socket 优化失败: {e}")
            # 不抛出异常，优化失败不应影响连接

    def get_client(self):
        """
        从连接池获取客户端，如果池为空则创建新客户端
        增强版本：添加详细的调试信息和错误恢复机制

        Returns:
            Optional[Quotes]: 行情客户端实例
        """
        # [修复] 调试信息：记录获取客户端的尝试
        logger.debug(f"尝试获取客户端: 队列大小={self.client_queue.qsize()}, 活跃连接={self.active_clients}, 池大小={self.pool_size}")

        # 尝试从队列获取客户端（非阻塞）
        try:
            client = self.client_queue.get(block=False)
            self.reuse_count += 1
            # [修复] 底层优化：成功获取连接，重置失败计数
            self.consecutive_get_failures = 0
            logger.debug(f"[成功] 成功从队列获取客户端，复用次数: {self.reuse_count}")
            return client
        except queue.Empty:
            logger.debug("队列为空，需要创建新客户端")

        # 队列为空，需要创建新客户端或等待
        with self.lock:
            # [修复] 修复：先尝试创建新客户端，再检查连接数限制
            if self.active_clients < self.pool_size:
                logger.debug(f"创建新客户端: 当前活跃连接={self.active_clients}, 池大小={self.pool_size}")
                new_client = self._create_new_client()
                if new_client:
                    logger.debug("[成功] 成功创建新客户端")
                    return new_client
                else:
                    logger.warning("[失败] 创建新客户端失败")
                    # 创建失败，增加失败计数
                    self.consecutive_get_failures += 1

            # 检查是否已达到最大连接数或创建失败
            if self.active_clients >= self.pool_size:
                # 控制警告日志输出频率
                if self.max_clients_warning_count == 0:
                    logger.warning(f"已达到最大连接数 {self.pool_size}，等待连接释放")
                elif self.max_clients_warning_count % 100 == 0:
                    logger.debug(f"已达到最大连接数 {self.pool_size}，等待连接释放 (第 {self.max_clients_warning_count} 次)")
                self.max_clients_warning_count += 1

                # 优化：使用更短的轮询间隔和更少的轮询次数
                for attempt in range(5):  # 减少轮询次数从10到5
                    try:
                        client = self.client_queue.get(block=True, timeout=0.2)  # 减少超时从0.5到0.2
                        logger.debug(f"[成功] 等待后获取到客户端，尝试次数: {attempt + 1}")
                        return client
                    except queue.Empty:
                        logger.debug(f"等待客户端超时，尝试次数: {attempt + 1}")
                        continue

                # 轮询超时，无法获取连接
                logger.error("等待连接超时，所有尝试都失败")

            # [修复] 底层优化：连接泄漏检测和自动恢复
            self.consecutive_get_failures += 1
            logger.warning(f"获取客户端失败，连续失败次数: {self.consecutive_get_failures}")
            self._check_and_recover_connection_leak()

            return None

    def close_client(self, client: Quotes) -> None:
        """
        将客户端归还到连接池，优化版本减少健康检查

        Args:
            client: 客户端实例
        """
        if client is None:
            return

        # 优化：减少健康检查频率，只在连接复用次数过高时检查
        need_health_check = (self.reuse_count % 100 == 0)  # 每100次复用检查一次

        if need_health_check and not self._check_client_health(client):
            # 如果连接不健康，关闭它
            try:
                if hasattr(client, 'close'):
                    client.close()  # type: ignore
                with self.lock:
                    self.active_clients -= 1
                logger.debug(f"连接不健康，关闭客户端，当前活跃连接数: {self.active_clients}")

                # 尝试创建新连接并添加到池中
                if not self.client_queue.full():
                    new_client = self._create_new_client()
                    if new_client:
                        self.client_queue.put(new_client)
                        logger.debug(f"创建新连接替代不健康连接，当前连接池大小: {self.client_queue.qsize()}")
            except Exception as e:
                logger.warning(f"关闭不健康客户端异常: {e}")
            return

        # 检查连接池是否已满
        if self.client_queue.full():
            # 如果已满，关闭客户端
            try:
                if hasattr(client, 'close'):
                    client.close()  # type: ignore
                with self.lock:
                    self.active_clients -= 1
                logger.debug(f"连接池已满，关闭客户端，当前活跃连接数: {self.active_clients}")
            except Exception as e:
                logger.warning(f"关闭客户端异常: {e}")
        else:
            # 归还到连接池
            try:
                self.client_queue.put(client)
                logger.debug(f"归还客户端到连接池，当前连接池大小: {self.client_queue.qsize()}")
            except Exception as e:
                logger.warning(f"归还客户端到连接池异常: {e}")
                try:
                    if hasattr(client, 'close'):
                        client.close()  # type: ignore
                except Exception:
                    pass

    def _check_client_health(self, client: Quotes) -> bool:
        """
        检查客户端连接是否健康（简化版本）

        Args:
            client: 客户端实例

        Returns:
            bool: 连接是否健康
        """
        try:
            # 优化：使用更轻量的健康检查，避免实际数据请求
            # 只检查客户端对象是否有效，不进行网络请求
            if client is None:
                return False

            # 检查客户端是否有基本属性
            if not hasattr(client, 'quotes'):
                return False

            # 简单的连接状态检查，避免网络请求
            return True

        except Exception as e:
            logger.debug(f"连接健康检查失败: {e}")
            return False

    def close_all(self) -> None:
        """
        关闭所有客户端连接
        """
        logger.info(f"关闭所有客户端连接，连接池大小: {self.client_queue.qsize()}, 活跃连接数: {self.active_clients}")

        # 关闭连接池中的所有连接
        closed_count = 0
        while not self.client_queue.empty():
            try:
                client = self.client_queue.get(block=False)
                try:
                    client.close()
                    closed_count += 1
                except Exception as e:
                    logger.warning(f"关闭客户端连接异常: {e}")
            except queue.Empty:
                break

        with self.lock:
            self.active_clients = 0

        logger.info(f"成功关闭 {closed_count} 个客户端连接")

    def cleanup(self) -> None:
        """
        清理TDX连接池资源
        """
        logger.info("开始清理TDX连接池资源...")
        self.close_all()
        logger.info("TDX连接池资源清理完成")
        logger.info(f"客户端池统计 - 创建: {self.creation_count}, 复用: {self.reuse_count}, 最大活跃数: {self.max_active_clients}")

        # 输出服务器统计信息
        server_stats = []
        for server, success in self.server_success_count.items():
            failure = self.server_failure_count.get(server, 0)
            total = success + failure
            success_rate = (success / total * 100) if total > 0 else 0
            server_stats.append((server, success, failure, success_rate))

        server_stats.sort(key=lambda x: x[3], reverse=True)
        for server, success, failure, success_rate in server_stats:
            if success + failure > 0:
                logger.info(f"服务器 {server[0]}:{server[1]} - 成功: {success}, 失败: {failure}, 成功率: {success_rate:.2f}%")

    def _check_and_recover_connection_leak(self):
        """
        [修复] 底层优化：检测连接泄漏并自动恢复

        当连续无法获取连接时，检测是否存在连接泄漏，并尝试自动恢复
        """
        current_time = time.time()

        # 检查是否需要进行泄漏检测
        if (current_time - self.last_leak_check_time < self.leak_check_interval and
            self.consecutive_get_failures < self.connection_leak_threshold):
            return

        self.last_leak_check_time = current_time

        try:
            # 获取当前连接池状态
            queue_size = self.client_queue.qsize()
            active_clients = self.active_clients

            logger.warning(f"连接泄漏检测: 队列大小={queue_size}, 活跃连接={active_clients}, "
                         f"连续失败={self.consecutive_get_failures}")

            # 如果连续失败次数超过阈值，执行强制恢复
            if self.consecutive_get_failures >= self.connection_leak_threshold:
                logger.error(f"[警报] 检测到连接泄漏！连续失败 {self.consecutive_get_failures} 次，执行强制恢复")
                self._force_recover_connection_pool()

            # 如果队列为空但活跃连接数异常，尝试重置计数器
            elif queue_size == 0 and active_clients > self.pool_size:
                logger.warning(f"[修复] 检测到连接计数异常，重置活跃连接数: {active_clients} -> {self.pool_size}")
                with self.lock:
                    self.active_clients = min(self.active_clients, self.pool_size)

        except Exception as e:
            logger.error(f"[失败] 连接泄漏检测失败: {e}")

    def _force_recover_connection_pool(self):
        """
        [修复] 底层优化：强制恢复连接池

        当检测到严重的连接泄漏时，强制清理并重建连接池
        """
        try:
            logger.warning("[修复] 开始强制恢复连接池...")

            # 1. 清空现有队列
            cleared_count = 0
            while not self.client_queue.empty():
                try:
                    client = self.client_queue.get_nowait()
                    if hasattr(client, 'close'):
                        client.close()
                    cleared_count += 1
                except queue.Empty:
                    break
                except Exception as e:
                    logger.debug(f"清理连接时出错: {e}")

            # 2. 重置计数器
            with self.lock:
                self.active_clients = 0
                self.consecutive_get_failures = 0
                self.max_clients_warning_count = 0

            # 3. 预创建一些连接
            created_count = 0
            target_connections = min(3, self.pool_size)  # 预创建3个连接

            for _ in range(target_connections):
                try:
                    client = self._create_new_client()
                    if client:
                        self.client_queue.put(client)
                        created_count += 1
                except Exception as e:
                    logger.debug(f"预创建连接失败: {e}")
                    break

            logger.info(f"[成功] 连接池强制恢复完成: 清理={cleared_count}, 重建={created_count}")

        except Exception as e:
            logger.error(f"[失败] 强制恢复连接池失败: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """
        获取客户端池统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        with self.lock:
            stats = {
                "active_clients": self.active_clients,
                "max_active_clients": self.max_active_clients,
                "creation_count": self.creation_count,
                "reuse_count": self.reuse_count,
                "pool_size": self.pool_size,
                "queue_size": self.client_queue.qsize(),
                "consecutive_get_failures": self.consecutive_get_failures,  # [修复] 新增
                "max_clients_warning_count": self.max_clients_warning_count,  # [修复] 新增
                "server_stats": {}
            }

            # 添加服务器统计
            for server, success in self.server_success_count.items():
                failure = self.server_failure_count.get(server, 0)
                total = success + failure
                success_rate = (success / total * 100) if total > 0 else 0
                stats["server_stats"][f"{server[0]}:{server[1]}"] = {
                    "success": success,
                    "failure": failure,
                    "success_rate": success_rate
                }

            return stats


# SharedMemoryMetrics类已删除 - 不再使用共享内存

class MarketData:
    """市场数据存储类"""

    def __init__(self):
        """初始化市场数据存储"""
        self._ticks = {}  # 存储最新的tick数据
        self._lock = threading.RLock()  # 数据锁，使用RLock以支持可重入

    def update_tick(self, code: str, tick: Dict[str, Any]) -> None:
        """
        更新tick数据

        Args:
            code: 股票代码
            tick: tick数据
        """
        with self._lock:
            self._ticks[code] = tick

    def update_ticks_batch(self, ticks_dict: Dict[str, Dict[str, Any]]) -> None:
        """
        批量更新tick数据

        Args:
            ticks_dict: 股票代码到tick数据的映射
        """
        with self._lock:
            self._ticks.update(ticks_dict)

    def get_tick(self, code: str) -> Optional[Dict[str, Any]]:
        """
        获取tick数据

        Args:
            code: 股票代码

        Returns:
            Optional[Dict[str, Any]]: tick数据
        """
        with self._lock:
            return self._ticks.get(code)

    def get_all_ticks(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有tick数据

        Returns:
            Dict[str, Dict[str, Any]]: 所有tick数据
        """
        with self._lock:
            return self._ticks.copy()


class TickDataInsertionStats:
    """Tick数据插入统计"""

    def __init__(self):
        self.total_attempts = 0
        self.successful_inserts = 0
        self.failed_inserts = 0
        self.total_records = 0
        self.total_time = 0.0
        self.last_reset = time.time()

    def record_attempt(self, record_count: int, duration: float, success: bool):
        """记录一次插入尝试"""
        self.total_attempts += 1
        self.total_records += record_count
        self.total_time += duration

        if success:
            self.successful_inserts += 1
        else:
            self.failed_inserts += 1

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        if self.total_attempts == 0:
            return {"message": "暂无统计数据"}

        return {
            "total_attempts": self.total_attempts,
            "success_rate": self.successful_inserts / self.total_attempts * 100,
            "total_records": self.total_records,
            "avg_records_per_batch": self.total_records / self.total_attempts,
            "avg_duration_per_batch": self.total_time / self.total_attempts,
            "records_per_second": self.total_records / self.total_time if self.total_time > 0 else 0,
            "uptime_minutes": (time.time() - self.last_reset) / 60
        }

    def reset(self):
        """重置统计"""
        self.__init__()


class MarketDataFetcher:
    """市场数据获取器"""

    def __init__(self, config_path: str = "config/main.toml", market_data_config_path: str = "config/market_data_fetcher.toml"):
        """
        初始化市场数据获取器

        参数:
            config_path: 全局配置文件路径
            market_data_config_path: 市场数据获取器配置文件路径
        """
        # 设置日志记录器
        self.logger = get_logger(f"MarketDataFetcher_{os.getpid()}_main") # 主logger，常规配置

        # 初始化日志记录器

        self.process_logger = setup_logger(
            name=f"MarketDataFetcher_ProcessLog_{os.getpid()}",
            level='info',  # Or from config
            log_file=None, # Typically None when using QueueHandler for subprocess/thread internal logging
            console=False  # Typically False for subprocess/thread internal logging
        )

        # 加载主配置到 self.config，这是最先进行的配置加载之一
        self.config_path = config_path
        self.config = self._load_config() # self.config 将持有主配置
        # logger.info(f"主配置文件加载结果: {self.config is not None}") # 这行原先在后面，但逻辑上 self.config 在这里已填充

        # 加载市场数据获取器配置
        # logger.info(f"加载市场数据获取器配置文件: {market_data_config_path}") # 这行原先在后面
        self.market_data_config_path = market_data_config_path
        self.market_data_config = self._load_market_data_config()
        # logger.info(f"市场数据获取器配置文件加载结果: {self.market_data_config is not None}") # 这行原先在后面

        market_data_config_specific = self.market_data_config.get("market_data_fetcher", {})
        if not market_data_config_specific:
            market_data_config_specific = self.config.get("market_data_fetcher", {})

        # [重构] 专注于tick数据获取和数据库存储
        self.logger.info("[成功] MarketDataFetcher重构完成：专注于tick数据获取和数据库存储")

        # 删除K线计算相关组件 - 专注于tick数据获取和存储
        # 理由：K线计算增加了系统复杂度，可以通过数据库查询或独立服务实现

        self.stop_event = threading.Event()

        # 添加锁对象，用于线程同步
        self.lock = threading.RLock()
        self.data_lock = threading.RLock()

        # 设置时区
        self.timezone = 'Asia/Shanghai'

        # [修复] 交易时间设置（提前更多时间开始获取数据，确保系统正常运行）
        self.am_open = "09:29:00"  # 提前15分钟，确保集合竞价期间也能获取数据
        self.am_close = "11:30:00"
        self.pm_open = "13:00:00"  # 提前15分钟，确保午盘开始前准备就绪
        self.pm_close = "15:05:00"  # 延后5分钟，确保收盘数据完整获取
        self.lunch_break_start = "11:30:00"
        self.lunch_break_end = "13:00:00"  # 提前1分钟，原为13:00:00

        # 获取市场数据获取配置
        market_data_config = self.market_data_config.get("market_data_fetcher", {})
        if not market_data_config:
            # 如果专用配置文件中没有配置，则尝试从主配置文件中获取
            logger.info("专用配置文件中没有market_data_fetcher配置，尝试从主配置文件中获取")
            market_data_config = self.config.get("market_data_fetcher", {})
            logger.info(f"从主配置文件中获取market_data_fetcher配置结果: {market_data_config != {}}")

        self.fetch_interval = market_data_config.get("fetch_interval", 3)  # 获取间隔，默认3秒
        self.batch_size = market_data_config.get("batch_size", 80)  # 每批次获取的股票数量，固定为80
        self.max_retries = market_data_config.get("max_retries", 3)  # 最大重试次数，默认3
        self.retry_delay = market_data_config.get("retry_delay", 0.5)  # 重试延迟，默认0.5秒
        self.timeout = market_data_config.get("timeout", 5)  # 连接超时时间，默认5秒

        logger.info(f"配置参数: fetch_interval={self.fetch_interval}, batch_size={self.batch_size}, max_retries={self.max_retries}, retry_delay={self.retry_delay}, timeout={self.timeout}")

        # TimescaleDB数据库设置 - 使用TimescaleDB时序数据库
        # 从配置文件读取数据库配置
        database_config = self.config.get("database", {})
        self.use_timescaledb = database_config.get("use_timescaledb", True)  # 使用TimescaleDB
        self.timescaledb_pool: Optional[Any] = None
        self.save_tick_to_db = True  # 保存tick数据到TimescaleDB

        # 从配置读取TimescaleDB服务器信息
        timescaledb_host = database_config.get("timescaledb_host", "********")
        timescaledb_port = database_config.get("timescaledb_port", 6668)
        logger.info(f"[架构] 使用TimescaleDB时序数据库，服务器: {timescaledb_host}:{timescaledb_port}")
        logger.info("[数据] 数据将直接写入TimescaleDB，支持高性能时序数据存储和查询")

        # 先计算线程数（连接池初始化需要用到）
        config_max_threads = market_data_config.get("max_threads", 50)  # 配置中的最大线程数，默认50
        self.max_threads = self._calculate_optimal_thread_count(config_max_threads)
        logger.info(f"计算得出最优线程数: {self.max_threads}")

        # 简化架构: 使用TimescaleDB时序数据库
        # logger.info("[架构] 简化架构: 数据获取 + 实时存储")
        # logger.info("[性能] 专注于高性能数据获取和存储")


        # 初始化股票列表（线程数已在连接池初始化前计算）
        self.stock_list = self._load_stock_list()
        # [修复] 修复：确保股票代码是字符串类型，过滤空值
        self.stock_codes = [str(stock.get("code", "")) for stock in self.stock_list if stock.get("code")]
        self.stock_codes = [code for code in self.stock_codes if code]  # 过滤空字符串
        # 将股票代码列表转换为集合，用于快速查找
        self.stock_codes_set = set(self.stock_codes)

        # [调试] 添加股票列表加载状态日志
        self.logger.info(f"[初始化] 股票列表加载完成: {len(self.stock_list)} 只股票")
        self.logger.info(f"[初始化] 有效股票代码: {len(self.stock_codes)} 个")
        if len(self.stock_codes) > 0:
            self.logger.info(f"[初始化] 前5个股票代码: {self.stock_codes[:5]}")
        else:
            self.logger.error(f"[错误] 没有有效的股票代码！股票列表: {self.stock_list}")

        # 获取监控配置
        monitor_config = self.config.get("monitor", {}).get("market_data_fetcher", {})
        self.initialization_time = monitor_config.get("initialization_time", "09:05:00")
        self.start_time = monitor_config.get("start_time", "09:08:00")
        self.market_open_time = monitor_config.get("market_open_time", "09:29:00")  # 提前1分钟，原为09:30:00
        self.market_close_time = monitor_config.get("market_close_time", "15:00:00")
        self.lunch_break_start = monitor_config.get("lunch_break_start", "11:30:00")
        self.lunch_break_end = monitor_config.get("lunch_break_end", "12:59:00")  # 提前1分钟，原为13:00:00

        # 初始化数据存储
        self.market_data = MarketData()

        # 初始化数据锁
        self.data_lock = threading.RLock()

        # 初始化线程同步事件
        self.all_history_data_loaded_event = threading.Event()
        self.shm_init_complete_event = threading.Event()
        self.latest_tick_data_lock = threading.RLock()
        self.latest_tick_data = {}

        # 初始化客户端池 - 使用优化后的共享连接池
        logger.info(f"初始化客户端池，池大小: {self.max_threads}, 超时时间: {self.timeout}秒")
        self.client_pool = QuotesClientPool(pool_size=self.max_threads, timeout=self.timeout)
        # 输出连接池统计信息
        pool_stats = self.client_pool.get_stats()
        logger.info(f"客户端池初始化完成，当前连接数: {pool_stats['queue_size']}, 最大连接数: {pool_stats['pool_size']}")

        # 初始化持久化线程池，避免每次创建
        from concurrent.futures import ThreadPoolExecutor
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_threads)
        logger.info(f"创建持久化线程池，最大线程数: {self.max_threads}")

        # 初始化日志计数器
        self._log_counter = 0

        # [修复] 优化：初始化性能监控指标
        self._init_performance_metrics()

        # 初始化股票列表
        #self.stock_list = self._load_stock_list()
        #self.stock_codes = [stock.get("code") for stock in self.stock_list]
        self.stock_batches = self._create_stock_batches()

        # 初始化线程
        self.fetch_thread: Optional[threading.Thread] = None
        self.save_thread: Optional[threading.Thread] = None
        self.schedule_thread: Optional[threading.Thread] = None

        # 初始化数据队列
        self.data_queue = queue.Queue()


        # 存储每只股票上一次的成交量数据，用于筛选成交量有增加的数据
        self.last_volume_data = {}

        # 初始化数据结构
        self.tick_data = {}  # 最新的tick数据

        # 初始化运行状态
        self.running = False

        # 初始化数据库连接 - 使用统一的数据库管理器
        self.db_manager: Optional[Any] = None
        self.timescaledb_initialized = False

        if self.use_timescaledb:
            success = self._init_db_manager()
            if success:
                self.timescaledb_initialized = True

        # K线生成功能已删除
        logger.info("K线生成功能已删除")

        # 初始化最后获取和保存时间
        self.last_fetch_time: Optional[datetime] = None
        self.last_save_time: Optional[datetime] = None

        # 初始化错误跟踪
        self.failed_stocks = set()  # 记录失败的股票代码
        self.error_counts = {}  # 记录每个股票的错误次数
        self.consecutive_errors = 0  # 连续错误次数
        self.max_consecutive_errors = 5  # 最大连续错误次数，超过则重新初始化客户端池

        # 用于避免重复警告的集合
        self._time_adjustment_warnings = set()  # 记录已经警告过的时间调整
        
        # 性能指标
        self.fetch_count = 0
        self.successful_batches = 0
        self.failed_batches = 0
        self.total_fetch_time = 0
        self.min_fetch_time = float('inf')
        self.max_fetch_time = 0

        # 删除K线计算配置 - 专注于tick数据获取和存储
        # 理由：简化系统架构，K线计算可以通过数据库查询或独立服务实现

        # 初始化性能统计
        self.insertion_stats = TickDataInsertionStats()

        logger.info("市场数据获取器初始化完成")

        self.all_history_data_loaded_event = threading.Event() # Initialize the event

        # 记录当前日期，用于检测日期变化时清理警告集合
        self._current_date = pd.Timestamp.now().date()

        # 初始化时间调整统计计数器
        self._premarket_adjustment_count = 0      # 盘前时间调整次数
        self._lunch_adjustment_count = 0          # 午休时间调整次数
        self._aftermarket_adjustment_count = 0    # 盘后时间调整次数
        self._nontrading_adjustment_count = 0     # 非交易时间调整次数

        # [启动] 性能优化：缓存机制
        self._invalid_time_cache = {}  # 缓存无效时间的标准化结果
        self._last_cache_clear = time.time()  # 上次清理缓存的时间
        self._cache_clear_interval = 300  # 缓存清理间隔（5分钟）

        # [启动] 性能优化：批量处理配置
        self._batch_write_threshold = 5000  # 批量写入阈值（增加到2000）
        self._pending_data_buffer = []  # 待写入数据缓冲区
        self._last_batch_write = time.time()  # 上次批量写入时间
        self._batch_write_interval = 0.8  # 批量写入间隔（0.8秒）



        # [启动] 性能优化：无效时间记录过滤
        self._known_invalid_stocks = {}  # 已知的无效时间股票
        self._invalid_stock_skip_count = 0  # 跳过的无效股票计数

        # [启动] 性能优化：数据库写入优化
        self._db_write_batch_size = 10000  # 数据库批量写入大小（优化：从5000提升到10000）
        self._enable_async_write = True  # 启用异步写入
        self._tick_data_buffer = []  # Tick数据缓冲区
        self._buffer_max_size = 15000  # 缓冲区最大大小
        self._buffer_flush_interval = 2.0  # 缓冲区刷新间隔（秒）
        self._last_buffer_flush = time.time()  # 上次缓冲区刷新时间

        logger.info("[性能优化] 性能优化配置已启用:")
        logger.info(f"   - 缓存机制: 清理间隔 {self._cache_clear_interval}秒")
        logger.info(f"   - 批量写入: 阈值 {self._batch_write_threshold}, 间隔 {self._batch_write_interval}秒")
        logger.info(f"   - 数据库优化: 批量大小 {self._db_write_batch_size}, 异步写入 {self._enable_async_write}")

        # 初始化时间调整日志限制器
        self._last_premarket_log = 0
        self._last_lunch_log = 0
        self._last_aftermarket_log = 0
        self._last_nontrading_log = 0

        # CSV备选方案设置（仅在数据库完全不可用时使用）
        self.use_csv_backup = False
        self.csv_backup_dir: Optional[Any] = None

        # 注册进程退出时的清理函数
        import atexit
        atexit.register(self._cleanup_persistent_connections)
        atexit.register(self._log_time_adjustment_summary)

    def _init_db_manager(self) -> bool:
        """
        初始化数据库管理器

        Returns:
            bool: 是否成功初始化
        """
        self.logger.info("正在初始化数据库管理器...")

        try:
            # 使用统一的数据库管理器
            self.db_manager = get_db_manager()

            # 测试连接
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1 as test_value")
                    result = cursor.fetchone()

                    # 处理RealDictCursor返回字典的情况
                    if isinstance(result, dict):
                        test_value = result.get('test_value')
                    else:
                        test_value = result[0] if result else None

                    if result and test_value == 1:
                        self.logger.info("[成功] 数据库管理器初始化成功")
                        return True
                    else:
                        raise Exception("连接测试查询失败")

        except Exception as e:
            self.logger.warning(f"数据库管理器初始化失败: {e}")
            # 不返回False，而是继续运行但禁用数据库功能
            self.db_manager: Optional[Any] = None
            return False

    def _write_tick_data_with_persistent_connection(self, processed_data: List[Dict]) -> bool:
        """
        使用数据库管理器写入tick数据

        Args:
            processed_data: 处理后的tick数据列表

        Returns:
            bool: 写入是否成功
        """
        if not processed_data:
            return True

        if not self.db_manager:
            self.logger.warning("数据库管理器未初始化，跳过数据插入")
            return False

        # 数据验证和清理
        validated_data = self._validate_and_clean_tick_data(processed_data)
        if not validated_data:
            self.logger.warning("所有数据验证失败，跳过插入")
            return False

        try:
            # [启动] 性能优化：使用优化的批量插入
            success = self.db_manager.insert_many(
                'stock_tick_data',
                validated_data,
                on_conflict="(trade_time, stock_code) DO UPDATE SET price = EXCLUDED.price, volume = EXCLUDED.volume",
                batch_size=self._db_write_batch_size
            )

            if success:
                return True
            else:
                self.logger.error("数据库管理器插入失败")
                return False

        except Exception as e:
            self.logger.error(f"数据插入失败: {e}")
            return False

    def _buffer_and_batch_write_tick_data(self, processed_data: List[Dict]) -> bool:
        """
        [优化] 缓冲区批量写入Tick数据 - 确保同一时间获取的数据合并后存储

        优化策略：
        1. 将同一批次获取的tick数据作为一个整体添加到缓冲区
        2. 按时间戳对数据进行分组，确保同一时间点的数据一起处理
        3. 使用批量写入提升数据库性能
        4. 智能缓冲区管理，平衡内存使用和写入频率

        Args:
            processed_data: 处理后的tick数据列表（同一时间获取的数据）

        Returns:
            bool: 操作是否成功
        """
        try:
            if not processed_data:
                return True

            # [修复] 删除不存在的数据库字段 - 根据实际数据库表结构
            # 数据库表 stock_tick_data 不包含 batch_timestamp 和 fetch_time 字段

            # 添加整批数据到缓冲区（保持同一时间获取的数据的完整性）
            self._tick_data_buffer.extend(processed_data)
            current_time = time.time()

            # [优化] 智能刷新策略：确保同一批次数据的完整性
            should_flush = (
                len(self._tick_data_buffer) >= self._buffer_max_size or  # 缓冲区满
                (current_time - self._last_buffer_flush) >= self._buffer_flush_interval  # 超时
            )

            if should_flush:
                return self._flush_tick_data_buffer()
            else:
                # 数据已缓冲，返回成功
                return True

        except Exception as e:
            self.logger.error(f"缓冲区批量写入失败: {e}")
            return False

    def _flush_tick_data_buffer(self) -> bool:
        """
        [优化] 刷新Tick数据缓冲区到数据库 - 确保数据完整性和一致性

        优化策略：
        1. 按批次时间戳对数据进行分组，确保同一时间获取的数据一起写入
        2. 使用事务确保数据写入的原子性
        3. 智能重试机制处理临时性错误
        4. 详细的性能监控和日志记录

        Returns:
            bool: 刷新是否成功
        """
        try:
            if not self._tick_data_buffer:
                return True

            # [修复] 简化数据处理逻辑 - 删除不存在的批次时间戳字段
            buffer_data = self._tick_data_buffer.copy()
            buffer_size = len(buffer_data)

            # 清空缓冲区
            self._tick_data_buffer.clear()
            self._last_buffer_flush = time.time()

            # [修复] 直接批量写入数据库 - 简化处理逻辑
            success = self._write_tick_data_with_persistent_connection(buffer_data)

            if success:
                total_written = buffer_size
                all_success = True
            else:
                total_written = 0
                all_success = False

            if all_success:
                self.logger.debug(f"[成功] 缓冲区刷新完成: {total_written} 条数据")
            else:
                self.logger.error(f"[失败] 缓冲区刷新失败: {buffer_size} 条数据")

            return all_success

        except Exception as e:
            self.logger.error(f"刷新缓冲区失败: {e}")
            return False

    def _validate_and_clean_tick_data(self, processed_data: List[Dict]) -> List[Dict]:
        """
        验证和清理tick数据

        Args:
            processed_data: 原始处理后的数据

        Returns:
            List[Dict]: 清理后的有效数据
        """
        valid_data = []
        invalid_count = 0

        for data in processed_data:
            try:
                # 基本字段验证
                if not self._validate_basic_fields(data):
                    invalid_count += 1
                    continue

                # 数值范围验证
                if not self._validate_numeric_ranges(data):
                    invalid_count += 1
                    continue

                # 时间有效性验证
                if not self._validate_time_field(data):
                    invalid_count += 1
                    continue

                valid_data.append(data)

            except Exception as e:
                self.logger.debug(f"数据验证失败: {e}")
                invalid_count += 1

        if invalid_count > 0:
            self.logger.debug(f"过滤掉 {invalid_count} 条无效数据")

        return valid_data

    def _validate_basic_fields(self, data: Dict) -> bool:
        """验证基本字段"""
        required_fields = ['trade_time', 'stock_code', 'price', 'volume']

        for field in required_fields:
            if field not in data or data[field] is None:
                return False

        # 股票代码不能为空
        if not str(data.get('stock_code', '')).strip():
            return False

        return True

    def _validate_numeric_ranges(self, data: Dict) -> bool:
        """验证数值范围"""
        try:
            # 价格必须为正数
            price = float(data.get('price', 0))
            if price <= 0 or price > 10000:  # 价格不能超过10000元
                return False

            # 成交量必须为非负数
            volume = int(data.get('volume', 0))
            if volume < 0:
                return False

            # 当前成交量必须为非负数
            cur_vol = int(data.get('cur_vol', 0))
            if cur_vol < 0:
                return False

            # 成交金额必须为非负数
            amount = float(data.get('amount', 0))
            if amount < 0:
                return False

            # 开盘价、最高价、最低价、昨收价的基本验证
            for field in ['open', 'high', 'low', 'last_close']:
                value = float(data.get(field, 0))
                if value <= 0 or value > 10000:
                    return False

            # 最高价不能低于最低价
            high = float(data.get('high', 0))
            low = float(data.get('low', 0))
            if high < low:
                return False

            return True

        except (ValueError, TypeError):
            return False

    def _validate_time_field(self, data: Dict) -> bool:
        """验证时间字段"""
        try:
            trade_time = data.get('trade_time')

            # 检查trade_time是否为None
            if trade_time is None:
                return False

            # 如果是字符串，尝试转换
            if isinstance(trade_time, str):
                trade_time = pd.to_datetime(trade_time)

            # 检查时间是否在合理范围内
            now = pd.Timestamp.now()

            # 检查trade_time是否为有效的时间戳
            if pd.isna(trade_time):
                return False

            # 不能是未来时间（允许5分钟的时钟偏差）
            if trade_time > now + pd.Timedelta(minutes=5):
                return False

            # 不能超过7天前的数据
            if trade_time < now - pd.Timedelta(days=7):
                return False

            return True

        except Exception:
            return False

    def _cleanup_persistent_connections(self):
        """
        清理数据库连接资源
        """
        try:
            # 数据库管理器会自动管理连接池，无需手动清理
            if hasattr(self, 'db_manager') and self.db_manager:
                self.logger.info("数据库管理器连接将自动清理")

        except Exception as e:
            self.logger.error(f"清理资源时发生异常: {e}")

    def _cleanup_memory_periodically(self):
        """定期清理内存缓存"""
        try:
            if not hasattr(self, '_metrics'):
                return

            current_time = time.time()
            # 每小时清理一次过期数据
            if current_time - self._metrics.last_cleanup_time >= 3600:
                # 使用数据库缓存清理机制
                if hasattr(self, 'db_cache_manager') and self.db_cache_manager:
                    cleaned_count = self.db_cache_manager.cleanup_expired_data()
                    if cleaned_count > 0:
                        self.logger.info(f"🧹 定期清理: 清理了 {cleaned_count} 条过期缓存数据")

                self._metrics.last_cleanup_time = current_time

        except Exception as e:
            self.logger.error(f"定期清理内存失败: {e}")



    # K线生成已改为应用层共享内存实现

    def _init_csv_backup(self) -> None:
        """初始化CSV备选方案"""
        try:
            # 创建CSV备份目录
            self.csv_backup_dir = "data/tick_backup"
            os.makedirs(self.csv_backup_dir, exist_ok=True)

            logger.info(f"[成功] CSV备选方案初始化成功: {self.csv_backup_dir}")
            logger.info("将使用CSV文件保存Tick数据作为备份")

        except Exception as e:
            logger.error(f"[失败] CSV备选方案初始化失败: {e}")
            self.use_csv_backup = False

    # K线生成已改为应用层共享内存实现









    # K线数据保存已改为应用层共享内存实现

    def _save_local_temp_to_persistent(self, local_data: List[Dict[str, Any]]) -> bool:
        """
        将本地内存数据保存到持久表

        Args:
            local_data: 本地内存数据列表

        Returns:
            bool: 保存是否成功
        """
        try:
            if not local_data:
                return True

            # 使用数据库管理器保存数据
            if self.use_timescaledb and self.db_manager:
                success = self.db_manager.insert_many(
                    'stock_tick_data',
                    local_data,
                    on_conflict="ON CONFLICT (trade_time, stock_code) DO UPDATE SET price = EXCLUDED.price, volume = EXCLUDED.volume, amount = EXCLUDED.amount, cur_vol = EXCLUDED.cur_vol"
                )
                if success:
                    self.logger.info(f"[成功] 成功将 {len(local_data)} 条本地数据保存到TimescaleDB表")
                    return True
                else:
                    self.logger.error("[失败] 保存本地数据到TimescaleDB表失败")
                    return False

            # 如果都不可用，保存到CSV
            elif self.use_csv_backup:
                try:
                    df = pd.DataFrame(local_data)
                    csv_file = f"{self.csv_backup_dir}/persistent_save_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv"
                    df.to_csv(csv_file, index=False)
                    self.logger.info(f"[成功] 成功将 {len(local_data)} 条本地数据保存到CSV文件: {csv_file}")
                    return True
                except Exception as e:
                    self.logger.error(f"保存本地数据到CSV失败: {e}")
                    return False

            return False

        except Exception as e:
            self.logger.error(f"保存本地内存数据失败: {e}")
            return False

    def _check_and_clear_daily_warnings(self) -> None:
        """
        检查日期变化，如果是新的一天则清理警告集合
        """
        current_date = pd.Timestamp.now().date()
        if current_date != self._current_date:
            self._time_adjustment_warnings.clear()
            self._current_date = current_date
            self.logger.info("新的一天开始，已清理时间调整警告记录")




    def _save_tick_data_to_timescaledb_optimized(self, tick_df: pd.DataFrame) -> None:
        """
        优化版本：高性能保存Tick数据到TimescaleDB
        使用向量化操作和批量处理，目标在3秒内完成

        参数:
            tick_df: Tick数据DataFrame，已预处理好的格式
        """
        try:
            if tick_df.empty:
                return

            # 新增：时间数据清理和验证
            tick_df = self._clean_and_validate_time_data(tick_df)

            # 优化1: 使用向量化操作过滤数据
            # 创建股票代码到当前成交量的映射
            stock_codes = tick_df['stock_code'].values
            volumes = tick_df['volume'].values

            # 创建一个布尔掩码，标记哪些行需要保留
            mask = np.ones(len(tick_df), dtype=bool)

            # 优化2: 使用NumPy向量化操作检查成交量
            for i, (code, volume) in enumerate(zip(stock_codes, volumes)):
                if code in self.last_volume_data:
                    last_vol = self.last_volume_data[code]
                    if volume <= last_vol:
                        mask[i] = False  # 标记为过滤

                # 无论是否过滤，都更新成交量记录
                self.last_volume_data[code] = volume

            # 应用过滤掩码
            filtered_df = tick_df[mask]

            # 如果没有有效数据，直接返回
            if filtered_df.empty:
                self.logger.debug("过滤后没有有效的Tick数据需要保存")
                return

            # 优化3: 直接将DataFrame转换为记录列表
            # 先移除不需要的字段（formatted_time和time_adjusted不存在于数据库表中）
            columns_to_remove = ['formatted_time', 'time_adjusted']
            for col in columns_to_remove:
                if col in filtered_df.columns:
                    filtered_df = filtered_df.drop(columns=[col])

            # 使用to_dict('records')方法一次性转换所有行
            processed_data = filtered_df.to_dict('records')  # type: ignore

            # [启动] 优化4: 使用缓冲区批量处理（大幅提升性能）
            start_time = time.time()
            success = self._buffer_and_batch_write_tick_data(processed_data)
            duration = time.time() - start_time

            # 记录统计信息
            self.insertion_stats.record_attempt(len(processed_data), duration, success)

            # 删除日K线更新逻辑 - 专注于tick数据存储
            # 理由：简化数据流，K线计算可以通过数据库查询或独立服务实现

            # 减少日志输出，但增加性能统计
            if self._log_counter % 10 == 0:
                if success:
                    self.logger.info(f"[成功] 成功保存 {len(processed_data)}/{len(tick_df)} 条Tick数据")
                else:
                    self.logger.error("[失败] 保存Tick数据失败")

            # 定期输出性能统计信息
            if self.insertion_stats.total_attempts % 100 == 0:
                stats = self.insertion_stats.get_stats()
                self.logger.info(f"插入统计: 成功率={stats['success_rate']:.1f}%, "
                               f"平均速度={stats['records_per_second']:.0f}条/秒, "
                               f"总记录数={stats['total_records']}")

            # [启动] 性能优化：定期输出优化报告
            self._log_performance_optimization_report()

            # 如果成功率过低，记录警告
            if self.insertion_stats.total_attempts >= 10:
                stats = self.insertion_stats.get_stats()
                if stats['success_rate'] < 80:
                    self.logger.warning(f"数据插入成功率较低: {stats['success_rate']:.1f}%，请检查数据库连接")

            # K线生成功能已删除

        except Exception as e:
            self.logger.error(f"优化保存Tick数据失败: {e}")
            # 在生产环境中不抛出异常，避免中断数据流
            # 只在开发环境抛出异常
            if os.environ.get('DEVELOPMENT_MODE') == 'true':
                raise





    # def _save_tick_data_to_csv(self, tick_df: pd.DataFrame) -> None:
    #     """
    #     保存Tick数据到CSV文件（备选方案）

    #     参数:
    #         tick_df: Tick数据DataFrame
    #     """
    #     try:
    #         if tick_df.empty:
    #             self.logger.debug("没有Tick数据需要保存到CSV")
    #             return

    #         # 获取当前时间
    #         now = pd.Timestamp.now()
    #         current_date = now.strftime('%Y-%m-%d')
    #         current_time = now.strftime('%H-%M-%S')

    #         # 处理数据，转换为CSV格式
    #         processed_data = []
    #         filtered_count = 0

    #         for _, row in tick_df.iterrows():
    #             # 基本验证（优先使用stock_code字段）
    #             code = row.get('stock_code', row.get('code', ''))
    #             if not code:
    #                 continue
    #             current_volume = int(row.get('volume', 0) or 0)

    #             # 检查成交量是否有增加
    #             if code in self.last_volume_data:
    #                 last_volume = self.last_volume_data[code]
    #                 if current_volume <= last_volume:
    #                     continue

    #             # 更新最新的成交量数据
    #             self.last_volume_data[code] = current_volume
    #             filtered_count += 1

    #             # 解析时间戳
    #             timestamp = self._parse_timestamp(row.get('servertime', '') or '', current_date, now)

    #             # 构建CSV记录
    #             record = {
    #                 'trade_time': timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
    #                 'stock_code': code,
    #                 'price': float(row.get('price', 0) or 0),
    #                 'cur_vol': int(row.get('cur_vol', 0) or 0),
    #                 'last_close': float(row.get('last_close', 0) or 0),
    #                 'open': float(row.get('open', 0) or 0),
    #                 'high': float(row.get('high', 0) or 0),
    #                 'low': float(row.get('low', 0) or 0),
    #                 'volume': current_volume,
    #                 'amount': float(row.get('amount', 0) or 0),
    #                 'bid1': float(row.get('bid1', 0) or 0) if row.get('bid1') is not None else 0.0,
    #                 'ask1': float(row.get('ask1', 0) or 0) if row.get('ask1') is not None else 0.0,
    #                 'bid_vol1': int(row.get('bid_vol1', 0) or 0) if row.get('bid_vol1') is not None else 0,
    #                 'ask_vol1': int(row.get('ask_vol1', 0) or 0) if row.get('ask_vol1') is not None else 0,
    #                 'bid2': float(row.get('bid2', 0) or 0) if row.get('bid2') is not None else 0.0,
    #                 'ask2': float(row.get('ask2', 0) or 0) if row.get('ask2') is not None else 0.0,
    #                 'bid_vol2': int(row.get('bid_vol2', 0) or 0) if row.get('bid_vol2') is not None else 0,
    #                 'ask_vol2': int(row.get('ask_vol2', 0) or 0) if row.get('ask_vol2') is not None else 0,
    #                 'bid3': float(row.get('bid3', 0) or 0) if row.get('bid3') is not None else 0.0,
    #                 'ask3': float(row.get('ask3', 0) or 0) if row.get('ask3') is not None else 0.0,
    #                 'bid_vol3': int(row.get('bid_vol3', 0) or 0) if row.get('bid_vol3') is not None else 0,
    #                 'ask_vol3': int(row.get('ask_vol3', 0) or 0) if row.get('ask_vol3') is not None else 0,
    #                 'bid4': float(row.get('bid4', 0) or 0) if row.get('bid4') is not None else 0.0,
    #                 'ask4': float(row.get('ask4', 0) or 0) if row.get('ask4') is not None else 0.0,
    #                 'bid_vol4': int(row.get('bid_vol4', 0) or 0) if row.get('bid_vol4') is not None else 0,
    #                 'ask_vol4': int(row.get('ask_vol4', 0) or 0) if row.get('ask_vol4') is not None else 0,
    #                 'bid5': float(row.get('bid5', 0) or 0) if row.get('bid5') is not None else 0.0,
    #                 'ask5': float(row.get('ask5', 0) or 0) if row.get('ask5') is not None else 0.0,
    #                 'bid_vol5': int(row.get('bid_vol5', 0) or 0) if row.get('bid_vol5') is not None else 0,
    #                 'ask_vol5': int(row.get('ask_vol5', 0) or 0) if row.get('ask_vol5') is not None else 0
    #             }

    #             processed_data.append(record)

    #         # 记录筛选结果
    #         self.logger.info(f"CSV Tick数据筛选：原始数据 {len(tick_df)} 条，筛选后 {filtered_count} 条")

    #         # 如果没有有效数据，直接返回
    #         if not processed_data:
    #             self.logger.debug("没有有效的Tick数据需要保存到CSV")
    #             return

    #         # 转换为DataFrame并保存到CSV
    #         df = pd.DataFrame(processed_data)

    #         # 生成CSV文件名
    #         csv_filename = f"tick_data_{current_date}_{current_time}.csv"
    #         csv_filepath = os.path.join(self.csv_backup_dir or "data/tick_backup", csv_filename)

    #         # 保存到CSV文件
    #         df.to_csv(csv_filepath, index=False, encoding='utf-8')

    #         self.logger.info(f"[成功] 成功保存 {len(processed_data)} 条Tick数据到CSV: {csv_filename}")

    #     except Exception as e:
    #         self.logger.error(f"保存Tick数据到CSV失败: {e}")
    #         import traceback
    #         self.logger.error(traceback.format_exc())
    #         raise

    def _save_tick_data_to_timescaledb(self, tick_df: pd.DataFrame) -> None:
        """
        保存Tick数据到TimescaleDB数据库

        参数:
            tick_df: Tick数据DataFrame
        """
        try:
            if tick_df.empty:
                self.logger.debug("没有Tick数据需要保存到TimescaleDB")
                return

            if not self.timescaledb_pool:
                self.logger.error("TimescaleDB连接池未初始化")
                return

            # 获取当前时间
            now = pd.Timestamp.now()
            current_date = now.strftime('%Y-%m-%d')

            # 处理数据，转换为TimescaleDB格式
            processed_data = []
            filtered_count = 0

            for _, row in tick_df.iterrows():
                # 基本验证（优先使用stock_code字段）
                code = row.get('stock_code', row.get('code', ''))
                if not code:
                    continue
                current_volume = int(row.get('volume', 0) or 0)

                # 检查成交量是否有增加（与CSV方法保持一致的逻辑）
                if code in self.last_volume_data:
                    last_volume = self.last_volume_data[code]
                    if current_volume <= last_volume:
                        continue

                # 更新最新的成交量数据
                self.last_volume_data[code] = current_volume
                filtered_count += 1

                # 解析时间戳
                timestamp = self._parse_timestamp(row.get('servertime', '') or '', current_date, now)

                # 构建TimescaleDB记录
                record = {
                    'trade_time': timestamp,  # TimescaleDB使用trade_time字段
                    'stock_code': code,
                    'price': float(row.get('price', 0) or 0),
                    'cur_vol': int(row.get('cur_vol', 0) or 0),
                    'last_close': float(row.get('last_close', 0) or 0),
                    'open': float(row.get('open', 0) or 0),
                    'high': float(row.get('high', 0) or 0),
                    'low': float(row.get('low', 0) or 0),
                    'volume': current_volume,
                    'amount': float(row.get('amount', 0) or 0),
                    'bid1': float(row.get('bid1', 0) or 0) if row.get('bid1') is not None else 0.0,
                    'ask1': float(row.get('ask1', 0) or 0) if row.get('ask1') is not None else 0.0,
                    'bid_vol1': int(row.get('bid_vol1', 0) or 0) if row.get('bid_vol1') is not None else 0,
                    'ask_vol1': int(row.get('ask_vol1', 0) or 0) if row.get('ask_vol1') is not None else 0,
                    'bid2': float(row.get('bid2', 0) or 0) if row.get('bid2') is not None else 0.0,
                    'ask2': float(row.get('ask2', 0) or 0) if row.get('ask2') is not None else 0.0,
                    'bid_vol2': int(row.get('bid_vol2', 0) or 0) if row.get('bid_vol2') is not None else 0,
                    'ask_vol2': int(row.get('ask_vol2', 0) or 0) if row.get('ask_vol2') is not None else 0,
                    'bid3': float(row.get('bid3', 0) or 0) if row.get('bid3') is not None else 0.0,
                    'ask3': float(row.get('ask3', 0) or 0) if row.get('ask3') is not None else 0.0,
                    'bid_vol3': int(row.get('bid_vol3', 0) or 0) if row.get('bid_vol3') is not None else 0,
                    'ask_vol3': int(row.get('ask_vol3', 0) or 0) if row.get('ask_vol3') is not None else 0,
                    'bid4': float(row.get('bid4', 0) or 0) if row.get('bid4') is not None else 0.0,
                    'ask4': float(row.get('ask4', 0) or 0) if row.get('ask4') is not None else 0.0,
                    'bid_vol4': int(row.get('bid_vol4', 0) or 0) if row.get('bid_vol4') is not None else 0,
                    'ask_vol4': int(row.get('ask_vol4', 0) or 0) if row.get('ask_vol4') is not None else 0,
                    'bid5': float(row.get('bid5', 0) or 0) if row.get('bid5') is not None else 0.0,
                    'ask5': float(row.get('ask5', 0) or 0) if row.get('ask5') is not None else 0.0,
                    'bid_vol5': int(row.get('bid_vol5', 0) or 0) if row.get('bid_vol5') is not None else 0,
                    'ask_vol5': int(row.get('ask_vol5', 0) or 0) if row.get('ask_vol5') is not None else 0
                }

                processed_data.append(record)

            # 记录筛选结果
            # 减少日志频率：每10次记录一次
            if hasattr(self, '_log_counter'):
                self._log_counter += 1
            else:
                self._log_counter = 1

            if self._log_counter % 10 == 1:
                self.logger.info(f"TimescaleDB Tick数据筛选：原始数据 {len(tick_df)} 条，筛选后 {filtered_count} 条")

            # 如果没有有效数据，直接返回
            if not processed_data:
                self.logger.debug("没有有效的Tick数据需要保存到TimescaleDB")
                return

            # 使用优化后的TimescaleDB连接池写入数据
            # 使用进程级别的持久数据库连接
            success = self._write_tick_data_with_persistent_connection(processed_data)

            if success:
                # 减少日志频率：每10次记录一次
                if self._log_counter % 10 == 1:
                    self.logger.info(f"[成功] 成功保存 {len(processed_data)} 条Tick数据到TimescaleDB")
                # K线更新功能已删除
            else:
                self.logger.error("[失败] 保存Tick数据到TimescaleDB失败")

        except Exception as e:
            self.logger.error(f"保存Tick数据到TimescaleDB失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            raise

    def _save_to_timescaledb_table(self, processed_data: List[Dict]) -> bool:
        """
        保存处理后的数据到TimescaleDB表

        参数:
            processed_data: 处理后的数据列表

        返回:
            bool: 保存是否成功
        """
        try:
            if not processed_data:
                return True

            # 使用TimescaleDB连接池保存数据
            if self.use_timescaledb and self.timescaledb_pool:
                success = self.timescaledb_pool.write_tick_data(processed_data)
                if success:
                    self.logger.info(f"[成功] 成功将 {len(processed_data)} 条数据保存到TimescaleDB表")

                    # K线生成功能已删除

                    return True
                else:
                    self.logger.error(f"[失败] 保存 {len(processed_data)} 条数据到TimescaleDB表失败")
                    return False
            else:
                self.logger.warning("TimescaleDB连接不可用")
                return False

        except Exception as e:
            self.logger.error(f"保存数据到TimescaleDB表失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False



    def start(self, force_start: bool = False) -> None:
        """
        启动市场数据获取器

        Args:
            force_start: 是否强制启动，不检查交易时间
        """
        # 检查是否已经在运行
        if self.running:
            logger.warning("市场数据获取器已经在运行")
            return

        # 检查是否只在交易时间启动
        start_only_in_trading_time = self.market_data_config.get("market_data_fetcher", {}).get("start_only_in_trading_time", True)

        # 非强制启动且只在交易时间启动时，进行交易时间检查
        if start_only_in_trading_time and not force_start:
            # 检查是否是交易时间（移除交易日检查，只检查交易时间）
            if not self._is_trading_time():
                logger.info("当前不是交易时间，不启动市场数据获取器")
                return

        # 设置运行标志
        self.running = True
        logger.info("市场数据获取器启动中...")

        # 重置成交量和成交金额记录
        self._reset_volume_amount_records()

        # 加载股票列表
        logger.info("开始加载股票列表...")
        try:
            self.stock_list = self._load_stock_list()
            if self.stock_list:
                logger.info(f"成功加载 {len(self.stock_list)} 只股票到 self.stock_list。")
            else:
                logger.warning("未能从数据库或配置文件加载到股票列表 (self.stock_list 为空或None)。")
        except Exception as e:
            logger.error(f"加载股票列表时发生严重错误: {e}")
            self.stock_list = [] # 确保在异常情况下 stock_list 是一个空列表而不是 None
            import traceback
            logger.error(traceback.format_exc())

        # [重构] 已删除共享内存功能 - 专注于tick数据获取和数据库存储
        # 理由：简化系统架构，提高可维护性和稳定性

        # 删除组件化模块初始化 - 简化架构
        # 理由：专注于tick数据获取和存储，不需要复杂的组件化架构
        logger.info("[简化] 使用简化架构：专注于tick数据获取和数据库存储")

        # 启动必要的线程（已删除数据处理线程）
        thread_configs = [
            ("数据获取线程", self._fetch_data_thread),
            ("定时任务线程", self._schedule_thread)
        ]

        # 创建并启动所有线程
        for thread_name, thread_target in thread_configs:
            logger.info(f"[线程] 正在启动 {thread_name}")
            thread = threading.Thread(target=thread_target)
            thread.daemon = True
            thread.start()
            logger.info(f"[线程] {thread_name} 启动完成，线程ID: {thread.ident}")

            # 根据线程名称设置相应的属性
            if thread_name == "数据获取线程":
                self.fetch_thread = thread
            elif thread_name == "定时任务线程":
                self.schedule_thread = thread

        # 初始化处理线程（已简化，仅保留必要功能）
        self._init_processing_threads()

        logger.info("市场数据获取器启动完成")





        # 历史K线数据预加载功能已删除 - 专注于实时数据获取和存储

        # TimescaleDB连接池状态检查
        if self.use_timescaledb and self.timescaledb_pool:
            logger.info("[成功] TimescaleDB连接池已在初始化时建立")
            # 显示连接池详细状态
            try:
                stats = self.timescaledb_pool.get_stats()
                logger.info(f"[连接池] TimescaleDB连接池状态: 可用连接 {stats['available_connections']}/{stats['pool_size']}")
                logger.info(f"   - 借用次数: {stats['borrowed_count']}, 归还次数: {stats['returned_count']}")
                logger.info(f"   - 连接池状态: {'正常' if stats['pool_available'] else '异常'}")
            except Exception as e:
                logger.warning(f"获取TimescaleDB连接池状态失败: {e}")
        elif self.use_timescaledb and not self.timescaledb_initialized:
            logger.warning("[警告] 数据库管理器未成功初始化，尝试重新初始化...")
            success = self._init_db_manager()
            if success:
                self.timescaledb_initialized = True
        else:
            logger.info("[架构] 直接使用TimescaleDB时序数据库，高性能数据存储")

        # 注释掉原来的复杂内存管理器代码
        # current_time = pd.Timestamp.now(tz=self.timezone).time()
        # if current_time.hour == 9 and current_time.minute >= 5:
        #     logger.info("9:05启动，开始初始化内存表管理器...")
        #     try:
        #         from utils.memory_manager import MemoryTableManager
        #         self.memory_manager = MemoryTableManager()
        #         # 执行完整的系统初始化
        #         if self.memory_manager.initialize_complete_system():
        #             logger.info("[成功] 内存表系统初始化成功")
        #             self.memory_tables_initialized = True
        #             # 输出内存使用情况
        #             memory_usage = self.memory_manager.get_memory_usage()
        #             logger.info("[内存] 内存表使用情况:")
        #             for table_name, usage in memory_usage.items():
        #                 logger.info(f"  - {table_name}: {usage['record_count']:,} 条记录, {usage['memory_mb']} MB")
        #         else:
        #             logger.error("[失败] 内存表系统初始化失败")
        #     except Exception as e:
        #         logger.error(f"[失败] 内存表管理器初始化失败: {e}")
        #         import traceback
        #         logger.error(traceback.format_exc())

        # 重复的线程启动代码已移动到start()方法内部




    def stop(self) -> None:
        """
        停止市场数据获取器
        """
        # 检查是否已经停止
        if not self.running:
            logger.warning("市场数据获取器已经停止")
            return

        logger.info("停止市场数据获取器")

        # 清除运行标志
        self.running = False

        # 等待所有线程结束
        threads = [
            ('fetch_thread', self.fetch_thread),
            ('schedule_thread', self.schedule_thread)
        ]

        for thread_name, thread in threads:
            if thread:
                logger.info(f"等待{thread_name}结束...")
                thread.join(timeout=5)
                if thread.is_alive():
                    logger.warning(f"{thread_name}未能在超时时间内结束")

        # [启动] 优化：刷新缓冲区并保存最后的数据
        logger.info("刷新缓冲区并保存最终数据...")
        try:
            # 刷新Tick数据缓冲区
            if hasattr(self, '_tick_data_buffer') and self._tick_data_buffer:
                buffer_size = len(self._tick_data_buffer)
                success = self._flush_tick_data_buffer()
                if success:
                    logger.info(f"[成功] 缓冲区刷新完成: {buffer_size} 条数据")
                else:
                    logger.error(f"[失败] 缓冲区刷新失败: {buffer_size} 条数据")

            self._save_data_to_db()
            logger.info("最终数据保存完成")
        except Exception as e:
            logger.error(f"保存最终数据失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

        # 数据库管理器会自动管理连接池，无需手动关闭
        if self.db_manager:
            logger.info("数据库管理器连接将自动清理")



        # 关闭持久化线程池
        if hasattr(self, 'thread_pool') and self.thread_pool:
            try:
                logger.info("关闭持久化线程池...")
                self.thread_pool.shutdown(wait=True)
                logger.info("[成功] 持久化线程池关闭成功")
            except Exception as e:
                logger.error(f"[失败] 关闭持久化线程池失败: {e}")

        # 清理TDX连接池
        if hasattr(self, 'client_pool') and self.client_pool:
            try:
                logger.info("清理TDX连接池...")
                self.client_pool.cleanup()
                logger.info("[成功] TDX连接池清理成功")
            except Exception as e:
                logger.error(f"[失败] 清理TDX连接池失败: {e}")

        # 清理共享内存（已禁用共享内存功能）
        # if self.use_shared_memory:
        #     try:
        #         # 使用MultiprocessingSharedMemoryManager的cleanup方法清理共享内存
        #         logger.info("停止时清理共享内存...")
        #         global sas
        #         if sas and sas.initialized:
        #             if hasattr(sas, 'cleanup') and callable(getattr(sas, 'cleanup')):
        #                 if sas.cleanup():
        #                     logger.info("停止时清理共享内存成功")
        #                 else:
        #                     logger.warning("停止时清理共享内存失败")
        #             else:
        #                 logger.warning("共享内存管理器没有cleanup方法，无法清理共享内存")
        #         else:
        #             logger.warning("共享内存管理器未初始化，无法清理共享内存")
        #     except Exception as e:
        #         logger.error(f"停止时处理共享内存清理出错: {e}")
        #         import traceback
        #         logger.error(traceback.format_exc())


        logger.info("市场数据获取器停止完成")

    def _process_tick_data(self, tick_df: pd.DataFrame) -> None:
        """
        处理Tick数据
        注意：Tick数据已在数据获取线程中直接保存到数据库

        参数:
            tick_df: Tick数据DataFrame
        """
        try:
            # 添加详细日志输出
            self.logger.info(f"处理Tick数据，收到 {len(tick_df)} 条记录")

            if tick_df.empty:
                self.logger.debug("Tick数据为空，跳过处理")
                return

            # 确保tick_df有必需的列
            required_columns = ['code', 'price', 'volume', 'amount', 'timestamp']
            missing_columns = [col for col in required_columns if col not in tick_df.columns]
            if missing_columns:
                self.logger.warning(f"Tick数据缺少必要列: {missing_columns}")
                # 尽可能地处理有效的列

            # 数据处理完成

            # 删除K线计算调用 - 专注于tick数据获取和存储
            # 理由：简化数据处理流程，K线计算可以通过独立服务实现

            # [修复] 优化：定期记录性能指标和清理内存
            self._log_performance_metrics()
            self._cleanup_memory_periodically()

        except Exception as e:
            self.logger.error(f"处理Tick数据出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    def _normalize_tick_data(self, row) -> Dict:
        """
        [修复] 修复：统一tick数据字段格式，解决时间字段不一致问题

        参数:
            row: 原始tick数据行

        返回:
            Dict: 标准化的tick数据
        """
        try:
            # [修复] 修复：统一使用trade_time字段，兼容多种时间格式
            trade_time = row.get('trade_time') or row.get('timestamp')

            if isinstance(trade_time, str):
                trade_time = pd.Timestamp(trade_time)
            elif isinstance(trade_time, (int, float)):
                # 判断是秒还是毫秒时间戳
                if trade_time > 1e10:  # 毫秒时间戳
                    trade_time = pd.Timestamp(trade_time, unit='ms')
                else:  # 秒时间戳
                    trade_time = pd.Timestamp(trade_time, unit='s')
            elif isinstance(trade_time, pd.Timestamp):
                pass  # 已经是Timestamp格式
            else:
                trade_time = pd.Timestamp.now()  # 默认使用当前时间

            return {
                'trade_time': int(trade_time.timestamp() * 1000),  # 统一为毫秒时间戳
                'price': float(row.get('price', 0)),
                'volume': int(row.get('volume', 0)),
                'amount': float(row.get('amount', 0.0)),
                'bid1': float(row.get('bid1', 0.0)),
                'ask1': float(row.get('ask1', 0.0)),
                'bid1_volume': int(row.get('bid1_volume', 0)),
                'ask1_volume': int(row.get('ask1_volume', 0)),
                'total_volume': int(row.get('total_volume', 0))
            }
        except Exception as e:
            self.logger.warning(f"标准化tick数据失败: {e}")
            # 返回默认数据
            return {
                'trade_time': int(pd.Timestamp.now().timestamp() * 1000),
                'price': float(row.get('price', 0)),
                'volume': 0,
                'amount': 0.0,
                'bid1': 0.0,
                'ask1': 0.0,
                'bid1_volume': 0,
                'ask1_volume': 0,
                'total_volume': 0
            }



    def _init_performance_metrics(self):
        """性能监控指标已删除 - 专注于数据获取和存储"""
        self._last_metrics_log_time = time.time()

    def _log_performance_metrics(self):
        """性能指标记录已删除 - 专注于数据获取和存储"""
        pass

    def _cleanup_memory_periodically(self):
        """[修复] 优化：定期清理内存"""
        try:
            if not hasattr(self, '_metrics'):
                return

            current_time = time.time()
            # 删除共享内存清理逻辑 - 不再使用共享内存
            # 理由：简化内存管理，专注于数据库存储
            self._metrics.last_cleanup_time = current_time

        except Exception as e:
            self.logger.error(f"定期清理内存失败: {e}")

    def _parse_timestamp(self, time_str: str, current_date: str, default_time: pd.Timestamp) -> pd.Timestamp:
        """
        解析时间戳字符串为pandas Timestamp对象，并调整非交易时间到交易时间内

        参数:
            time_str: 时间字符串，可能有多种格式
            current_date: 当前日期字符串，格式为'YYYY-MM-DD'
            default_time: 默认时间戳，当解析失败时使用

        返回:
            pd.Timestamp: 解析并调整后的时间戳
        """
        try:
            # 先解析原始时间戳
            original_timestamp: Optional[datetime] = None

            # 情况1: 标准时间格式 HH:MM:SS 或 HH:MM:SS.ms
            # 例如: "14:30:00" 或 "14:30:00.500"
            if ':' in time_str and len(time_str) <= 12:
                # 将当前日期与时间字符串组合
                original_timestamp = pd.Timestamp(f"{current_date} {time_str}")

            # 情况2: 完整日期时间格式 YYYY/MM/DD HH:MM
            # 例如: "2023/04/30 14:30"
            elif '/' in time_str and ':' in time_str:
                original_timestamp = pd.Timestamp(time_str)

            # 情况3: 特殊格式 "YYYY/MM/DD 0"
            # 这种格式通常表示收盘时间 15:00:00
            # 例如: "2023/04/30 0" 解析为 "2023/04/30 15:00:00"
            elif '/' in time_str and ' 0' in time_str:
                date_part = time_str.split(' ')[0]
                original_timestamp = pd.Timestamp(f"{date_part} 15:00:00")

            # 默认情况: 使用当前时间
            else:
                original_timestamp = default_time

            # 如果解析成功，调整非交易时间到交易时间内
            if original_timestamp is not None:
                try:
                    # 使用更安全的检查方式
                    if original_timestamp is not None:
                        if isinstance(original_timestamp, pd.Timestamp) and not pd.isna(original_timestamp):
                            return self._adjust_to_trading_time(original_timestamp)
                except (TypeError, ValueError):
                    pass
            return default_time

        except Exception:
            # 解析失败时使用默认时间
            return default_time

    def _adjust_to_trading_time(self, timestamp: pd.Timestamp) -> pd.Timestamp:
        """
        市场数据时间标准化 - 优化版本

        标准化规则:
        - 9:29-9:30 -> 9:30:00.xxx (开盘时间标准化，需要微秒偏移)
        - 11:30-11:35 -> 11:30:00.000 (上午收盘时间标准化，固定为.000)
        - 12:50-13:00 -> 13:00:00.xxx (下午开盘时间标准化，需要微秒偏移)
        - 15:00-15:01 -> 15:00:00.000 (收盘时间标准化，固定为.000)

        参数:
            timestamp: 原始时间戳

        返回:
            pd.Timestamp: 标准化后的时间戳
        """
        try:
            # 提取时间部分 - 使用更快的属性访问
            time_part = timestamp.time()
            date_part = timestamp.date()

            # 转换为总秒数进行快速比较 (避免多次计算)
            total_seconds = time_part.hour * 3600 + time_part.minute * 60 + time_part.second

            # 使用查找表进行快速匹配 - 比多重if更快
            # 格式: (开始秒数, 结束秒数, 标准时间秒数, 是否需要微秒偏移, 偏移基数)
            time_rules = (
                (34140, 34259, 34200, True, 0),     # 9:29-9:30 -> 9:30:00.xxx (需要偏移)
                (41400, 41699, 41400, False, 0),    # 11:30-11:35 -> 11:30:00.000 (固定.000)
                (41700, 46199, 46800, True, 200),   # 11:35-12:50 -> 13:00:00.xxx (午休时间)
                (46200, 46799, 46800, True, 200),   # 12:50-12:59:59 -> 13:00:00.xxx (需要偏移)
                (54000, 54119, 54000, False, 0),    # 15:00-15:01 -> 15:00:00.000 (固定.000)
            )

            # 快速查找匹配的规则
            for start_sec, end_sec, standard_sec, need_offset, ms_offset_base in time_rules:
                if start_sec <= total_seconds <= end_sec:
                    # 创建标准化时间 - 直接从秒数构造
                    standard_hour = standard_sec // 3600
                    standard_minute = (standard_sec % 3600) // 60

                    if need_offset:
                        # 需要微秒偏移的时间段 (开盘和下午开盘)
                        original_seconds = time_part.second
                        original_microseconds = time_part.microsecond

                        # 使用更安全的方法生成微秒偏移，确保值在有效范围内
                        # 将偏移限制在0-999范围内，这样乘以1000后不会超过999000
                        ms_offset = (ms_offset_base + (original_seconds << 4) + (original_microseconds >> 16)) % 1000
                        microseconds = ms_offset * 1000  # 现在保证在0-999000范围内

                        standardized_time = datetime.combine(
                            date_part,
                            dt_time(standard_hour, standard_minute, 0, microseconds)
                        )
                    else:
                        # 固定为.000的时间段 (上午收盘和收盘)
                        standardized_time = datetime.combine(
                            date_part,
                            dt_time(standard_hour, standard_minute, 0, 0)  # 微秒固定为0
                        )

                    result = pd.Timestamp(standardized_time)
                    # 确保返回有效的Timestamp
                    try:
                        if pd.isna(result):
                            return timestamp
                    except (TypeError, ValueError):
                        return timestamp
                    return result


            # 如果不在标准化范围内，返回原始时间
            return timestamp

        except Exception as e:
            self.logger.warning(f"时间标准化失败: {e}，返回原时间戳")
            return timestamp

    def _standardize_time_format(self, timestamp: pd.Timestamp) -> str:
        """
        标准化时间格式为数据库存储格式

        参数:
            timestamp: 时间戳对象

        返回:
            str: 标准化的时间字符串 (YYYY-MM-DD HH:MM:SS.mmm)
        """
        try:
            # 标准格式：YYYY-MM-DD HH:MM:SS.mmm
            return timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]  # 保留3位毫秒
        except Exception as e:
            self.logger.warning(f"时间格式标准化失败: {e}")
            # 返回当前时间的标准格式
            return pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]

    def _validate_trading_time_range(self, timestamp: pd.Timestamp) -> bool:
        """
        验证时间是否在有效的交易时间范围内（包括调整后的时间）

        参数:
            timestamp: 时间戳对象

        返回:
            bool: 是否在有效交易时间范围内
        """
        try:
            hour = timestamp.hour
            minute = timestamp.minute
            time_minutes = hour * 60 + minute

            # 有效的交易时间范围：
            # 1. 09:30-11:30 (上午交易时间)
            # 2. 13:00-15:00 (下午交易时间)
            # 3. 15:00-15:01 (盘后1分钟，调整到15:00)
            # 4. 调整后的时间（带微秒偏移的边界时间）

            morning_start = 9 * 60 + 30   # 09:30
            morning_end = 11 * 60 + 30    # 11:30
            afternoon_start = 13 * 60     # 13:00
            afternoon_end = 15 * 60       # 15:00
            aftermarket_end = 15 * 60 + 1 # 15:01 (新增：盘后1分钟)

            # 检查是否在正常交易时间内
            # 上午：09:30:00.000 到 11:30:00.000（包含11:30整点）
            is_morning_trading = (morning_start <= time_minutes < morning_end) or (time_minutes == morning_end and timestamp.second == 0 and timestamp.microsecond == 0)
            # 下午：13:00:00.000 到 15:00:00.000（包含15:00整点）
            is_afternoon_trading = (afternoon_start <= time_minutes < afternoon_end) or (time_minutes == afternoon_end and timestamp.second == 0 and timestamp.microsecond == 0)
            # 盘后：15:00:00.001 到 15:00:59.999
            is_aftermarket_trading = (time_minutes == afternoon_end and (timestamp.second > 0 or timestamp.microsecond > 0)) and time_minutes < aftermarket_end

            # 检查是否是调整后的边界时间（带微秒偏移）
            is_adjusted_boundary = (
                (hour == 9 and minute == 30 and timestamp.microsecond > 0) or  # 调整后的开盘时间
                (hour == 13 and minute == 0 and timestamp.microsecond > 0) or  # 调整后的午后开盘时间
                (hour == 15 and minute == 0 and timestamp.microsecond > 0)     # 调整后的收盘时间
            )

            return is_morning_trading or is_afternoon_trading or is_aftermarket_trading or is_adjusted_boundary

        except Exception as e:
            self.logger.warning(f"交易时间验证失败: {e}")
            return False

    def _get_time_adjustment_summary(self) -> Dict[str, int]:
        """
        获取时间调整统计摘要

        返回:
            Dict[str, int]: 各类时间调整的统计数据
        """
        try:
            summary = {
                'premarket_adjustments': getattr(self, '_premarket_adjustment_count', 0),
                'lunch_adjustments': getattr(self, '_lunch_adjustment_count', 0),
                'aftermarket_adjustments': getattr(self, '_aftermarket_adjustment_count', 0),
                'nontrading_adjustments': getattr(self, '_nontrading_adjustment_count', 0),
                'total_adjustments': 0
            }

            summary['total_adjustments'] = sum([
                summary['premarket_adjustments'],
                summary['lunch_adjustments'],
                summary['aftermarket_adjustments'],
                summary['nontrading_adjustments']
            ])

            return summary

        except Exception as e:
            self.logger.warning(f"获取时间调整统计失败: {e}")
            return {
                'premarket_adjustments': 0,
                'lunch_adjustments': 0,
                'aftermarket_adjustments': 0,
                'nontrading_adjustments': 0,
                'total_adjustments': 0
            }

    def _log_time_adjustment_summary(self) -> None:
        """
        记录时间调整统计摘要到日志
        """
        try:
            summary = self._get_time_adjustment_summary()

            if summary['total_adjustments'] > 0:
                self.logger.info("=" * 60)
                self.logger.info("时间标准化统计摘要")
                self.logger.info("=" * 60)
                self.logger.info(f"集合竞价时间调整: {summary['premarket_adjustments']:,} 次")
                self.logger.info(f"午休时间调整: {summary['lunch_adjustments']:,} 次 【重点关注】")
                self.logger.info(f"盘后时间调整: {summary['aftermarket_adjustments']:,} 次")
                self.logger.info(f"非交易时间调整: {summary['nontrading_adjustments']:,} 次")
                self.logger.info("-" * 60)
                self.logger.info(f"总计时间调整: {summary['total_adjustments']:,} 次")
                self.logger.info("=" * 60)

                # 特别提醒午休时间调整情况
                if summary['lunch_adjustments'] > 0:
                    lunch_percentage = (summary['lunch_adjustments'] / summary['total_adjustments']) * 100
                    self.logger.info(f"[统计] 午休时间(11:30-13:00)调整占比: {lunch_percentage:.1f}%")
                    self.logger.info("[成功] 所有午休时间数据已标准化到13:00:00开始的时间段")
            else:
                self.logger.info("本次运行未进行任何时间调整")

        except Exception as e:
            self.logger.warning(f"记录时间调整统计失败: {e}")

    def _periodic_time_adjustment_report(self) -> None:
        """
        定期输出时间调整统计报告（每小时一次）
        """
        try:
            # 检查是否需要输出报告
            current_time = time.time()
            if not hasattr(self, '_last_adjustment_report_time'):
                self._last_adjustment_report_time = current_time
                return

            # 每小时输出一次报告
            if current_time - self._last_adjustment_report_time >= 3600:  # 3600秒 = 1小时
                summary = self._get_time_adjustment_summary()

                if summary['total_adjustments'] > 0:
                    self.logger.info(f"[时间] 时间调整统计 - 总计: {summary['total_adjustments']} 次 "
                                   f"(午休: {summary['lunch_adjustments']}, "
                                   f"盘前: {summary['premarket_adjustments']}, "
                                   f"盘后: {summary['aftermarket_adjustments']}, "
                                   f"其他: {summary['nontrading_adjustments']})")

                self._last_adjustment_report_time = current_time

        except Exception as e:
            self.logger.warning(f"定期时间调整报告失败: {e}")



    # 实时K线数据获取方法已删除，简化架构







    def get_insertion_stats(self) -> Dict[str, Any]:
        """
        获取数据插入统计信息

        Returns:
            Dict[str, Any]: 插入统计信息
        """
        try:
            if hasattr(self, 'insertion_stats'):
                return self.insertion_stats.get_stats()
            else:
                return {'message': '统计功能未初始化'}

        except Exception as e:
            self.logger.error(f"获取插入统计信息失败: {e}")
            return {'error': str(e)}

    def reset_insertion_stats(self):
        """重置插入统计信息"""
        try:
            if hasattr(self, 'insertion_stats'):
                self.insertion_stats.reset()
                self.logger.info("插入统计信息已重置")
            else:
                self.logger.warning("统计功能未初始化，无法重置")

        except Exception as e:
            self.logger.error(f"重置插入统计信息失败: {e}")

    def _schedule_daily_tasks(self):
        """
        安排每日定时任务（用于保存K线数据）
        """
        try:
            import schedule
            import threading

            # 清除之前的任务
            schedule.clear()

            # 11:35 保存上午K线数据
            schedule.every().day.at("11:35").do(self._save_morning_klines_task)

            # 15:05 保存下午K线数据和生成120分钟K线
            schedule.every().day.at("15:05").do(self._save_afternoon_klines_task)

            # 启动定时任务线程
            def run_scheduler():
                while self.running:
                    schedule.run_pending()
                    time.sleep(60)  # 每分钟检查一次

            self.schedule_thread = threading.Thread(target=run_scheduler, daemon=True)
            self.schedule_thread.start()

            self.logger.info("定时任务已安排：11:35保存上午K线，15:05保存下午K线和生成120分钟K线")

        except ImportError:
            self.logger.warning("schedule模块未安装，定时任务功能不可用")
        except Exception as e:
            self.logger.error(f"安排定时任务失败: {e}")

    # 删除定时K线保存任务 - 不再使用共享内存和K线计算
    # 理由：
    # 1. 简化定时任务逻辑，专注于实时tick数据获取和存储
    # 2. K线数据可以通过数据库查询tick数据进行计算
    # 3. 避免复杂的定时任务管理，提高系统稳定性

    def _clean_and_validate_time_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理和验证时间数据，确保时间格式的一致性（性能优化版本）

        参数:
            df: 包含时间数据的DataFrame

        返回:
            pd.DataFrame: 清理后的DataFrame
        """
        try:
            if df.empty:
                return df

            # [启动] 性能优化：预过滤已知的无效时间股票
            if 'stock_code' in df.columns:
                invalid_stock_mask = df['stock_code'].isin(self._known_invalid_stocks)
                invalid_stock_count = invalid_stock_mask.sum()

                if invalid_stock_count > 0:
                    self._invalid_stock_skip_count += invalid_stock_count
                    # 每50次记录一次跳过统计
                    if self._invalid_stock_skip_count % 50 == 0:
                        self.logger.debug(f"已跳过 {self._invalid_stock_skip_count} 条已知无效时间股票记录")

                    # 过滤掉已知的无效时间股票
                    df = df[~invalid_stock_mask].copy()

                    if df.empty:
                        return df

            # 1. 标准化trade_time字段（使用缓存优化）
            if 'trade_time' in df.columns:
                # 应用时间标准化（使用缓存）
                df.loc[:, 'trade_time'] = df['trade_time'].apply(
                    lambda x: self._adjust_to_trading_time_cached(x) if pd.notna(x) else pd.Timestamp.now()
                )

                # 验证时间范围
                valid_time_mask = df['trade_time'].apply(self._validate_trading_time_range)
                invalid_count = (~valid_time_mask).sum()

                if invalid_count > 0:
                    # [启动] 性能优化：减少日志输出频率
                    if self._log_counter % 30 == 1:  # 每30次记录一次
                        invalid_records = df[~valid_time_mask][['stock_code', 'trade_time']].head(3)  # 只显示前3条
                        self.logger.warning(f"发现 {invalid_count} 条无效时间记录，已进行标准化处理")
                        self.logger.warning(f"无效时间记录示例：\n{invalid_records.to_string(index=False)}")

            # 2. 标准化时间格式字符串（仅在需要时）
            if 'trade_time' in df.columns and self._log_counter % 10 == 1:
                df.loc[:, 'formatted_time'] = df['trade_time'].apply(self._standardize_time_format)

            # 3. 添加时间验证标记（仅在调试模式下）
            if 'trade_time' in df.columns and self.logger.level <= 10:  # DEBUG级别
                df.loc[:, 'time_adjusted'] = df['trade_time'].apply(
                    lambda x: x.microsecond > 0  # 微秒大于0表示经过调整
                )

            return df

        except Exception as e:
            self.logger.warning(f"清理和验证时间数据失败: {e}")
            return df

    def _adjust_to_trading_time_cached(self, timestamp: pd.Timestamp) -> pd.Timestamp:
        """
        带缓存的时间调整方法（性能优化版本）

        参数:
            timestamp: 原始时间戳

        返回:
            pd.Timestamp: 调整后的时间戳
        """
        try:
            # 生成缓存键（使用时间字符串）
            cache_key = str(timestamp)

            # 检查缓存
            if cache_key in self._invalid_time_cache:
                return self._invalid_time_cache[cache_key]

            # 调用原始方法
            adjusted_time = self._adjust_to_trading_time(timestamp)

            # 只缓存调整过的时间（节省内存）
            if adjusted_time != timestamp:
                self._invalid_time_cache[cache_key] = adjusted_time

                # 限制缓存大小
                if len(self._invalid_time_cache) > 1000:
                    # 清理最旧的50%缓存项
                    keys_to_remove = list(self._invalid_time_cache.keys())[:500]
                    for key in keys_to_remove:
                        del self._invalid_time_cache[key]

            return adjusted_time

        except Exception as e:
            self.logger.debug(f"缓存时间调整失败: {e}")
            # 回退到原始方法
            return self._adjust_to_trading_time(timestamp)

    def _get_performance_optimization_stats(self) -> Dict[str, Any]:
        """
        获取性能优化统计信息

        返回:
            Dict[str, Any]: 性能优化统计数据
        """
        try:
            current_time = time.time()
            uptime = current_time - getattr(self, '_start_time', current_time)

            stats = {
                'cache_optimization': {
                    'cache_size': len(self._invalid_time_cache),
                    'cache_hit_rate': self._calculate_cache_hit_rate(),
                    'last_cache_clear': self._last_cache_clear,
                    'cache_clear_interval': self._cache_clear_interval
                },
                'batch_processing': {
                    'buffer_size': len(self._pending_data_buffer),
                    'batch_threshold': self._batch_write_threshold,
                    'batch_interval': self._batch_write_interval,
                    'last_batch_write': self._last_batch_write
                },
                # 共享内存优化已删除
                'invalid_stock_filtering': {
                    'known_invalid_stocks': len(self._known_invalid_stocks),
                    'skip_count': self._invalid_stock_skip_count,
                    'skip_rate_per_minute': self._invalid_stock_skip_count / (uptime / 60) if uptime > 0 else 0
                },
                'database_optimization': {
                    'batch_size': self._db_write_batch_size,
                    'async_write_enabled': self._enable_async_write,
                    'insertion_stats': self.insertion_stats.get_stats()
                },
                'system_performance': {
                    'uptime_minutes': uptime / 60,
                    'log_counter': self._log_counter,
                    'avg_processing_rate': self._log_counter / (uptime / 60) if uptime > 0 else 0
                }
            }

            return stats

        except Exception as e:
            self.logger.error(f"获取性能优化统计失败: {e}")
            return {'error': str(e)}

    def _calculate_cache_hit_rate(self) -> float:
        """计算缓存命中率"""
        try:
            # 简单估算：缓存大小与处理次数的比率
            cache_access_count = getattr(self, '_cache_access_count', 0)
            if cache_access_count > 0:
                return len(self._invalid_time_cache) / cache_access_count * 100
            return 0.0
        except:
            return 0.0

    def _log_performance_optimization_report(self):
        """记录性能优化报告"""
        try:
            if self._log_counter % 200 == 0:  # 每200次记录一次
                stats = self._get_performance_optimization_stats()

                self.logger.info("[启动] 性能优化报告:")
                self.logger.info(f"   缓存: 大小={stats['cache_optimization']['cache_size']}, "
                               f"命中率={stats['cache_optimization']['cache_hit_rate']:.1f}%")
                self.logger.info(f"   批处理: 缓冲区={stats['batch_processing']['buffer_size']}, "
                               f"阈值={stats['batch_processing']['batch_threshold']}")

                # 检查是否需要调整优化参数
                self._auto_tune_optimization_parameters(stats)

        except Exception as e:
            self.logger.debug(f"记录性能优化报告失败: {e}")

    def _auto_tune_optimization_parameters(self, stats: Dict[str, Any]):
        """自动调整优化参数"""
        try:
            # 根据性能统计自动调整参数
            insertion_stats = stats.get('database_optimization', {}).get('insertion_stats', {})

            if isinstance(insertion_stats, dict):
                success_rate = insertion_stats.get('success_rate', 100)
                records_per_second = insertion_stats.get('records_per_second', 0)

                # 如果成功率低，增加批量大小
                if success_rate < 90 and self._db_write_batch_size < 2000:
                    self._db_write_batch_size = min(self._db_write_batch_size + 100, 2000)
                    self.logger.info(f"自动调整: 数据库批量大小增加到 {self._db_write_batch_size}")

                # 共享内存更新频率调整已删除

        except Exception as e:
            self.logger.debug(f"自动调整优化参数失败: {e}")

    def _get_lunch_time_statistics(self) -> Dict[str, Any]:
        """
        获取午休时间处理的详细统计信息

        返回:
            Dict[str, Any]: 午休时间统计数据
        """
        try:
            lunch_count = getattr(self, '_lunch_adjustment_count', 0)
            total_count = self._get_time_adjustment_summary()['total_adjustments']

            stats = {
                'lunch_adjustments': lunch_count,
                'lunch_percentage': (lunch_count / total_count * 100) if total_count > 0 else 0,
                'lunch_time_range': '11:30:00 - 13:00:00',
                'adjusted_time_range': '13:00:00.000001 - 13:00:00.999999',
                'adjustment_method': '线性映射到微秒偏移',
                'data_integrity': '保持原有时间间隔关系'
            }

            return stats

        except Exception as e:
            self.logger.warning(f"获取午休时间统计失败: {e}")
            return {
                'lunch_adjustments': 0,
                'lunch_percentage': 0,
                'lunch_time_range': '11:30:00 - 13:00:00',
                'adjusted_time_range': '13:00:00.000001 - 13:00:00.999999',
                'adjustment_method': '线性映射到微秒偏移',
                'data_integrity': '保持原有时间间隔关系'
            }

    def test_time_standardization(self) -> None:
        """
        测试时间标准化功能，特别是11:30-13:00午休时间的处理
        """
        try:
            self.logger.info("=" * 60)
            self.logger.info("开始测试时间标准化功能")
            self.logger.info("=" * 60)

            # 测试用例：不同时间段的时间戳
            test_cases = [
                # 集合竞价时间
                pd.Timestamp('2024-01-15 09:20:30'),
                pd.Timestamp('2024-01-15 09:25:45'),

                # 正常交易时间
                pd.Timestamp('2024-01-15 09:35:00'),
                pd.Timestamp('2024-01-15 10:30:00'),
                pd.Timestamp('2024-01-15 11:25:00'),

                # 午休时间（重点测试）
                pd.Timestamp('2024-01-15 11:30:00'),
                pd.Timestamp('2024-01-15 11:45:30'),
                pd.Timestamp('2024-01-15 12:00:00'),
                pd.Timestamp('2024-01-15 12:30:15'),
                pd.Timestamp('2024-01-15 12:59:59'),

                # 下午交易时间
                pd.Timestamp('2024-01-15 13:00:00'),
                pd.Timestamp('2024-01-15 14:30:00'),
                pd.Timestamp('2024-01-15 14:59:59'),

                # 盘后时间
                pd.Timestamp('2024-01-15 15:00:30'),
                pd.Timestamp('2024-01-15 15:15:00'),
            ]

            self.logger.info("测试时间标准化结果：")
            self.logger.info("-" * 60)

            for i, original_time in enumerate(test_cases, 1):
                # 确保original_time是有效的Timestamp
                if not isinstance(original_time, pd.Timestamp):
                    continue

                try:
                    if pd.isna(original_time):
                        continue
                except (TypeError, ValueError):
                    continue

                adjusted_time = self._adjust_to_trading_time(original_time)
                time_category = self._get_time_category(original_time)

                try:
                    original_str = original_time.strftime('%H:%M:%S')
                    adjusted_str = adjusted_time.strftime('%H:%M:%S.%f')
                    self.logger.info(f"{i:2d}. {time_category:12s} | "
                                   f"{original_str:8s} → "
                                   f"{adjusted_str:15s}")
                except (AttributeError, TypeError):
                    self.logger.info(f"{i:2d}. {time_category:12s} | 时间格式化失败")

            # 测试午休时间的详细映射
            self.logger.info("-" * 60)
            self.logger.info("午休时间详细映射测试：")

            lunch_test_times = [
                pd.Timestamp('2024-01-15 11:30:00'),
                pd.Timestamp('2024-01-15 11:45:00'),
                pd.Timestamp('2024-01-15 12:00:00'),
                pd.Timestamp('2024-01-15 12:15:00'),
                pd.Timestamp('2024-01-15 12:30:00'),
                pd.Timestamp('2024-01-15 12:45:00'),
                pd.Timestamp('2024-01-15 12:59:59'),
            ]

            for lunch_time in lunch_test_times:
                # 确保lunch_time是有效的Timestamp
                if not isinstance(lunch_time, pd.Timestamp):
                    continue

                try:
                    if pd.isna(lunch_time):
                        continue
                except (TypeError, ValueError):
                    continue

                try:
                    adjusted_time = self._adjust_to_trading_time(lunch_time)
                    offset_minutes = (lunch_time.hour - 11) * 60 + (lunch_time.minute - 30)
                    if lunch_time.hour == 12:
                        offset_minutes = 30 + lunch_time.minute

                    lunch_str = lunch_time.strftime('%H:%M:%S')
                    adjusted_str = adjusted_time.strftime('%H:%M:%S.%f')
                    self.logger.info(f"午休 {lunch_str} "
                                   f"(偏移{offset_minutes:2d}分钟) → "
                                   f"{adjusted_str}")
                except (AttributeError, TypeError):
                    self.logger.info(f"午休时间处理失败: {lunch_time}")

            # 输出统计信息
            lunch_stats = self._get_lunch_time_statistics()
            self.logger.info("-" * 60)
            self.logger.info("午休时间处理配置：")
            self.logger.info(f"原始时间范围: {lunch_stats['lunch_time_range']}")
            self.logger.info(f"调整后范围: {lunch_stats['adjusted_time_range']}")
            self.logger.info(f"调整方法: {lunch_stats['adjustment_method']}")
            self.logger.info(f"数据完整性: {lunch_stats['data_integrity']}")

            self.logger.info("=" * 60)
            self.logger.info("时间标准化功能测试完成")
            self.logger.info("=" * 60)

        except Exception as e:
            self.logger.error(f"时间标准化测试失败: {e}")

    def _get_time_category(self, timestamp: pd.Timestamp) -> str:
        """
        获取时间所属的类别

        参数:
            timestamp: 时间戳

        返回:
            str: 时间类别
        """
        try:
            hour = timestamp.hour
            minute = timestamp.minute
            time_minutes = hour * 60 + minute

            if 9 * 60 + 15 <= time_minutes < 9 * 60 + 30:
                return "集合竞价"
            elif 9 * 60 + 30 <= time_minutes < 11 * 60 + 30:
                return "上午交易"
            elif 11 * 60 + 30 <= time_minutes < 13 * 60:
                return "午休时间"
            elif 13 * 60 <= time_minutes < 15 * 60:
                return "下午交易"
            elif 15 * 60 <= time_minutes < 15 * 60 + 1:
                return "盘后1分钟"
            elif 15 * 60 + 1 <= time_minutes < 15 * 60 + 30:
                return "盘后时间"
            else:
                return "非交易时间"

        except Exception:
            return "未知"

    def _reset_volume_amount_records(self) -> None:
        """
        重置成交量记录

        在每天开盘时调用，清空上一个交易日的记录
        """
        old_count = len(self.last_volume_data)
        self.logger.info(f"重置成交量记录，清空 {old_count} 只股票的历史成交量数据")
        with self.lock:
            # 重置用于筛选成交量有增加的数据的记录
            self.last_volume_data = {}

    # 共享内存检查方法已优化

# 已删除重复的_check_shared_memory_manager方法，使用_check_shared_memory方法代替

    # 数据处理线程已删除 - 数据直接在获取线程中保存，无需额外处理线程

    def _fetch_data_thread(self) -> None:
        """
        数据获取线程 - 底层优化版本，解决阻塞和性能问题

        核心优化：
        1. 增强错误恢复机制
        2. 添加线程健康检查
        3. 优化资源管理
        4. 增强日志监控
        """
        logger.info("[启动] 数据获取线程启动 - 底层优化版本")

        # 线程健康监控
        thread_start_time = time.time()
        last_heartbeat = time.time()
        heartbeat_interval = 30  # 30秒心跳间隔

        # 错误恢复机制
        consecutive_errors = 0
        max_consecutive_errors = 5
        error_recovery_delay = 1.0

        # 性能监控
        fetch_count = 0
        total_fetch_time = 0.0

        # 初始化下一个获取时间点
        next_fetch_time: Optional[float] = None

        logger.info(f"线程配置: 心跳间隔={heartbeat_interval}s, 最大连续错误={max_consecutive_errors}")

        while self.running:
            try:
                current_time = time.time()

                # [修复] 底层优化：线程健康检查和心跳监控
                if current_time - last_heartbeat >= heartbeat_interval:
                    uptime = current_time - thread_start_time
                    avg_fetch_time = total_fetch_time / max(fetch_count, 1)

                    logger.info(f"[心跳] 线程心跳: 运行时间={uptime:.1f}s, 获取次数={fetch_count}, "
                               f"平均耗时={avg_fetch_time:.2f}s, 连续错误={consecutive_errors}")

                    # 检查线程是否健康
                    if consecutive_errors >= max_consecutive_errors:
                        logger.warning(f"[警告] 线程健康状况不佳，连续错误={consecutive_errors}，尝试恢复")
                        self._recover_thread_health()
                        consecutive_errors = 0

                    last_heartbeat = current_time

                # [修复] 底层优化：增强交易时间判断，添加异常处理
                try:
                    is_trading = self._is_trading_time(check_just_ended=True)
                except Exception as e:
                    logger.error(f"[失败] 交易时间判断失败: {e}")
                    is_trading = True  # 默认为交易时间，避免阻塞
                    consecutive_errors += 1

                # 非交易时间处理
                if not is_trading:
                    # 在完全退出交易时间前，确保所有数据都已保存
                    if next_fetch_time is not None:
                        logger.info("[统计] 交易时间结束，保存最终数据")
                        try:
                            self._save_data_to_db()
                        except Exception as e:
                            logger.error(f"[失败] 保存最终数据失败: {e}")
                            consecutive_errors += 1

                    # 非交易时间，重置下一个获取时间点
                    next_fetch_time = None

                    # [修复] 底层优化：非交易时间的智能等待
                    sleep_time = min(60, heartbeat_interval)  # 最多等待60秒或心跳间隔
                    logger.debug(f"[时间] 非交易时间，等待 {sleep_time} 秒后重新检查")
                    time.sleep(sleep_time)
                    continue

                # 获取当前时间
                now = time.time()

                # 首次获取或刚进入交易时间
                if next_fetch_time is None:
                    logger.info("[目标] 开始交易时间数据获取")

                    # [修复] 底层优化：安全的数据获取，增强错误处理
                    fetch_start_time = time.time()
                    try:
                        self._fetch_realtime_quotes()
                        fetch_elapsed = time.time() - fetch_start_time

                        # 更新性能统计
                        fetch_count += 1
                        total_fetch_time += fetch_elapsed
                        consecutive_errors = 0  # 重置错误计数

                        logger.debug(f"[成功] 数据获取完成，耗时: {fetch_elapsed:.2f}s")

                    except Exception as e:
                        fetch_elapsed = time.time() - fetch_start_time
                        consecutive_errors += 1
                        logger.error(f"[失败] 数据获取失败 (第{consecutive_errors}次): {e}, 耗时: {fetch_elapsed:.2f}s")

                        # 错误恢复延迟
                        time.sleep(error_recovery_delay)
                        error_recovery_delay = min(error_recovery_delay * 1.5, 10.0)  # 指数退避，最大10秒

                    # 设置下一个获取时间点（向上取整到下一个 fetch_interval 的倍数）
                    current_seconds = int(now)
                    next_fetch_time = current_seconds + self.fetch_interval - (current_seconds % self.fetch_interval)
                else:
                    # 计算距离下一个获取时间点的等待时间
                    wait_time = next_fetch_time - now

                    # 如果已经超过了下一个获取时间点，立即获取数据
                    if wait_time <= 0:
                        # 记录明显的延迟
                        delay = -wait_time
                        if delay > 0.1:
                            logger.warning(f"获取延迟: {delay:.2f}秒")

                        # 获取数据
                        self._fetch_realtime_quotes()

                        # 设置下一个获取时间点
                        next_fetch_time += self.fetch_interval

                        # 如果下一个获取时间点已经过去，则调整到未来的时间点
                        while next_fetch_time is not None and next_fetch_time < now:
                            next_fetch_time += self.fetch_interval
                            logger.warning("获取延迟过大，跳过时间点")
                    else:
                        # 等待到下一个获取时间点，最多等待1秒，然后重新检查
                        time.sleep(min(wait_time, 1.0))
                        continue  # 继续循环，重新检查时间

                # 等待到下一个获取时间点
                if next_fetch_time is not None:
                    wait_time = max(0, next_fetch_time - time.time())
                    if wait_time > 0:
                        time.sleep(wait_time)

            except Exception as e:
                consecutive_errors += 1
                logger.error(f"[失败] 数据获取线程异常 (第{consecutive_errors}次): {e}")
                import traceback
                logger.error(traceback.format_exc())

                # [修复] 底层优化：智能错误恢复
                if consecutive_errors >= max_consecutive_errors:
                    logger.critical(f"[警报] 连续错误达到阈值 {max_consecutive_errors}，启动紧急恢复")
                    self._emergency_thread_recovery()
                    consecutive_errors = 0
                    error_recovery_delay = 1.0  # 重置延迟

                # 出错时，重置下一个获取时间点
                next_fetch_time = time.time() + self.fetch_interval
                time.sleep(min(error_recovery_delay, self.fetch_interval))  # 智能等待
                error_recovery_delay = min(error_recovery_delay * 1.2, 30.0)  # 逐步增加延迟

        logger.warning("[停止] 数据获取线程退出")

        # [修复] 底层优化：线程退出时的资源清理和统计报告
        try:
            uptime = time.time() - thread_start_time
            avg_fetch_time = total_fetch_time / max(fetch_count, 1)

            logger.info(f"[统计] 线程性能统计:")
            logger.info(f"   运行时间: {uptime:.1f}秒")
            logger.info(f"   获取次数: {fetch_count}")
            logger.info(f"   平均耗时: {avg_fetch_time:.2f}秒")
            logger.info(f"   连续错误: {consecutive_errors}")
            logger.info(f"   错误恢复延迟: {error_recovery_delay:.1f}秒")

            # 清理资源
            if hasattr(self, '_pending_data_buffer'):
                buffer_size = len(self._pending_data_buffer)
                if buffer_size > 0:
                    logger.info(f"清理数据缓冲区: {buffer_size} 条记录")
                    self._pending_data_buffer.clear()

        except Exception as e:
            logger.debug(f"线程退出清理失败: {e}")

    def _recover_thread_health(self) -> None:
        """
        线程健康恢复机制

        当检测到线程健康状况不佳时，尝试恢复：
        1. 重新初始化连接池
        2. 清理资源
        3. 重置状态
        """
        try:
            logger.info("[修复] 开始线程健康恢复...")

            # 1. 重新初始化客户端连接池
            if hasattr(self, 'client_pool') and self.client_pool:
                try:
                    logger.info("重新初始化客户端连接池...")
                    old_pool = self.client_pool
                    old_pool.cleanup()  # 清理旧连接池

                    # 创建新连接池
                    self.client_pool = QuotesClientPool(pool_size=self.max_threads, timeout=self.timeout)
                    logger.info("[成功] 客户端连接池重新初始化完成")

                except Exception as e:
                    logger.error(f"[失败] 重新初始化客户端连接池失败: {e}")

            # 2. 重新初始化数据库连接
            if hasattr(self, 'db_manager') and self.db_manager:
                try:
                    logger.info("检查数据库连接健康状况...")
                    # 这里可以添加数据库连接健康检查
                    logger.info("[成功] 数据库连接检查完成")

                except Exception as e:
                    logger.error(f"[失败] 数据库连接检查失败: {e}")

            # 3. 清理缓存和临时数据
            try:
                if hasattr(self, '_invalid_time_cache'):
                    self._invalid_time_cache.clear()
                    logger.info("[成功] 时间缓存已清理")

                if hasattr(self, '_pending_data_buffer'):
                    self._pending_data_buffer.clear()
                    logger.info("[成功] 数据缓冲区已清理")

            except Exception as e:
                logger.error(f"[失败] 清理缓存失败: {e}")

            logger.info("[成功] 线程健康恢复完成")

        except Exception as e:
            logger.error(f"[失败] 线程健康恢复失败: {e}")

    def _emergency_thread_recovery(self) -> None:
        """
        紧急线程恢复机制

        当连续错误达到阈值时的紧急恢复措施：
        1. 强制重启所有连接
        2. 清理所有缓存
        3. 重置所有计数器
        """
        try:
            logger.critical("[警报] 启动紧急线程恢复...")

            # 1. 强制重启客户端连接池
            try:
                if hasattr(self, 'client_pool'):
                    logger.info("强制重启客户端连接池...")
                    self.client_pool.close_all()  # 关闭所有连接
                    time.sleep(2)  # 等待连接完全关闭

                    # 重新创建连接池
                    self.client_pool = QuotesClientPool(pool_size=self.max_threads, timeout=self.timeout)
                    logger.info("[成功] 客户端连接池强制重启完成")

            except Exception as e:
                logger.error(f"[失败] 强制重启客户端连接池失败: {e}")

            # 2. 重置所有计数器和状态
            try:
                self.consecutive_errors = 0
                if hasattr(self, '_log_counter'):
                    self._log_counter = 0
                if hasattr(self, '_shared_memory_update_counter'):
                    self._shared_memory_update_counter = 0

                logger.info("[成功] 状态计数器已重置")

            except Exception as e:
                logger.error(f"[失败] 重置状态计数器失败: {e}")

            # 3. 强制垃圾回收
            try:
                import gc
                gc.collect()
                logger.info("[成功] 强制垃圾回收完成")

            except Exception as e:
                logger.error(f"[失败] 强制垃圾回收失败: {e}")

            logger.critical("[成功] 紧急线程恢复完成")

        except Exception as e:
            logger.critical(f"[失败] 紧急线程恢复失败: {e}")
            # 这是最后的手段，如果紧急恢复都失败了，可能需要重启整个进程

    def _calculate_optimal_workers(self) -> int:
        """
        计算最优的工作线程数

        基于系统资源、网络状况和历史性能动态调整

        返回:
            int: 最优工作线程数
        """
        try:
            # 基础线程数
            base_workers = min(32, self.max_threads * 2)

            # [修复] 根据系统CPU核心数调整
            import os
            cpu_count = os.cpu_count() or 4
            cpu_based_workers = min(base_workers, cpu_count * 4)

            # [修复] 根据历史性能调整
            if hasattr(self, '_fetch_performance_stats'):
                stats = self._fetch_performance_stats
                if stats['total_fetches'] > 10:
                    avg_time = stats['avg_fetch_time']
                    if avg_time > 2.5:  # 如果平均时间超过2.5秒，减少线程数
                        cpu_based_workers = max(8, int(cpu_based_workers * 0.8))
                    elif avg_time < 1.5:  # 如果平均时间小于1.5秒，可以增加线程数
                        cpu_based_workers = min(64, int(cpu_based_workers * 1.2))

            # [修复] 根据网络状况调整
            if hasattr(self, 'client_pool') and self.client_pool:
                pool_stats = getattr(self.client_pool, 'connection_stats', {})
                failure_rate = pool_stats.get('total_failed', 0) / max(pool_stats.get('total_created', 1), 1)
                if failure_rate > 0.1:  # 失败率超过10%，减少并发
                    cpu_based_workers = max(4, int(cpu_based_workers * 0.7))

            self.logger.debug(f"计算最优工作线程数: 基础={base_workers}, CPU调整={cpu_based_workers}")
            return cpu_based_workers

        except Exception as e:
            self.logger.debug(f"计算最优工作线程数失败: {e}")
            return min(32, self.max_threads * 2)

    def _fetch_batch_quotes_optimized(self, batch_idx: int, batch: List[str]) -> Optional[pd.DataFrame]:
        """
        优化的批量获取股票行情数据
        增强版本：添加详细的错误处理和调试信息

        参数:
            batch_idx: 批次索引
            batch: 股票代码列表

        返回:
            Optional[pd.DataFrame]: 获取的数据或None
        """
        try:
            batch_start_time = time.time()

            # [修复] 底层优化：使用优化的客户端连接
            client = self.client_pool.get_client()
            if not client:
                self.logger.warning(f"批次 {batch_idx}: 无法获取客户端连接")
                return None

            try:
                # [修复] 底层优化：批量获取数据，减少网络往返
                self.logger.debug(f"批次 {batch_idx}: 开始获取 {len(batch)} 只股票的数据")
                quotes_data = client.quotes(symbol=batch)  # type: ignore

                if quotes_data is None:
                    self.logger.warning(f"批次 {batch_idx}: 获取数据为None")
                    return None

                if hasattr(quotes_data, 'empty') and quotes_data.empty:
                    self.logger.warning(f"批次 {batch_idx}: 获取数据为空DataFrame")
                    return None

                # [修复] 底层优化：快速数据验证和清理
                if not self._quick_validate_quotes_data(quotes_data):
                    self.logger.warning(f"批次 {batch_idx}: 数据验证失败")
                    # 输出数据样本用于调试
                    if hasattr(quotes_data, 'columns'):
                        self.logger.debug(f"批次 {batch_idx}: 数据列名: {list(quotes_data.columns)}")
                        self.logger.debug(f"批次 {batch_idx}: 数据形状: {quotes_data.shape}")
                    return None

                # [修复] 底层优化：内存高效的数据处理
                processed_data = self._fast_process_quotes_data(quotes_data)

                batch_time = time.time() - batch_start_time
                data_count = len(processed_data) if processed_data is not None else 0
                self.logger.debug(f"批次 {batch_idx}: 处理完成，耗时 {batch_time:.3f}s，数据量 {data_count}")

                if processed_data is not None and data_count > 0:
                    return processed_data
                else:
                    self.logger.warning(f"批次 {batch_idx}: 处理后数据为空")
                    return None

            finally:
                # [修复] 底层优化：确保连接归还到池中
                self.client_pool.close_client(client)  # type: ignore

        except Exception as e:
            self.logger.error(f"批次 {batch_idx} 获取失败: {e}")
            # 输出详细的错误信息用于调试
            import traceback
            self.logger.debug(f"批次 {batch_idx} 错误详情: {traceback.format_exc()}")
            return None

    def _quick_validate_quotes_data(self, data: pd.DataFrame) -> bool:
        """
        快速验证行情数据

        参数:
            data: 行情数据

        返回:
            bool: 数据是否有效
        """
        try:
            if data is None or data.empty:
                return False

            # 检查必要的列
            required_cols = ['code', 'price']
            for col in required_cols:
                if col not in data.columns:
                    return False

            # 检查数据质量
            if data['price'].isna().all():
                return False

            return True

        except Exception:
            return False

    def _fast_process_quotes_data(self, data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """
        快速处理行情数据

        参数:
            data: 原始行情数据

        返回:
            Optional[pd.DataFrame]: 处理后的数据
        """
        try:
            # [修复] 底层优化：使用向量化操作，避免循环
            processed = data.copy()

            # 添加时间戳
            current_time = pd.Timestamp.now()
            processed['trade_time'] = current_time

            # 快速数据类型转换
            numeric_cols = ['price', 'volume', 'amount']
            for col in numeric_cols:
                if col in processed.columns:
                    processed[col] = pd.to_numeric(processed[col], errors='coerce')

            # 过滤无效数据
            processed = processed.dropna(subset=['code', 'price'])

            return processed if not processed.empty else None

        except Exception as e:
            self.logger.debug(f"快速处理行情数据失败: {e}")
            return None

    def _update_fetch_performance_stats(self, total_time: float, network_time: float,
                                       processing_time: float, database_time: float):
        """
        更新获取性能统计

        参数:
            total_time: 总耗时
            network_time: 网络耗时
            processing_time: 处理耗时
            database_time: 数据库耗时
        """
        try:
            if not hasattr(self, '_fetch_performance_stats'):
                return

            stats = self._fetch_performance_stats
            total_fetches = stats['total_fetches']

            # 更新平均值（使用增量更新算法）
            if total_fetches > 1:
                alpha = 1.0 / total_fetches  # 权重
                stats['avg_fetch_time'] = (1 - alpha) * stats['avg_fetch_time'] + alpha * total_time
                stats['avg_network_time'] = (1 - alpha) * stats['avg_network_time'] + alpha * network_time
                stats['avg_processing_time'] = (1 - alpha) * stats['avg_processing_time'] + alpha * processing_time
                stats['avg_database_time'] = (1 - alpha) * stats['avg_database_time'] + alpha * database_time
            else:
                stats['avg_fetch_time'] = total_time
                stats['avg_network_time'] = network_time
                stats['avg_processing_time'] = processing_time
                stats['avg_database_time'] = database_time

            # 更新成功计数
            if total_time < self.fetch_interval * 1.2:  # 在合理时间内完成视为成功
                stats['successful_fetches'] += 1

            # 定期检查是否需要优化
            current_time = time.time()
            if current_time - stats['last_optimization_check'] > 300:  # 每5分钟检查一次
                self._check_performance_optimization_needed(stats)
                stats['last_optimization_check'] = current_time

        except Exception as e:
            self.logger.debug(f"更新获取性能统计失败: {e}")

    def _check_performance_optimization_needed(self, stats: Dict[str, Any]):
        """
        检查是否需要性能优化

        参数:
            stats: 性能统计数据
        """
        try:
            if stats['total_fetches'] < 10:
                return  # 样本太少，不进行优化

            avg_time = stats['avg_fetch_time']
            success_rate = stats['successful_fetches'] / stats['total_fetches']

            # 性能优化建议
            if avg_time > self.fetch_interval * 0.9:
                self.logger.warning(f"[修复] 性能优化建议：平均获取时间 {avg_time:.2f}s 过长")

                if stats['avg_network_time'] > avg_time * 0.6:
                    self.logger.warning("   - 网络时间占比过高，建议优化网络连接或减少并发数")

                if stats['avg_database_time'] > avg_time * 0.3:
                    self.logger.warning("   - 数据库时间占比过高，建议优化数据库写入")

            if success_rate < 0.8:
                self.logger.warning(f"[修复] 性能优化建议：成功率 {success_rate:.1%} 过低")

        except Exception as e:
            self.logger.debug(f"检查性能优化需求失败: {e}")




    #                 # 保存15分钟K线数据
    #                 self._save_kline_data_to_db(klines_15min, "stock_min_data", 15)
    #             except Exception as e:
    #                 self.logger.error(f"保存K线数据到数据库出错: {e}")

    #     except Exception as e:
    #         self.logger.error(f"更新K线数据出错: {e}")
    #         import traceback
    #         self.logger.error(traceback.format_exc())

    def _init_processing_threads(self) -> None:
        """
        初始化处理线程（已简化）

        K线生成功能已删除
        """

        self.logger.info("K线生成功能已删除")


    # 共享内存初始化方法已优化


    def _save_data_to_db(self) -> None:
        """
        保存所有数据到TimescaleDB数据库

        数据直接保存到TimescaleDB，实时处理无需批量同步
        """
        try:
            # 检查数据库管理器
            if not self.use_timescaledb or not self.db_manager:
                self.logger.warning("数据库管理器不可用，无法保存数据")
                return

            # 保存Tick数据到TimescaleDB表
            if self.save_tick_to_db and self.tick_data:
                try:
                    # 转换为DataFrame
                    tick_data_list = []
                    for code, data in self.tick_data.items():
                        data['code'] = code
                        tick_data_list.append(data)

                    if tick_data_list:
                        tick_df = pd.DataFrame(tick_data_list)
                        self._save_tick_data_to_timescaledb(tick_df)
                        self.logger.debug(f"保存Tick数据到TimescaleDB成功，包含 {len(self.tick_data)} 只股票")
                except Exception as e:
                    self.logger.error(f"保存Tick数据到TimescaleDB失败: {e}")
                    import traceback
                    self.logger.error(traceback.format_exc())

        except Exception as e:
            self.logger.error(f"保存数据到数据库失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())





    def _load_stock_list(self) -> List[Dict[str, Any]]:
        """
        加载股票列表 - 从配置文件获取

        返回:
            股票列表
        """
        try:
            self.logger.info("开始加载股票列表...")

            # 优先从PostgreSQL xystock数据库获取
            pg_stocks = self._load_stock_list_from_postgresql()
            if pg_stocks:
                self.logger.info(f"[数据库] 从PostgreSQL xystock数据库加载 {len(pg_stocks)} 只股票")
                # 将从数据库获取的股票列表保存到配置文件作为备份
                self._save_stock_list_to_config(pg_stocks)
                return pg_stocks

            # 如果PostgreSQL获取失败，从配置文件加载
            config_stocks = self._load_stock_list_from_config()
            if config_stocks:
                self.logger.info(f"[配置] 从配置文件加载 {len(config_stocks)} 只股票")
                return config_stocks

            # 如果配置文件也没有股票列表，使用默认列表
            default_stocks = [
                {"code": "000001", "name": "平安银行"},
                {"code": "600519", "name": "贵州茅台"},
                {"code": "601318", "name": "中国平安"}
            ]

            self.logger.warning(f"[警告] 未从PostgreSQL数据库或配置文件加载到股票列表，使用基础默认列表 ({len(default_stocks)} 只股票)")

            # 将默认列表保存到配置文件
            self._save_stock_list_to_config(default_stocks)

            return default_stocks

        except Exception as e:
            self.logger.error(f"[失败] 加载股票列表失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

            # 出错时返回一个小的默认列表
            default_stocks = [
                {"code": "000001", "name": "平安银行"},
                {"code": "600519", "name": "贵州茅台"},
                {"code": "601318", "name": "中国平安"}
            ]

            self.logger.warning(f"[警告] 加载股票列表出错，使用基础默认列表 ({len(default_stocks)} 只股票)")
            return default_stocks

    def _load_stock_list_from_postgresql(self) -> List[Dict[str, Any]]:
        """
        从PostgreSQL数据库加载股票列表

        返回:
            List[Dict[str, Any]]: 股票列表
        """
        try:
            self.logger.info("正在从数据库加载股票列表...")

            # 使用统一的数据库管理器
            if not hasattr(self, 'db_manager') or not self.db_manager:
                self.db_manager = get_db_manager()

            # [修复] 查询股票信息 - 使用专门的 stock_info 表
            query = "SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code"
            self.logger.info(f"[调试] 执行数据库查询: {query}")

            # 使用数据库管理器执行查询
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query)
                    results = cursor.fetchall()
                    self.logger.info(f"[调试] 数据库查询结果数量: {len(results) if results else 0}")

            if not results:
                self.logger.warning("数据库中stock_info表为空或无股票数据")
                return []

            # 转换为字典列表
            stock_list = []
            for row in results:
                # 处理不同的cursor返回格式
                if isinstance(row, dict):
                    # RealDictCursor返回字典格式
                    stock_code = str(row.get('stock_code', '')).strip()
                    stock_name = str(row.get('stock_name', stock_code)).strip()

                    stock_list.append({
                        'code': stock_code,
                        'name': stock_name
                    })
                else:
                    # 普通cursor返回元组格式
                    stock_code = str(row[0]).strip()  # stock_code
                    stock_name = str(row[1]).strip() if len(row) > 1 else stock_code  # stock_name

                    stock_list.append({
                        'code': stock_code,
                        'name': stock_name
                    })

            self.logger.info(f"[成功] 从数据库成功获取 {len(stock_list)} 只股票")
            return stock_list

        except ImportError:
            self.logger.error("[失败] 数据库模块未安装，无法连接数据库")
            return []
        except Exception as e:
            self.logger.error(f"[失败] 从PostgreSQL获取股票列表失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return []

    # [删除] 不再需要股票代码标准化方法
    # 理由：stock_info表中的stock_code字段是character varying类型，数据已经是正确格式

    def _save_stock_list_to_config(self, stock_list: List[Dict[str, Any]]) -> None:
        """
        将股票列表保存到配置文件

        参数:
            stock_list: 股票列表
        """
        try:
            # 构建TOML格式的内容
            config_content = "# 股票列表配置\n\n# 股票列表\n"

            for stock in stock_list:
                code = stock.get("code", "")
                name = stock.get("name", "")
                market = 1 if code.startswith("6") or code.startswith("9") else 0

                config_content += "[[stock_list]]\n"
                config_content += f'code = "{code}"\n'
                config_content += f'name = "{name}"\n'
                config_content += f'market = {market}\n\n'

            # 保存到文件
            config_path = "config/stock_list.toml"
            with open(config_path, "w", encoding="utf-8") as f:
                f.write(config_content)

            self.logger.info(f"成功将 {len(stock_list)} 只股票保存到配置文件: {config_path}")

        except Exception as e:
            self.logger.error(f"保存股票列表到配置文件失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    def _load_stock_list_from_config(self) -> List[Dict[str, Any]]:
        """
        从配置文件加载股票列表

        返回:
            股票列表
        """
        try:
            import toml

            # 加载配置文件
            config_path = "config/stock_list.toml"
            if not os.path.exists(config_path):
                self.logger.warning(f"股票列表配置文件不存在: {config_path}")
                return []

            with open(config_path, "r", encoding="utf-8") as f:
                config = toml.load(f)

            # 提取股票列表
            stock_list = config.get("stock_list", [])
            if not stock_list:
                self.logger.warning(f"配置文件中没有股票列表: {config_path}")
                return []

            # 确保每个股票都有code和name字段
            valid_stocks = []
            for stock in stock_list:
                if isinstance(stock, dict) and "code" in stock:
                    if "name" not in stock:
                        stock["name"] = stock["code"]
                    valid_stocks.append(stock)

            return valid_stocks

        except Exception as e:
            self.logger.error(f"从配置文件加载股票列表失败: {e}")
            return []

    def _create_stock_batches(self) -> List[List[Dict[str, Any]]]:
        """
        将股票列表分批处理

        返回:
            股票批次列表
        """
        try:
            if not self.stock_list:
                return []

            # 创建批次
            batches = []
            for i in range(0, len(self.stock_list), self.batch_size):
                batch = self.stock_list[i:i+self.batch_size]
                batches.append(batch)

            self.logger.info(f"创建了 {len(batches)} 个股票批次，每批最多 {self.batch_size} 只股票")
            return batches

        except Exception as e:
            self.logger.error(f"创建股票批次失败: {e}")
            return []

    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件

        返回:
            配置字典
        """
        try:
            # 检查配置文件是否存在
            if not os.path.exists(self.config_path):
                self.logger.error(f"配置文件不存在: {self.config_path}")
                return {}

            # 加载配置文件
            with open(self.config_path, 'r', encoding='utf-8') as f:
                try:
                    config = toml.load(f)
                except toml.TomlDecodeError as e:
                    self.logger.error(f"TOML格式错误: {self.config_path}, 错误: {e}")
                    # 尝试使用可能的更健壮的解析器
                    f.seek(0)  # 重置文件指针到开头
                    try:
                        import tomli  # type: ignore
                        config = tomli.loads(f.read())
                        self.logger.info(f"使用备用解析器成功加载配置文件: {self.config_path}")
                        return config
                    except ImportError:
                        self.logger.warning("tomli库未安装，跳过备用解析器")
                        return {}
                    except Exception as e2:
                        self.logger.error(f"备用解析器也无法解析配置文件: {self.config_path}, 错误: {e2}")
                        return {}

            self.logger.info(f"成功加载配置文件: {self.config_path}")
            return config

        except Exception as e:
            self.logger.error(f"加载配置文件失败: {self.config_path}, 错误: {e}")
            return {}

    def _load_market_data_config(self) -> Dict[str, Any]:
        """
        加载市场数据获取器配置文件

        返回:
            配置字典
        """
        try:
            # 检查配置文件是否存在
            if not os.path.exists(self.market_data_config_path):
                self.logger.error(f"市场数据获取器配置文件不存在: {self.market_data_config_path}")
                return {}

            # 加载配置文件
            with open(self.market_data_config_path, 'r', encoding='utf-8') as f:
                config = toml.load(f)

            self.logger.info(f"成功加载市场数据获取器配置文件: {self.market_data_config_path}")
            return config

        except Exception as e:
            self.logger.error(f"加载市场数据获取器配置文件失败: {self.market_data_config_path}, 错误: {e}")
            return {}

    def _calculate_optimal_thread_count(self, config_max_threads: int) -> int:
        """
        计算最优线程数

        参数:
            config_max_threads: (int) 配置文件中指定的最大线程数

        返回:
            int: 最优线程数
        """
        try:
            # 获取CPU核心数
            cpu_count = os.cpu_count() or 4

            # 简化计算，避免循环依赖
            # 基于CPU核心数和配置的最大线程数计算
            optimal_threads = min(cpu_count * 2, config_max_threads)

            # 确保至少有一个线程
            optimal_threads = max(1, optimal_threads)

            self.logger.info(f"计算得到最优线程数: {optimal_threads} (CPU核心数: {cpu_count}, 配置最大线程数: {config_max_threads})")
            return optimal_threads

        except Exception as e:
            self.logger.error(f"计算最优线程数失败: {e}")
            # 失败时返回一个安全值
            return min(4, config_max_threads)




    def _fetch_realtime_quotes(self) -> None:
        """
        获取实时行情数据并直接保存到数据库
        [启动] 底层优化版本：目标在3秒内完成tick数据获取和K线生成

        核心优化：
        1. 智能批量处理和并发控制
        2. 内存池复用和缓存优化
        3. 网络连接池优化
        4. 数据库批量写入优化
        5. 详细性能监控和自动调优
        """
        try:
            # [修复] 底层优化：确保所有计数器和缓存已初始化
            if not hasattr(self, '_log_counter'):
                self._log_counter = 0
            if not hasattr(self, '_fetch_performance_stats'):
                self._fetch_performance_stats = {
                    'total_fetches': 0,
                    'successful_fetches': 0,
                    'avg_fetch_time': 0.0,
                    'avg_network_time': 0.0,
                    'avg_processing_time': 0.0,
                    'avg_database_time': 0.0,
                    'last_optimization_check': time.time()
                }

            # 记录总开始时间（从获取到保存的全过程）
            total_start_time = time.time()
            network_start_time = total_start_time

            if not self.stock_batches:
                self.logger.warning("股票批次为空，无法获取实时行情")
                return

            # [修复] 底层优化：智能日志输出和性能监控
            self._fetch_performance_stats['total_fetches'] += 1

            if self._log_counter % 30 == 1:
                self.logger.info(f"[统计] 股票代码列表长度: {len(self.stock_codes)}")
                self.logger.info(f"[修复] 使用持久化线程池，最大线程数: {self.max_threads}")
                self.logger.info(f"[批次] 提交 {len(self.stock_batches)} 个批次任务到线程池")

                # 输出性能统计
                stats = self._fetch_performance_stats
                if stats['total_fetches'] > 1:
                    self.logger.info(f"平均获取耗时: {stats['avg_fetch_time']:.2f}s "
                                   f"(网络: {stats['avg_network_time']:.2f}s, "
                                   f"处理: {stats['avg_processing_time']:.2f}s, "
                                   f"数据库: {stats['avg_database_time']:.2f}s)")

            # 记录总股票数，用于性能统计
            total_stocks = len(self.stock_codes)

            # [修复] 底层优化：增强统计信息
            successful_batches = 0
            failed_batches = 0

            # 记录数据获取开始时间
            start_time = time.time()

            """# 提交批次任务到线程池
            if self._log_counter % 10 == 1:
                self.logger.info(f"提交 {total_batches} 个批次任务到线程池")"""

            # 存储所有批次的Tick数据
            all_ticks = []

            # [启动] 底层优化1: 使用更高效的并发处理和连接池管理
            from concurrent.futures import ThreadPoolExecutor, as_completed

            # [启动] 底层优化2: 智能线程池大小调整
            optimal_workers = self._calculate_optimal_workers()

            # [启动] 底层优化3: 批量处理结果，减少锁竞争和内存分配
            with ThreadPoolExecutor(max_workers=optimal_workers, thread_name_prefix="FetchWorker") as executor:
                # [修复] 修复：将股票字典列表转换为股票代码字符串列表
                # 提交任务
                future_to_batch = {}
                for batch_idx, batch in enumerate(self.stock_batches):
                    # 从字典列表中提取股票代码字符串列表
                    stock_codes = [str(stock.get('code', '')) for stock in batch if stock.get('code')]
                    # 过滤掉空的股票代码
                    stock_codes = [code for code in stock_codes if code]

                    if stock_codes:  # 只有当有有效股票代码时才提交任务
                        future_to_batch[executor.submit(self._fetch_batch_quotes_optimized, batch_idx, stock_codes)] = batch_idx

                # [启动] 底层优化4: 使用as_completed处理结果，不必等待所有任务完成
                for future in as_completed(future_to_batch):
                    try:
                        batch_result = future.result(timeout=10)  # 添加超时控制
                        if batch_result is not None and isinstance(batch_result, pd.DataFrame) and not batch_result.empty:
                            successful_batches += 1
                            all_ticks.append(batch_result)
                        else:
                            failed_batches += 1
                    except Exception as e:
                        failed_batches += 1
                        if self._log_counter % 10 == 0:  # 减少错误日志
                            self.logger.error(f"处理批次结果出错: {e}")

            # 优化4: 使用更高效的数据处理方式
            if all_ticks:
                # 优化5: 使用concat一次性合并，减少内存复制
                combined_df = pd.concat(all_ticks, ignore_index=True, copy=False)
                timestamp = pd.Timestamp.now(tz=self.timezone)

                if not combined_df.empty:
                    save_start_time = time.time()

                    if self.save_tick_to_db:
                        try:
                            # 优化6: 使用向量化操作处理时间
                            combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)

                            # 优化7: 简化字段检查和转换
                            if 'code' in combined_df.columns:
                                combined_df.loc[:, 'stock_code'] = combined_df['code']
                            else:
                                # 快速检查可能的代码字段
                                for field in ['symbol', 'stock_code', 'stockcode']:
                                    if field in combined_df.columns:
                                        combined_df.loc[:, 'stock_code'] = combined_df[field]
                                        break
                                else:
                                    self.logger.error("无法找到股票代码字段，跳过此批次数据")
                                    return

                            # 优化8: 预定义必要字段和默认值
                            required_fields = {
                                'price': 0.0, 'volume': 0, 'amount': 0.0,
                                'open': 0.0, 'high': 0.0, 'low': 0.0, 'last_close': 0.0,
                                'cur_vol': 0,
                                'bid1': 0.0, 'ask1': 0.0, 'bid_vol1': 0, 'ask_vol1': 0,
                                'bid2': 0.0, 'ask2': 0.0, 'bid_vol2': 0, 'ask_vol2': 0,
                                'bid3': 0.0, 'ask3': 0.0, 'bid_vol3': 0, 'ask_vol3': 0,
                                'bid4': 0.0, 'ask4': 0.0, 'bid_vol4': 0, 'ask_vol4': 0,
                                'bid5': 0.0, 'ask5': 0.0, 'bid_vol5': 0, 'ask_vol5': 0
                            }

                            # 优化9: 一次性创建所有缺失列
                            for field, default_value in required_fields.items():
                                if field not in combined_df.columns:
                                    combined_df[field] = default_value

                            # 优化10: 只保留需要的列，减少数据量
                            final_columns = ['trade_time', 'stock_code'] + list(required_fields.keys())
                            processed_df = combined_df[final_columns].copy()

                            # 优化11: 批量类型转换
                            float_cols = ['price', 'last_close', 'open', 'high', 'low', 'amount',
                                         'bid1', 'ask1', 'bid2', 'ask2', 'bid3', 'ask3',
                                         'bid4', 'ask4', 'bid5', 'ask5']
                            int_cols = ['cur_vol', 'volume', 'bid_vol1', 'ask_vol1', 'bid_vol2', 'ask_vol2',
                                       'bid_vol3', 'ask_vol3', 'bid_vol4', 'ask_vol4', 'bid_vol5', 'ask_vol5']

                            # 优化12: 使用astype直接转换，而不是to_numeric
                            for col in float_cols:
                                if col in processed_df.columns:
                                    processed_df.loc[:, col] = processed_df[col].fillna(0.0).astype(float)  # type: ignore

                            for col in int_cols:
                                if col in processed_df.columns:
                                    processed_df.loc[:, col] = processed_df[col].fillna(0).astype(int)  # type: ignore

                            # 优化13: 使用高性能保存方法
                            if isinstance(processed_df, pd.DataFrame):
                                self._save_tick_data_to_timescaledb_optimized(processed_df)

                            save_elapsed_time = time.time() - save_start_time
                            """self.logger.info(f"保存Tick数据到TimescaleDB完成，共 {len(combined_df)} 条记录，耗时: {save_elapsed_time:.2f}秒")"""
                            if self._log_counter % 10 == 0:  # 减少日志输出
                                self.logger.info(f"保存Tick数据完成，共 {len(processed_df)} 条记录，耗时: {save_elapsed_time:.2f}秒")

                        except Exception as e:
                            self.logger.error(f"保存Tick数据失败: {e}")
                            import traceback
                            self.logger.error(traceback.format_exc())

                    # 将结果放入数据队列（用于兼容性）
                    try:
                        self.data_queue.put_nowait(combined_df)  # 使用非阻塞方式
                    except queue.Full:
                        pass  # 忽略队列已满的情况

                    # 记录总体信息（减少频率）
                    if self._log_counter % 10 == 0:
                        self.logger.info(f"处理完成，时间: {timestamp.strftime('%H:%M:%S')}, 股票数: {len(combined_df)}")

            # 计算耗时
            elapsed_time = (time.time() - start_time) * 1000  # 转换为毫秒
            stocks_per_second = total_stocks / (elapsed_time / 1000) if elapsed_time > 0 else 0

            # 记录性能指标
            self.logger.info(f"获取实时行情数据: 成功获取 {total_stocks} 只股票的行情，成功批次: {successful_batches}，失败批次: {failed_batches}，耗时: {elapsed_time:.2f} 毫秒，每秒处理股票数: {stocks_per_second:.2f}")

            # 更新性能统计信息
            self.fetch_count += 1
            self.successful_batches += successful_batches
            self.failed_batches += failed_batches
            self.total_fetch_time += elapsed_time
            self.min_fetch_time = min(self.min_fetch_time, elapsed_time) if self.min_fetch_time else elapsed_time
            self.max_fetch_time = max(self.max_fetch_time, elapsed_time)

            # [修复] 底层优化：计算详细的性能统计
            total_elapsed_time = time.time() - total_start_time
            network_time = time.time() - network_start_time
            processing_time = total_elapsed_time - network_time
            database_time = time.time() - start_time if hasattr(self, 'save_tick_to_db') and self.save_tick_to_db else 0

            # [修复] 底层优化：更新性能统计
            self._update_fetch_performance_stats(total_elapsed_time, network_time, processing_time, database_time)

            # 输出总耗时日志
            self.logger.info(f"[目标] 本次获取和保存数据总耗时: {total_elapsed_time:.2f}s "
                           f"(网络: {network_time:.2f}s, 处理: {processing_time:.2f}s, 数据库: {database_time:.2f}s)")

            # [修复] 底层优化：智能性能警告
            if total_elapsed_time > self.fetch_interval * 0.8:
                self.logger.warning(f"[警告] 总耗时 {total_elapsed_time:.2f}s 接近或超过获取间隔 {self.fetch_interval}s，"
                                  f"可能无法保证每 {self.fetch_interval}s 获取一次数据")

                # 自动调优建议
                if hasattr(self, '_fetch_performance_stats'):
                    stats = self._fetch_performance_stats
                    if stats['avg_fetch_time'] > self.fetch_interval * 0.9:
                        self.logger.warning("[修复] 建议：考虑增加获取间隔或优化网络连接")

            # 定期输出时间调整统计报告
            self._periodic_time_adjustment_report()

        except Exception as e:
            self.logger.error(f"获取实时行情数据失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())


    def _fetch_batch_quotes(self, batch_idx: int, stock_batch: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        获取一批股票的实时行情

        参数:
            batch_idx: 批次索引
            stock_batch: 股票批次列表

        返回:
            pd.DataFrame: 行情数据DataFrame
        """
        try:
            # 获取股票代码列表
            stock_codes = [stock.get("code") for stock in stock_batch]

            # 获取客户端
            client = self.client_pool.get_client()

            if not client:
                self.logger.error(f"批次 {batch_idx}: 无法获取客户端")
                return pd.DataFrame()

            try:
                # 获取行情数据
                quotes_result = client.quotes(symbol=stock_codes)  # type: ignore
                quotes_df = quotes_result if hasattr(quotes_result, 'empty') else pd.DataFrame()

                # 归还客户端到连接池
                self.client_pool.close_client(client)  # type: ignore

                # 处理结果
                if quotes_df is None or quotes_df.empty:
                    self.logger.warning(f"批次 {batch_idx}: 获取行情数据为空")
                    return pd.DataFrame()

                # 增强字段检查和重命名逻辑
                # self.logger.info(f"批次 {batch_idx}: 原始列名: {list(quotes_df.columns)}")

                # 检查并重命名股票代码字段
                if 'code' not in quotes_df.columns:
                    # 尝试多种可能的股票代码字段名
                    possible_code_fields = ['symbol', 'stock_code', 'stockcode', 'code_name', 'ts_code']
                    code_field_found: Optional[Any] = None

                    for field in possible_code_fields:
                        if field in quotes_df.columns:
                            code_field_found = field
                            break

                    if code_field_found:
                        quotes_df = quotes_df.rename(columns={code_field_found: 'code'})
                        self.logger.debug(f"批次 {batch_idx}: 将字段 '{code_field_found}' 重命名为 'code'")
                    else:
                        # 如果没有找到股票代码字段，尝试从请求的股票代码列表中添加
                        try:
                            if len(stock_codes) == len(quotes_df):
                                quotes_df.loc[:, 'code'] = stock_codes
                                self.logger.warning(f"批次 {batch_idx}: 未找到股票代码字段，使用请求的股票代码列表")
                            else:
                                self.logger.error(f"批次 {batch_idx}: 无法确定股票代码字段，数据长度不匹配")
                                return pd.DataFrame()
                        except (TypeError, ValueError):
                            self.logger.error(f"批次 {batch_idx}: 无法比较数据长度")
                            return pd.DataFrame()

                # 验证code字段是否正确设置
                try:
                    if 'code' not in quotes_df.columns or quotes_df['code'].isna().all():
                        self.logger.error(f"批次 {batch_idx}: code字段缺失或全为空值")
                        return pd.DataFrame()
                except (TypeError, ValueError):
                    self.logger.error(f"批次 {batch_idx}: code字段验证失败")
                    return pd.DataFrame()

                # 添加时间戳列，但不覆盖原始的servertime
                if 'servertime' not in quotes_df.columns:
                    quotes_df['servertime'] = pd.Timestamp.now().strftime('%H:%M:%S')

                # 保留timestamp列以保持兼容性
                quotes_df['timestamp'] = quotes_df.get('servertime', pd.Timestamp.now().strftime('%H:%M:%S'))

                return quotes_df

            except Exception as e:
                # 客户端可能异常，关闭它
                try:
                    if hasattr(client, 'close'):
                        client.close()
                except Exception:
                    pass

                # 增加连续错误计数
                self.consecutive_errors += 1

                # 如果连续错误次数超过阈值，重新初始化客户端池
                if self.consecutive_errors > self.max_consecutive_errors:
                    self.logger.warning(f"连续错误次数 {self.consecutive_errors} 超过阈值 {self.max_consecutive_errors}，尝试重新初始化客户端池")
                    try:
                        # 关闭现有连接池
                        self.client_pool.close_all()

                        # 创建新的连接池
                        self.client_pool = QuotesClientPool(pool_size=self.max_threads, timeout=self.timeout)

                        # 重置连续错误计数
                        self.consecutive_errors = 0
                    except Exception as pool_err:
                        self.logger.error(f"重新初始化客户端池失败: {pool_err}")

                # 重新抛出异常，以便重试机制处理
                raise

        except Exception as e:
            self.logger.error(f"批次 {batch_idx}: 获取行情数据失败: {e}")
            # 重新抛出异常，以便重试机制处理
            raise

    def _is_trading_time(self, check_just_ended: bool = False) -> bool:
        """
        检查当前是否是交易时间（移除交易日检查，只检查交易时间）

        参数:
            check_just_ended: 是否检查刚刚结束的交易时间

        返回:
            是否是交易时间
        """
        # 获取当前时间
        now = pd.Timestamp.now(tz=self.timezone)

        # 获取当前时间字符串 HH:MM:SS
        current_time_str = now.strftime("%H:%M:%S")

        # 检查是否在任一交易时段内
        is_trading = False

        # 检查早盘
        if self.am_open <= current_time_str <= self.am_close:
            is_trading = True

        # 检查午盘
        elif self.pm_open <= current_time_str <= self.pm_close:
            is_trading = True

        # [调试] 添加交易时间检查日志
        self.logger.debug(f"[交易时间检查] 当前时间: {current_time_str}, "
                         f"早盘: {self.am_open}-{self.am_close}, "
                         f"午盘: {self.pm_open}-{self.pm_close}, "
                         f"是否交易时间: {is_trading}")

        # 如果需要，检查刚刚结束的交易时间（可能在收盘后仍需继续获取数据）
        if not is_trading and check_just_ended:
            # 定义一个宽限期，例如收盘后5分钟内仍然视为交易时间
            grace_period = 1 * 60  # 1分钟，单位秒

            # 计算从收盘到现在经过的秒数
            pm_close_time = pd.Timestamp(f"{now.strftime('%Y-%m-%d')} {self.pm_close}", tz=self.timezone)
            seconds_since_close = (now - pm_close_time).total_seconds()

            # 如果在宽限期内，视为交易时间
            if 0 <= seconds_since_close <= grace_period:
                self.logger.debug(f"当前时间 {current_time_str} 在收盘后宽限期内，视为交易时间")
                is_trading = True

        return is_trading

    def _schedule_thread(self) -> None:
        """
        定时任务线程

        运行定时任务调度器，用于执行定时下载历史K线数据等任务
        """
        try:
            self.logger.info("定时任务线程启动")

            # 导入schedule库
            import schedule

            # K线生成功能已删除
            self.logger.info("K线生成功能已删除")

            # 注意：启动时自动下载历史数据的功能已移除
            # 请使用main.py中的--download-history参数手动触发历史数据下载

            # 主循环
            while self.running:
                # 运行待执行的定时任务
                schedule.run_pending()

                # 休眠一段时间
                time.sleep(1)

            self.logger.info("定时任务线程退出")

        except Exception as e:
            self.logger.error(f"定时任务线程出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    # 传统数据库重连方法已删除，仅使用TimescaleDB数据库


    # 传统数据库K线下载方法已删除，仅使用TimescaleDB数据库

    def _load_historical_data_optimized(self) -> bool:
        """
        使用优化的方式加载历史数据（在q端处理）

        Returns:
            bool: 加载是否成功
        """
        try:
            # 历史数据加载已简化，使用TimescaleDB
            logger.info("使用TimescaleDB加载历史数据（简化版本）")

            # 这里可以添加简化的历史数据加载逻辑
            # 暂时返回True，表示加载成功
            return True

        except Exception as e:
            logger.error(f"优化历史数据加载失败: {e}")
            return False

    def _init_temp_tables_native(self) -> bool:
        """
        原生连接器的内存表初始化（简化版本）

        Returns:
            bool: 初始化是否成功
        """
        try:
            if not self.use_timescaledb or not self.timescaledb_pool:
                self.logger.warning("TimescaleDB连接池不可用，无法初始化表")
                return False

            # TimescaleDB表已在连接时自动创建，无需额外初始化
            self.logger.info("TimescaleDB表已准备就绪")

            self.logger.info("[成功] 原生连接器内存表初始化完成")
            return True

        except Exception as e:
            self.logger.error(f"原生连接器初始化内存表失败: {e}")
            return False

    def _load_historical_data_native(self) -> bool:
        """
        原生连接器的历史数据加载（简化版本）

        Returns:
            bool: 加载是否成功
        """
        try:
            self.logger.info("原生连接器模式：跳过复杂历史数据加载")
            return True
        except Exception as e:
            self.logger.error(f"原生连接器加载历史数据失败: {e}")
            return False

    def _init_daily_start_values_native(self) -> bool:
        """
        原生连接器的当日起始值初始化（简化版本）

        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("原生连接器模式：使用默认起始值")
            # 初始化空的起始值字典
            if hasattr(self, 'kline_lock') and hasattr(self, 'daily_start_values'):
                with self.kline_lock:  # type: ignore
                    self.daily_start_values.clear()  # type: ignore
            return True
        except Exception as e:
            self.logger.error(f"原生连接器初始化起始值失败: {e}")
            return False

    # K线生成功能已删除

    def _parse_server_time_vectorized(self, df: pd.DataFrame) -> pd.Series:
        """
        向量化解析server_time字段并进行时间标准化，保持高性能

        特别优化11:30-13:00午休时间的处理，确保数据时间的一致性

        Args:
            df: 包含servertime字段的DataFrame

        Returns:
            pd.Series: 解析并标准化后的时间序列
        """
        try:
            current_time = pd.Timestamp.now()

            # 如果没有servertime字段，返回标准化的当前时间
            if 'servertime' not in df.columns:
                standardized_time = self._adjust_to_trading_time(current_time)
                return pd.Series([standardized_time] * len(df), index=df.index)

            # 创建结果序列
            result_times = []
            current_date = current_time.strftime('%Y-%m-%d')

            for servertime in df['servertime']:
                try:
                    # 检查是否是有效的时间格式
                    if pd.isna(servertime) or str(servertime).strip() in ['', '0']:
                        standardized_time = self._adjust_to_trading_time(current_time)
                        result_times.append(standardized_time)
                        continue

                    servertime_str = str(servertime).strip()

                    # 检查是否包含冒号（时间格式）
                    if ':' not in servertime_str:
                        standardized_time = self._adjust_to_trading_time(current_time)
                        result_times.append(standardized_time)
                        continue

                    # 解析时间格式 HH:MM:SS 或 HH:MM:SS.mmm
                    time_parts = servertime_str.split(':')

                    if len(time_parts) < 2:  # 至少需要小时和分钟
                        standardized_time = self._adjust_to_trading_time(current_time)
                        result_times.append(standardized_time)
                        continue

                    try:
                        hour = int(time_parts[0])
                        minute = int(time_parts[1])

                        # 处理秒数（可能包含小数或缺失）
                        if len(time_parts) >= 3:
                            second_str = time_parts[2]
                            if '.' in second_str:
                                # 包含毫秒的情况，如 "52.956"
                                second = int(float(second_str))
                                microsecond = int((float(second_str) - second) * 1000000)
                            else:
                                # 不包含毫秒的情况
                                second = int(second_str)
                                microsecond = 0
                        else:
                            # 只有小时和分钟的情况，如 "14:30"
                            second = 0
                            microsecond = 0

                        # 验证时间范围
                        if 0 <= hour <= 23 and 0 <= minute <= 59 and 0 <= second <= 59:
                            # 创建原始时间戳
                            raw_time = pd.Timestamp(f"{current_date} {hour:02d}:{minute:02d}:{second:02d}")
                            if microsecond > 0:
                                raw_time = raw_time.replace(microsecond=microsecond)

                            # 应用时间标准化，特别处理11:30-13:00午休时间
                            try:
                                if isinstance(raw_time, pd.Timestamp) and pd.notna(raw_time):
                                    standardized_time = self._adjust_to_trading_time(raw_time)
                                else:
                                    standardized_time = raw_time
                            except (TypeError, ValueError):
                                standardized_time = raw_time
                            result_times.append(standardized_time)
                        else:
                            # 时间范围无效，使用标准化的当前时间
                            standardized_time = self._adjust_to_trading_time(current_time)
                            result_times.append(standardized_time)

                    except (ValueError, IndexError) as e:
                        # 解析失败，使用标准化的当前时间
                        standardized_time = self._adjust_to_trading_time(current_time)
                        result_times.append(standardized_time)

                except Exception as e:
                    # 处理异常，使用标准化的当前时间
                    standardized_time = self._adjust_to_trading_time(current_time)
                    result_times.append(standardized_time)

            return pd.Series(result_times, index=df.index)

        except Exception as e:
            self.logger.warning(f"向量化时间解析失败: {e}，使用标准化的当前时间")
            current_time = pd.Timestamp.now()
            standardized_time = self._adjust_to_trading_time(current_time)
            return pd.Series([standardized_time] * len(df), index=df.index)

    # 注释掉多余的方法，简化流程
    # def _init_temp_tables_pool(self) -> bool:
    #     """使用连接池初始化内存表（已简化）"""
    #     pass

    # def _load_historical_data_pool(self) -> bool:
    #     """使用连接池加载历史数据（已简化，不需要复杂历史数据加载）"""
    #     pass

    # def _init_daily_start_values_pool(self) -> bool:
    #     """使用连接池初始化当日起始值（已简化）"""
    #     pass

    # def _save_tick_data_to_db_pool(self, tick_df: pd.DataFrame) -> None:
    #     """使用连接池保存tick数据（已合并到_save_tick_data_to_timescaledb）"""
    #     pass


    # K线更新相关方法已删除

    # 5分钟K线保存方法已删除

    def _save_realtime_daily_klines_to_db(self) -> int:
        """
        将统一内存池中的实时日K线数据保存到数据库（适配新架构）

        返回:
            int: 保存的记录数
        """
        try:
            if not self.direct_stock_manager or not self.db_manager:
                return 0

            saved_count = 0
            current_date = pd.Timestamp.now().date()

            # 获取所有有数据的股票代码（使用新的统一内存池API）
            stock_codes = self.direct_stock_manager.get_stock_codes()

            if not stock_codes:
                self.logger.debug("统一内存池中没有股票数据")
                return 0

            self.logger.info(f"开始保存日K线数据，共 {len(stock_codes)} 只股票")

            # 遍历所有股票
            for stock_code in stock_codes:
                try:
                    # 使用新的统一内存池API获取日K线数据
                    kline_data = self.direct_stock_manager.get_klines_for_stock(stock_code, "day")

                    if not kline_data:
                        continue

                    # 筛选当日数据并验证数据完整性
                    today_klines = []
                    for kline in kline_data:
                        try:
                            kline_time = pd.Timestamp(kline['trade_time'], unit='ms')
                            kline_date = kline_time.date()

                            if kline_date == current_date:
                                # 数据验证
                                if self._validate_kline_data(kline, stock_code, "day"):
                                    today_klines.append({
                                        'trade_time': kline_time,
                                        'stock_code': stock_code,
                                        'open': float(kline['open']),
                                        'high': float(kline['high']),
                                        'low': float(kline['low']),
                                        'close': float(kline['close']),
                                        'volume': int(kline['volume']),
                                        'amount': float(kline['amount'])
                                    })
                        except Exception as e:
                            self.logger.warning(f"处理股票 {stock_code} 日K线数据失败: {e}")
                            continue

                    if today_klines:
                        # 增量保存：只保存新的K线数据
                        new_klines = self._filter_new_klines(today_klines, stock_code, "day")

                        if new_klines:
                            # 批量保存到数据库
                            success = self.db_manager.insert_many(
                                'stock_kline_day',
                                new_klines,
                                on_conflict="ON CONFLICT (trade_time, stock_code) DO UPDATE SET open = EXCLUDED.open, high = EXCLUDED.high, low = EXCLUDED.low, close = EXCLUDED.close, volume = EXCLUDED.volume, amount = EXCLUDED.amount"
                            )

                            if success:
                                saved_count += len(new_klines)
                                self.logger.debug(f"成功保存股票 {stock_code} 的日K线: {len(new_klines)} 条")
                            else:
                                self.logger.warning(f"保存股票 {stock_code} 日K线失败")

                except Exception as e:
                    self.logger.warning(f"处理股票 {stock_code} 日K线失败: {e}")
                    continue

            self.logger.info(f"日K线保存完成，共保存 {saved_count} 条记录")
            return saved_count

        except Exception as e:
            self.logger.error(f"保存实时日K线数据失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return 0

    def _validate_kline_data(self, kline: Dict[str, Any], stock_code: str, kline_type: str) -> bool:
        """
        验证K线数据的完整性和准确性

        参数:
            kline: K线数据字典
            stock_code: 股票代码
            kline_type: K线类型 ("5min" 或 "day")

        返回:
            bool: 数据是否有效
        """
        try:
            # 检查必需字段
            required_fields = ['trade_time', 'open', 'high', 'low', 'close', 'volume', 'amount']
            for field in required_fields:
                if field not in kline:
                    self.logger.warning(f"股票 {stock_code} {kline_type}K线缺少字段: {field}")
                    return False

            # 检查数值有效性
            open_price = float(kline['open'])
            high_price = float(kline['high'])
            low_price = float(kline['low'])
            close_price = float(kline['close'])
            volume = int(kline['volume'])
            amount = float(kline['amount'])

            # 价格合理性检查
            if not (0 < open_price < 10000 and 0 < high_price < 10000 and
                   0 < low_price < 10000 and 0 < close_price < 10000):
                self.logger.warning(f"股票 {stock_code} {kline_type}K线价格异常: O={open_price}, H={high_price}, L={low_price}, C={close_price}")
                return False

            # OHLC逻辑检查
            if not (low_price <= open_price <= high_price and
                   low_price <= close_price <= high_price):
                self.logger.warning(f"股票 {stock_code} {kline_type}K线OHLC逻辑错误: O={open_price}, H={high_price}, L={low_price}, C={close_price}")
                return False

            # 成交量和成交额检查
            if volume < 0 or amount < 0:
                self.logger.warning(f"股票 {stock_code} {kline_type}K线成交量或成交额为负: V={volume}, A={amount}")
                return False

            # 成交额与价格成交量的合理性检查
            if volume > 0:
                avg_price = amount / volume
                if not (low_price * 0.8 <= avg_price <= high_price * 1.2):
                    self.logger.warning(f"股票 {stock_code} {kline_type}K线平均价格异常: avg_price={avg_price}, range=[{low_price}, {high_price}]")
                    return False

            return True

        except (ValueError, TypeError, ZeroDivisionError) as e:
            self.logger.warning(f"股票 {stock_code} {kline_type}K线数据验证失败: {e}")
            return False

    def _filter_new_klines(self, klines: List[Dict[str, Any]], stock_code: str, kline_type: str) -> List[Dict[str, Any]]:
        """
        过滤出新的K线数据，实现增量保存（仅支持日K线）

        参数:
            klines: K线数据列表
            stock_code: 股票代码
            kline_type: K线类型 ("day")

        返回:
            List[Dict]: 新的K线数据列表
        """
        try:
            if not klines:
                return []

            # 仅支持日K线
            if kline_type != 'day':
                self.logger.warning(f"不支持的K线类型: {kline_type}")
                return []

            # 获取数据库中最新的K线时间
            table_name = 'stock_kline_day'

            # 查询最新记录的时间
            query = f"""
                SELECT MAX(trade_time) as latest_time
                FROM {table_name}
                WHERE stock_code = %s
            """

            if self.db_manager is None:
                self.logger.error("数据库管理器未初始化")
                return []

            result = self.db_manager.execute_query(query, (stock_code,))

            latest_time: Optional[datetime] = None
            if result and result[0] and result[0][0]:
                latest_time = pd.Timestamp(result[0][0])

            # 过滤出新的K线数据
            new_klines = []
            for kline in klines:
                kline_time = kline['trade_time']

                # 如果数据库中没有记录，或者当前K线时间更新，则认为是新数据
                if latest_time is None or kline_time > latest_time:
                    new_klines.append(kline)
                elif kline_time == latest_time:
                    # 同一时间的K线，更新数据（用于实时更新当前K线）
                    new_klines.append(kline)

            if new_klines:
                self.logger.debug(f"股票 {stock_code} {kline_type}K线: 总数={len(klines)}, 新增={len(new_klines)}")

            return new_klines

        except Exception as e:
            self.logger.warning(f"过滤股票 {stock_code} {kline_type}K线新数据失败: {e}")
            # 出错时返回所有数据，确保数据不丢失
            return klines


