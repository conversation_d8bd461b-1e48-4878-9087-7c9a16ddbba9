#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的信号修复测试

验证修复的核心功能：
1. EnhancedSignal的兼容性属性
2. 数据库保存功能
3. 策略数据库管理器

作者: QuantFM Team
创建时间: 2025-08-08
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategies.trending.dual_channel_fibonacci import EnhancedSignal
from data.strategy_db_manager import get_strategy_db_manager
from processes.after_market_schedule import AfterMarketScheduler


def test_enhanced_signal_compatibility():
    """测试EnhancedSignal的兼容性"""
    print("=" * 60)
    print("测试 EnhancedSignal 兼容性")
    print("=" * 60)
    
    # 创建测试信号
    signal = EnhancedSignal(
        stock_code="000001.SZ",
        stock_name="平安银行",
        strategy_name="双通道斐波那契突破",
        break_t1_date=datetime(2025, 8, 1),
        break_t2_date=datetime(2025, 8, 8),
        start_low_date=datetime(2025, 7, 15),
        target_high_date=datetime(2025, 7, 20),
        start_low_price=10.50,
        target_high_price=12.80,
        break_t1_price=11.20,
        break_t2_price=12.50,
        signal_strength=0.85,
        volume_ratio_t1=2.3,
        volume_ratio_t2=3.1,
        breakout_amplitude=0.15,
        stability_score=0.78,
        structure_score=0.82,
        signal_note="强势突破信号"
    )
    
    # 测试所有兼容性属性
    compatibility_tests = [
        ("signal_date", signal.signal_date),
        ("latest_close", signal.latest_close),
        ("latest_volume", signal.latest_volume),
        ("avg_volume", signal.avg_volume),
        ("volume_ratio", signal.volume_ratio),
        ("max_high_20d", signal.max_high_20d),
        ("breakout_ratio", signal.breakout_ratio)
    ]
    
    all_passed = True
    for attr_name, attr_value in compatibility_tests:
        try:
            print(f"✅ {attr_name}: {attr_value}")
        except Exception as e:
            print(f"❌ {attr_name}: 错误 - {e}")
            all_passed = False
    
    return signal, all_passed


def test_after_market_scheduler_save():
    """测试AfterMarketScheduler的信号保存功能"""
    print("\n" + "=" * 60)
    print("测试 AfterMarketScheduler 信号保存")
    print("=" * 60)
    
    try:
        # 创建调度器实例
        scheduler = AfterMarketScheduler()
        
        # 创建测试信号
        signal = EnhancedSignal(
            stock_code="000002.SZ",
            stock_name="万科A",
            strategy_name="双通道斐波那契突破",
            break_t1_date=datetime(2025, 8, 5),
            break_t2_date=datetime(2025, 8, 8),
            start_low_date=datetime(2025, 7, 20),
            target_high_date=datetime(2025, 7, 25),
            start_low_price=8.50,
            target_high_price=10.20,
            break_t1_price=9.10,
            break_t2_price=9.80,
            signal_strength=0.75,
            volume_ratio_t1=1.8,
            volume_ratio_t2=2.5,
            breakout_amplitude=0.12,
            stability_score=0.72,
            structure_score=0.76,
            signal_note="中等强度突破"
        )
        
        # 测试保存
        print("1. 测试保存EnhancedSignal...")
        result = scheduler.save_signals([signal])
        print(f"   保存结果: {'✅ 成功' if result else '❌ 失败'}")
        
        return result
        
    except Exception as e:
        print(f"❌ AfterMarketScheduler测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_strategy_db_manager():
    """测试策略数据库管理器的完整功能"""
    print("\n" + "=" * 60)
    print("测试策略数据库管理器")
    print("=" * 60)
    
    try:
        db_manager = get_strategy_db_manager()
        
        # 创建测试信号
        signal = EnhancedSignal(
            stock_code="000003.SZ",
            stock_name="万科A",
            strategy_name="双通道斐波那契突破",
            break_t1_date=datetime(2025, 8, 6),
            break_t2_date=datetime(2025, 8, 8),
            start_low_date=datetime(2025, 7, 22),
            target_high_date=datetime(2025, 7, 28),
            start_low_price=15.20,
            target_high_price=18.50,
            break_t1_price=16.80,
            break_t2_price=17.90,
            signal_strength=0.92,
            volume_ratio_t1=3.2,
            volume_ratio_t2=4.1,
            breakout_amplitude=0.18,
            stability_score=0.88,
            structure_score=0.85,
            signal_note="超强突破信号"
        )
        
        results = []
        
        # 1. 测试保存到专用表
        print("1. 保存到双通道专用表...")
        result1 = db_manager.save_dual_channel_signal(signal)
        results.append(result1)
        print(f"   结果: {'✅ 成功' if result1 else '❌ 失败'}")
        
        # 2. 测试保存到通用表
        print("2. 保存到通用策略表...")
        result2 = db_manager.save_strategy_signal(signal)
        results.append(result2)
        print(f"   结果: {'✅ 成功' if result2 else '❌ 失败'}")
        
        # 3. 测试执行日志
        print("3. 记录执行日志...")
        result3 = db_manager.log_strategy_execution(
            strategy_name="双通道斐波那契突破",
            execution_type="test",
            stocks_processed=1,
            signals_generated=1,
            execution_duration_ms=200,
            status="completed"
        )
        results.append(result3)
        print(f"   结果: {'✅ 成功' if result3 else '❌ 失败'}")
        
        # 4. 测试获取策略配置
        print("4. 获取策略配置...")
        config = db_manager.get_strategy_config("双通道斐波那契突破")
        result4 = config is not None
        results.append(result4)
        print(f"   结果: {'✅ 成功' if result4 else '❌ 失败'}")
        if config:
            print(f"   配置项数量: {len(config)}")
        
        # 5. 测试获取最新信号
        print("5. 获取最新信号...")
        signals = db_manager.get_latest_signals("双通道斐波那契突破", 5)
        result5 = len(signals) > 0
        results.append(result5)
        print(f"   结果: {'✅ 成功' if result5 else '❌ 失败'}")
        print(f"   获取到 {len(signals)} 个信号")
        
        # 6. 测试性能统计更新
        print("6. 更新性能统计...")
        result6 = db_manager.update_strategy_performance("双通道斐波那契突破")
        results.append(result6)
        print(f"   结果: {'✅ 成功' if result6 else '❌ 失败'}")
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 策略数据库管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("双通道斐波那契策略修复验证")
    print("=" * 80)
    
    try:
        # 1. 测试EnhancedSignal兼容性
        signal, compatibility_ok = test_enhanced_signal_compatibility()
        
        # 2. 测试AfterMarketScheduler保存
        scheduler_ok = test_after_market_scheduler_save()
        
        # 3. 测试策略数据库管理器
        db_manager_ok = test_strategy_db_manager()
        
        # 总结结果
        print("\n" + "=" * 80)
        print("修复验证结果")
        print("=" * 80)
        
        print(f"{'✅' if compatibility_ok else '❌'} EnhancedSignal兼容性: {'通过' if compatibility_ok else '失败'}")
        print(f"{'✅' if scheduler_ok else '❌'} AfterMarketScheduler保存: {'通过' if scheduler_ok else '失败'}")
        print(f"{'✅' if db_manager_ok else '❌'} 策略数据库管理器: {'通过' if db_manager_ok else '失败'}")
        
        all_passed = compatibility_ok and scheduler_ok and db_manager_ok
        
        print(f"\n总体结果: {'🎉 全部通过' if all_passed else '⚠️ 部分失败'}")
        
        if all_passed:
            print("\n🎯 修复成功验证:")
            print("1. ✅ EnhancedSignal现在具有完整的兼容性属性")
            print("2. ✅ 'signal_date'属性错误已修复")
            print("3. ✅ 数据库保存功能正常工作")
            print("4. ✅ 策略数据库管理器功能完整")
            print("5. ✅ 双通道策略可以正常保存信号")
            
            print("\n🔧 已实现的优化:")
            print("- 通道2重复突破检查逻辑已添加")
            print("- 完整的策略数据库表结构")
            print("- 统一的策略信号管理")
            print("- 详细的执行日志和性能统计")
        
        print("=" * 80)
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
