# 双通道斐波那契策略 API 文档

## 概述

本文档详细描述了双通道斐波那契突破策略的API接口、数据结构和使用方法。

## 核心类

### DualChannelFibonacciStrategy

主策略类，实现双通道斐波那契突破信号检测。

#### 构造函数

```python
def __init__(self, config: Optional[StrategyConfig] = None, 
             config_file: str = "config/main.toml")
```

**参数:**
- `config` (Optional[StrategyConfig]): 策略配置对象，如果为None则从配置文件加载
- `config_file` (str): 配置文件路径，默认为"config/main.toml"

**示例:**
```python
# 使用默认配置
strategy = DualChannelFibonacciStrategy()

# 使用自定义配置
config = StrategyConfig(volume_ratio=1.5)
strategy = DualChannelFibonacciStrategy(config)

# 从指定配置文件加载
strategy = DualChannelFibonacciStrategy(config_file="custom_config.toml")
```

#### 主要方法

##### analyze()

分析单只股票，识别双通道斐波那契突破信号。

```python
def analyze(self, stock_code: str, stock_name: str, 
            kline_data: Optional[List[Dict]] = None,
            preprocessed_df: Optional[pd.DataFrame] = None) -> Optional[Signal]
```

**参数:**
- `stock_code` (str): 股票代码，如"000001"
- `stock_name` (str): 股票名称，如"平安银行"
- `kline_data` (Optional[List[Dict]]): 原始K线数据列表（向后兼容）
- `preprocessed_df` (Optional[pd.DataFrame]): 预处理后的DataFrame（推荐使用）

**返回值:**
- `Optional[Signal]`: 如果发现有效信号则返回Signal对象，否则返回None

**异常:**
- `ValueError`: 输入数据格式错误
- `KeyError`: 缺少必要的数据字段
- `MemoryError`: 内存不足

**示例:**
```python
strategy = DualChannelFibonacciStrategy()

# 使用预处理数据（推荐）
signal = strategy.analyze("000001", "平安银行", preprocessed_df=df)

# 使用原始数据（向后兼容）
signal = strategy.analyze("000001", "平安银行", kline_data=kline_list)

if signal:
    print(f"发现信号: {signal.stock_code}, 强度: {signal.signal_strength}")
```

##### get_strategy_name()

返回策略名称。

```python
def get_strategy_name(self) -> str
```

**返回值:**
- `str`: 策略名称 "双通道斐波那契突破"

### StrategyConfig

策略配置数据类，定义所有策略参数。

#### 属性

##### 通道参数
- `ema_short_1` (int): 通道1短期EMA周期，默认144
- `ema_long_1` (int): 通道1长期EMA周期，默认169
- `ema_short_2` (int): 通道2短期EMA周期，默认576
- `ema_long_2` (int): 通道2长期EMA周期，默认676

##### 时间窗参数
- `min_days` (int): 最小突破天数，默认2
- `max_days` (int): 最大突破天数，默认62
- `pre_check_days` (int): 历史结构验证天数，默认120
- `pivot_window` (int): 关键点提取窗口，默认10

##### 成交量参数
- `volume_window` (int): 成交量均值计算窗口，默认20
- `volume_ratio` (float): 突破时成交量比率，默认1.2

##### 容错参数
- `max_intrusion_days` (int): 最大允许影线穿透次数，默认1

#### 类方法

##### from_config_file()

从配置文件加载策略配置。

```python
@classmethod
def from_config_file(cls, config_file: str = "config/main.toml", 
                    section: str = "dual_channel_fibonacci") -> 'StrategyConfig'
```

**参数:**
- `config_file` (str): 配置文件路径
- `section` (str): 配置节名称

**返回值:**
- `StrategyConfig`: 配置实例

**示例:**
```python
# 从默认配置文件加载
config = StrategyConfig.from_config_file()

# 从指定文件和节加载
config = StrategyConfig.from_config_file("custom.toml", "my_strategy")
```

##### get_default_config()

获取默认配置。

```python
@classmethod
def get_default_config(cls) -> 'StrategyConfig'
```

**返回值:**
- `StrategyConfig`: 默认配置实例

#### 实例方法

##### validate()

验证配置参数的合理性。

```python
def validate(self) -> bool
```

**返回值:**
- `bool`: 配置是否有效

**示例:**
```python
config = StrategyConfig(ema_short_1=100, ema_long_1=80)  # 错误配置
if not config.validate():
    print("配置无效")
    errors = config.get_validation_errors()
    for error in errors:
        print(f"- {error}")
```

##### get_validation_errors()

获取配置验证错误信息。

```python
def get_validation_errors(self) -> List[str]
```

**返回值:**
- `List[str]`: 错误信息列表

##### save_to_config_file()

保存配置到配置文件。

```python
def save_to_config_file(self, config_file: str = "config/main.toml", 
                       section: str = "dual_channel_fibonacci") -> bool
```

**参数:**
- `config_file` (str): 配置文件路径
- `section` (str): 配置节名称

**返回值:**
- `bool`: 是否保存成功

## 数据结构

### Signal

信号数据结构，包含策略检测到的信号信息。

#### 字段

- `stock_code` (str): 股票代码
- `stock_name` (str): 股票名称
- `strategy_name` (str): 策略名称
- `signal_date` (date): 信号日期
- `signal_strength` (float): 信号强度 (0-1)
- `latest_close` (float): 最新收盘价
- `latest_volume` (int): 最新成交量
- `avg_volume` (int): 平均成交量
- `volume_ratio` (float): 成交量比率
- `max_high_20d` (float): 20日最高价（复用字段存储目标高点）
- `breakout_ratio` (float): 突破幅度
- `signal_note` (str): 详细信号信息

#### 示例

```python
signal = Signal(
    stock_code="000001",
    stock_name="平安银行",
    strategy_name="双通道斐波那契突破",
    signal_date=date.today(),
    signal_strength=0.75,
    latest_close=12.50,
    latest_volume=1000000,
    volume_ratio=1.8,
    signal_note="通道1突破:2025-01-05,通道2突破:2025-01-06"
)
```

### 输入数据格式

#### K线数据 (List[Dict])

原始K线数据格式，用于向后兼容。

```python
kline_data = [
    {
        "trade_time": "2025-01-01",
        "open": 10.0,
        "high": 10.5,
        "low": 9.8,
        "close": 10.2,
        "volume": 1000000,
        "amount": 10200000.0
    },
    # ... 更多数据
]
```

#### 预处理数据 (DataFrame)

包含所有技术指标的DataFrame格式（推荐使用）。

**必需列:**
- `trade_time`: 交易时间
- `open`, `high`, `low`, `close`: OHLC价格
- `volume`: 成交量
- `ema_144`, `ema_169`, `ema_576`, `ema_676`: EMA指标
- `channel1_upper`, `channel1_lower`: 通道1上下轨
- `channel2_upper`, `channel2_lower`: 通道2上下轨
- `volume_ma_20`: 成交量移动平均

**示例:**
```python
import pandas as pd

df = pd.DataFrame({
    'trade_time': pd.date_range('2020-01-01', periods=1000),
    'open': np.random.randn(1000) + 100,
    'high': np.random.randn(1000) + 101,
    'low': np.random.randn(1000) + 99,
    'close': np.random.randn(1000) + 100,
    'volume': np.random.randint(100000, 1000000, 1000),
    'ema_144': np.random.randn(1000) + 100,
    'ema_169': np.random.randn(1000) + 99,
    # ... 其他指标
})
```

## 工具函数

### 指标计算模块

#### calculate_ema_series()

批量计算多个周期的EMA指标。

```python
from indicators.talib_wrapper import calculate_ema_series

def calculate_ema_series(df: pd.DataFrame, periods: List[int], 
                        price_column: str = 'close') -> pd.DataFrame
```

**参数:**
- `df` (pd.DataFrame): 包含价格数据的DataFrame
- `periods` (List[int]): EMA周期列表
- `price_column` (str): 价格列名，默认'close'

**返回值:**
- `pd.DataFrame`: 包含EMA列的DataFrame

**示例:**
```python
df = calculate_ema_series(df, [144, 169, 576, 676])
print(df.columns)  # 包含 ema_144, ema_169, ema_576, ema_676
```

#### calculate_dual_channels()

计算双通道指标。

```python
from indicators.channel_indicators import calculate_dual_channels

def calculate_dual_channels(df: pd.DataFrame, 
                           channel1_params: Dict[str, int],
                           channel2_params: Dict[str, int],
                           price_column: str = 'close') -> pd.DataFrame
```

**参数:**
- `df` (pd.DataFrame): 包含价格数据的DataFrame
- `channel1_params` (Dict[str, int]): 通道1参数 {'upper': 144, 'lower': 169}
- `channel2_params` (Dict[str, int]): 通道2参数 {'upper': 576, 'lower': 676}
- `price_column` (str): 价格列名

**返回值:**
- `pd.DataFrame`: 包含通道指标的DataFrame

**示例:**
```python
channel1_params = {'upper': 144, 'lower': 169}
channel2_params = {'upper': 576, 'lower': 676}
df = calculate_dual_channels(df, channel1_params, channel2_params)
```

#### calculate_price_position()

计算价格与通道位置关系。

```python
from indicators.position_indicators import calculate_price_position

def calculate_price_position(df: pd.DataFrame, 
                           price_column: str = 'close',
                           channel_names: List[str] = ['channel1', 'channel2']) -> pd.DataFrame
```

**参数:**
- `df` (pd.DataFrame): 包含价格和通道数据的DataFrame
- `price_column` (str): 价格列名
- `channel_names` (List[str]): 通道名称列表

**返回值:**
- `pd.DataFrame`: 包含位置关系指标的DataFrame

### 缓存管理

#### get_global_cache()

获取全局缓存实例。

```python
from indicators.cache_manager import get_global_cache

cache = get_global_cache()
stats = cache.get_stats()
print(f"缓存条目数: {stats['total_entries']}")
```

#### clear_global_cache()

清空全局缓存。

```python
from indicators.cache_manager import clear_global_cache

clear_global_cache()
```

## 性能优化

### 缓存机制

策略使用多级缓存提高性能：

1. **指标计算缓存**: 避免重复计算EMA等指标
2. **数据预处理缓存**: 缓存预处理后的DataFrame
3. **算法结果缓存**: 缓存中间计算结果

### 向量化优化

使用numpy向量化操作替代循环：

```python
# 传统循环方式（慢）
results = []
for i in range(len(prices)):
    if prices[i] > upper[i]:
        results.append("above")
    else:
        results.append("below")

# 向量化方式（快）
results = np.where(prices > upper, "above", "below")
```

### 内存优化

```python
# 使用float32减少内存占用
data = df['close'].values.astype(np.float32)

# 及时释放不需要的数据
del intermediate_data
```

## 错误处理

### 常见异常

#### ValueError
数据格式错误或参数无效。

```python
try:
    signal = strategy.analyze(stock_code, stock_name, preprocessed_df=df)
except ValueError as e:
    print(f"数据格式错误: {e}")
```

#### KeyError
缺少必要的数据字段。

```python
try:
    signal = strategy.analyze(stock_code, stock_name, preprocessed_df=df)
except KeyError as e:
    print(f"缺少数据字段: {e}")
```

#### MemoryError
内存不足。

```python
try:
    signal = strategy.analyze(stock_code, stock_name, preprocessed_df=df)
except MemoryError as e:
    print(f"内存不足: {e}")
    # 可以尝试减少数据量或清理缓存
    clear_global_cache()
```

### 日志记录

策略提供详细的日志记录：

```python
import logging

# 设置日志级别
logging.getLogger("dual_channel_fibonacci").setLevel(logging.DEBUG)

# 查看详细执行过程
strategy = DualChannelFibonacciStrategy()
signal = strategy.analyze("000001", "平安银行", preprocessed_df=df)
```

## 最佳实践

### 1. 数据准备

```python
# 推荐的数据预处理流程
def preprocess_data(raw_df):
    # 1. 计算EMA指标
    df = calculate_ema_series(raw_df, [144, 169, 576, 676])
    
    # 2. 计算成交量移动平均
    df = calculate_volume_ma(df, [20])
    
    # 3. 计算双通道指标
    channel1_params = {'upper': 144, 'lower': 169}
    channel2_params = {'upper': 576, 'lower': 676}
    df = calculate_dual_channels(df, channel1_params, channel2_params)
    
    # 4. 计算价格位置关系
    df = calculate_price_position(df, 'close', ['channel1', 'channel2'])
    
    return df
```

### 2. 批量处理

```python
def batch_analyze(stock_list, strategy):
    signals = []
    
    for stock_code, stock_name in stock_list:
        try:
            # 获取和预处理数据
            raw_data = get_stock_data(stock_code)
            df = preprocess_data(raw_data)
            
            # 执行策略
            signal = strategy.analyze(stock_code, stock_name, preprocessed_df=df)
            
            if signal:
                signals.append(signal)
                
        except Exception as e:
            print(f"处理 {stock_code} 失败: {e}")
    
    return signals
```

### 3. 配置管理

```python
# 根据市场环境调整配置
def get_market_adapted_config(market_condition):
    if market_condition == "bull":
        return StrategyConfig(volume_ratio=1.1, max_days=80)
    elif market_condition == "bear":
        return StrategyConfig(volume_ratio=1.5, max_days=40)
    else:
        return StrategyConfig()  # 默认配置
```

### 4. 性能监控

```python
import time
from indicators.cache_manager import get_cache_stats

def monitor_performance(strategy, test_data):
    start_time = time.time()
    
    # 执行策略
    results = []
    for stock_code, stock_name, df in test_data:
        signal = strategy.analyze(stock_code, stock_name, preprocessed_df=df)
        results.append(signal)
    
    # 性能统计
    elapsed_time = time.time() - start_time
    cache_stats = get_cache_stats()
    
    print(f"处理时间: {elapsed_time:.2f}秒")
    print(f"平均每只股票: {elapsed_time/len(test_data):.3f}秒")
    print(f"缓存命中率: {cache_stats}")
    
    return results
```

## 版本信息

- **当前版本**: 1.1.0
- **最后更新**: 2025-01-06
- **兼容性**: Python 3.8+, pandas 1.3+, numpy 1.20+

## 更新日志

### v1.1.0 (2025-01-06)
- 添加缓存机制提高性能
- 实现向量化优化
- 完善API文档
- 添加性能监控功能

### v1.0.0 (2025-01-06)
- 初始版本发布
- 实现基础双通道突破逻辑
- 支持配置文件管理
- 提供完整的错误处理