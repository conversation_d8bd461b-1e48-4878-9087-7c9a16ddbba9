#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评分和度量工具

提供背离强度、模式置信度、完成概率等评分功能。

作者: QuantFM Team
创建时间: 2025-08-11
"""

import numpy as np
from typing import Dict, Any, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


def calculate_divergence_strength(price_change: float, indicator_change: float,
                                time_factor: float = 1.0, volume_factor: float = 1.0) -> float:
    """计算背离强度
    
    Args:
        price_change: 价格变化幅度
        indicator_change: 指标变化幅度
        time_factor: 时间因子
        volume_factor: 成交量因子
        
    Returns:
        背离强度 (0-1)
    """
    try:
        # 标准化价格和指标变化
        normalized_price_change = min(abs(price_change), 1.0)
        normalized_indicator_change = min(abs(indicator_change), 1.0)
        
        # 背离强度基于价格和指标变化的差异
        if price_change * indicator_change < 0:  # 方向相反
            # 真正的背离
            base_strength = min(normalized_price_change, normalized_indicator_change)
            direction_bonus = 0.3  # 背离方向奖励
        else:
            # 同向变化，强度较低
            base_strength = (normalized_price_change + normalized_indicator_change) / 2
            direction_bonus = 0.0
        
        # 应用时间和成交量因子
        total_strength = (base_strength + direction_bonus) * time_factor * volume_factor
        
        return min(max(total_strength, 0.0), 1.0)
        
    except Exception as e:
        logger.warning(f"背离强度计算失败: {e}")
        return 0.5


def calculate_pattern_confidence(price_extremes: List[Any], indicator_extremes: List[Any],
                               time_synchronization: float, volume_confirmation: float = 0.5) -> float:
    """计算模式置信度
    
    Args:
        price_extremes: 价格极值点列表
        indicator_extremes: 指标极值点列表
        time_synchronization: 时间同步性
        volume_confirmation: 成交量确认度
        
    Returns:
        模式置信度 (0-1)
    """
    try:
        # 极值点质量评分
        price_quality = np.mean([getattr(pe, 'strength', 0.5) for pe in price_extremes])
        indicator_quality = np.mean([ie.get('strength', 0.5) for ie in indicator_extremes])
        
        # 极值点数量因子
        point_count_factor = min(len(price_extremes) / 2.0, 1.0)  # 2个点为满分
        
        # 综合置信度计算
        confidence = (
            price_quality * 0.25 +
            indicator_quality * 0.25 +
            time_synchronization * 0.3 +
            volume_confirmation * 0.1 +
            point_count_factor * 0.1
        )
        
        return min(max(confidence, 0.0), 1.0)
        
    except Exception as e:
        logger.warning(f"模式置信度计算失败: {e}")
        return 0.5


def calculate_completion_probability(pattern_strength: float, time_factor: float,
                                   market_context: float = 0.5) -> float:
    """计算完成概率
    
    Args:
        pattern_strength: 模式强度
        time_factor: 时间因子
        market_context: 市场环境因子
        
    Returns:
        完成概率 (0-1)
    """
    try:
        # 使用sigmoid函数计算概率
        # 基础概率基于模式强度
        base_prob = 1 / (1 + np.exp(-5 * (pattern_strength - 0.5)))
        
        # 时间衰减调整
        time_adjusted_prob = base_prob * time_factor
        
        # 市场环境调整
        final_prob = time_adjusted_prob * (0.5 + 0.5 * market_context)
        
        return min(max(final_prob, 0.0), 1.0)
        
    except Exception as e:
        logger.warning(f"完成概率计算失败: {e}")
        return 0.5


def assess_signal_quality(signal_strength: float, confirmation_count: int,
                         data_quality: float, time_freshness: float = 1.0) -> float:
    """评估信号质量
    
    Args:
        signal_strength: 信号强度
        confirmation_count: 确认信号数量
        data_quality: 数据质量
        time_freshness: 时间新鲜度
        
    Returns:
        信号质量评分 (0-1)
    """
    try:
        # 确认数量因子
        confirmation_factor = min(confirmation_count / 3.0, 1.0)  # 3个确认为满分
        
        # 综合质量评分
        quality_score = (
            signal_strength * 0.4 +
            confirmation_factor * 0.3 +
            data_quality * 0.2 +
            time_freshness * 0.1
        )
        
        return min(max(quality_score, 0.0), 1.0)
        
    except Exception as e:
        logger.warning(f"信号质量评估失败: {e}")
        return 0.5


def compute_risk_reward_ratio(entry_price: float, stop_loss: float, take_profit: float) -> float:
    """计算风险收益比
    
    Args:
        entry_price: 入场价格
        stop_loss: 止损价格
        take_profit: 止盈价格
        
    Returns:
        风险收益比
    """
    try:
        if entry_price <= 0:
            return 0.0
        
        risk = abs(entry_price - stop_loss)
        reward = abs(take_profit - entry_price)
        
        if risk <= 0:
            return float('inf') if reward > 0 else 0.0
        
        return reward / risk
        
    except Exception as e:
        logger.warning(f"风险收益比计算失败: {e}")
        return 1.0


def calculate_time_decay_factor(time_since_signal: int, half_life: int = 10) -> float:
    """计算时间衰减因子
    
    Args:
        time_since_signal: 距离信号产生的时间
        half_life: 半衰期（周期数）
        
    Returns:
        时间衰减因子 (0-1)
    """
    try:
        # 指数衰减
        decay_factor = np.exp(-np.log(2) * time_since_signal / half_life)
        return min(max(decay_factor, 0.0), 1.0)
        
    except Exception as e:
        logger.warning(f"时间衰减因子计算失败: {e}")
        return 0.5


def calculate_volume_confirmation_weight(current_volume: float, avg_volume: float,
                                       volume_threshold: float = 1.5) -> float:
    """计算成交量确认权重
    
    Args:
        current_volume: 当前成交量
        avg_volume: 平均成交量
        volume_threshold: 成交量阈值倍数
        
    Returns:
        成交量确认权重 (0-1)
    """
    try:
        if avg_volume <= 0:
            return 0.5
        
        volume_ratio = current_volume / avg_volume
        
        # 使用sigmoid函数转换为权重
        weight = 1 / (1 + np.exp(-2 * (volume_ratio - volume_threshold)))
        
        return min(max(weight, 0.0), 1.0)
        
    except Exception as e:
        logger.warning(f"成交量确认权重计算失败: {e}")
        return 0.5


def normalize_score(score: float, min_val: float = 0.0, max_val: float = 1.0) -> float:
    """标准化评分到指定范围
    
    Args:
        score: 原始评分
        min_val: 最小值
        max_val: 最大值
        
    Returns:
        标准化后的评分
    """
    try:
        return min(max(score, min_val), max_val)
    except Exception as e:
        logger.warning(f"评分标准化失败: {e}")
        return (min_val + max_val) / 2


def calculate_multi_dimensional_strength(price_strength: float, indicator_strength: float,
                                       time_strength: float, volume_strength: float,
                                       weights: Optional[List[float]] = None) -> float:
    """计算多维度强度
    
    Args:
        price_strength: 价格强度
        indicator_strength: 指标强度
        time_strength: 时间强度
        volume_strength: 成交量强度
        weights: 权重列表，默认为均等权重
        
    Returns:
        综合强度 (0-1)
    """
    try:
        if weights is None:
            weights = [0.3, 0.3, 0.2, 0.2]  # 默认权重
        
        if len(weights) != 4:
            weights = [0.25, 0.25, 0.25, 0.25]  # 均等权重
        
        # 标准化权重
        total_weight = sum(weights)
        if total_weight > 0:
            weights = [w / total_weight for w in weights]
        else:
            weights = [0.25, 0.25, 0.25, 0.25]
        
        # 计算加权强度
        strengths = [price_strength, indicator_strength, time_strength, volume_strength]
        weighted_strength = sum(s * w for s, w in zip(strengths, weights))
        
        return normalize_score(weighted_strength)
        
    except Exception as e:
        logger.warning(f"多维度强度计算失败: {e}")
        return 0.5


def calculate_pattern_reliability(pattern_type: str, market_regime: str,
                                indicator_name: str) -> float:
    """计算模式可靠性
    
    Args:
        pattern_type: 模式类型
        market_regime: 市场状态
        indicator_name: 指标名称
        
    Returns:
        模式可靠性 (0-1)
    """
    try:
        # 基础可靠性评分
        base_reliability = {
            'bullish_divergence': 0.8,
            'bearish_divergence': 0.8,
            'trend_continuation_bullish': 0.7,
            'trend_continuation_bearish': 0.7
        }.get(pattern_type, 0.6)
        
        # 市场状态调整
        regime_adjustment = {
            'trending_bull': 1.1 if 'bullish' in pattern_type else 0.9,
            'trending_bear': 1.1 if 'bearish' in pattern_type else 0.9,
            'sideways': 1.2 if 'divergence' in pattern_type else 0.8,
            'high_volatility': 0.8
        }.get(market_regime, 1.0)
        
        # 指标特性调整
        indicator_adjustment = {
            'macd': 1.0,
            'rsi': 1.1,  # RSI在极值区域更可靠
            'kdj': 0.9,  # KDJ较为敏感
            'cci': 1.0,
            'obv': 1.1   # OBV量价关系可靠
        }.get(indicator_name.lower(), 1.0)
        
        # 综合可靠性
        reliability = base_reliability * regime_adjustment * indicator_adjustment
        
        return normalize_score(reliability)
        
    except Exception as e:
        logger.warning(f"模式可靠性计算失败: {e}")
        return 0.6


def calculate_signal_urgency(completion_probability: float, time_factor: float,
                           strength: float) -> str:
    """计算信号紧急程度
    
    Args:
        completion_probability: 完成概率
        time_factor: 时间因子
        strength: 信号强度
        
    Returns:
        紧急程度 ('low', 'medium', 'high', 'urgent')
    """
    try:
        # 综合紧急度评分
        urgency_score = (completion_probability * 0.4 + 
                        (1 - time_factor) * 0.3 + 
                        strength * 0.3)
        
        if urgency_score >= 0.8:
            return 'urgent'
        elif urgency_score >= 0.6:
            return 'high'
        elif urgency_score >= 0.4:
            return 'medium'
        else:
            return 'low'
            
    except Exception as e:
        logger.warning(f"信号紧急程度计算失败: {e}")
        return 'medium'


def calculate_confidence_interval(values: List[float], confidence_level: float = 0.95) -> Tuple[float, float]:
    """计算置信区间
    
    Args:
        values: 数值列表
        confidence_level: 置信水平
        
    Returns:
        置信区间 (下界, 上界)
    """
    try:
        if len(values) < 2:
            return (0.0, 1.0)
        
        values_array = np.array(values)
        mean_val = np.mean(values_array)
        std_val = np.std(values_array, ddof=1)
        
        # 使用t分布（简化为正态分布）
        alpha = 1 - confidence_level
        z_score = 1.96  # 95%置信水平的z分数
        
        margin_error = z_score * std_val / np.sqrt(len(values))
        
        lower_bound = mean_val - margin_error
        upper_bound = mean_val + margin_error
        
        return (max(lower_bound, 0.0), min(upper_bound, 1.0))
        
    except Exception as e:
        logger.warning(f"置信区间计算失败: {e}")
        return (0.0, 1.0)
