#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MACD背离检测器

专门处理MACD指标的背离分析，包括DIF线、DEA线和MACD柱状图的分析。

作者: QuantFM Team
创建时间: 2025-08-11
"""

import numpy as np
from dataclasses import dataclass
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


@dataclass
class IndicatorResult:
    """指标分析结果"""
    patterns: List[Dict[str, Any]]      # 检测到的模式
    summary: Dict[str, Any]             # 汇总信息
    signals: List[Dict[str, Any]]       # 信号列表
    metadata: Dict[str, Any]            # 元数据


class MACDDivergenceDetector:
    """MACD背离检测器
    
    核心功能:
    1. DIF线、DEA线、MACD柱状图三线分析
    2. 零轴位置重要性分析
    3. 金叉死叉确认
    4. 柱状图收敛发散模式
    5. 成交量确认
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化检测器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # MACD特定参数
        self.zero_line_significance = self.config.get('zero_line_significance', 1.2)
        self.histogram_threshold = self.config.get('histogram_threshold', 0.1)
        self.cross_confirmation_periods = self.config.get('cross_confirmation_periods', 3)
        self.volume_confirmation_weight = self.config.get('volume_confirmation_weight', 0.3)
        
    def detect(self, macd_data: Dict[str, np.ndarray], price_extremes: List[Any],
              volume: Optional[np.ndarray] = None) -> IndicatorResult:
        """检测MACD背离模式
        
        Args:
            macd_data: MACD数据，包含dif、dea、macd
            price_extremes: 价格极值点
            volume: 成交量数据
            
        Returns:
            指标分析结果
        """
        try:
            dif = macd_data.get('dif', np.array([]))
            dea = macd_data.get('dea', np.array([]))
            macd_hist = macd_data.get('macd', np.array([]))
            
            if len(dif) == 0 or len(dea) == 0 or len(macd_hist) == 0:
                logger.warning("MACD数据不完整")
                return self._get_empty_result()
            
            patterns = []
            signals = []
            
            # 1. 分析DIF线背离
            dif_patterns = self._analyze_dif_divergence(dif, price_extremes)
            patterns.extend(dif_patterns)
            
            # 2. 分析DEA线背离
            dea_patterns = self._analyze_dea_divergence(dea, price_extremes)
            patterns.extend(dea_patterns)
            
            # 3. 分析MACD柱状图模式
            hist_patterns = self._analyze_histogram_patterns(macd_hist, price_extremes)
            patterns.extend(hist_patterns)
            
            # 4. 检测金叉死叉
            cross_signals = self._detect_golden_death_cross(dif, dea)
            signals.extend(cross_signals)
            
            # 5. 零轴分析
            zero_line_analysis = self._analyze_zero_line_position(dif, dea, macd_hist)
            
            # 6. 成交量确认
            if volume is not None:
                patterns = self._add_volume_confirmation(patterns, volume)
            
            # 生成汇总
            summary = self._generate_summary(patterns, signals, zero_line_analysis)
            
            # 构建元数据
            metadata = {
                'data_length': len(dif),
                'zero_line_analysis': zero_line_analysis,
                'has_volume_confirmation': volume is not None,
                'analysis_timestamp': np.datetime64('now').astype(str)
            }
            
            return IndicatorResult(
                patterns=patterns,
                summary=summary,
                signals=signals,
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"MACD背离检测失败: {e}")
            return self._get_empty_result()
    
    def _analyze_dif_divergence(self, dif: np.ndarray, price_extremes: List[Any]) -> List[Dict[str, Any]]:
        """分析DIF线背离"""
        patterns = []
        
        try:
            # 寻找DIF的极值点
            dif_peaks = self._find_local_extremes(dif, 'peak')
            dif_troughs = self._find_local_extremes(dif, 'trough')
            
            # 与价格极值点匹配
            for price_extreme in price_extremes:
                if price_extreme.type == 'peak':
                    # 寻找对应的DIF峰值
                    matching_dif = self._find_matching_extreme(price_extreme, dif_peaks, dif)
                    if matching_dif:
                        pattern = self._create_divergence_pattern(
                            'dif_divergence', price_extreme, matching_dif, dif
                        )
                        if pattern:
                            patterns.append(pattern)
                
                elif price_extreme.type == 'trough':
                    # 寻找对应的DIF谷值
                    matching_dif = self._find_matching_extreme(price_extreme, dif_troughs, dif)
                    if matching_dif:
                        pattern = self._create_divergence_pattern(
                            'dif_divergence', price_extreme, matching_dif, dif
                        )
                        if pattern:
                            patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            logger.warning(f"DIF背离分析失败: {e}")
            return []
    
    def _analyze_dea_divergence(self, dea: np.ndarray, price_extremes: List[Any]) -> List[Dict[str, Any]]:
        """分析DEA线背离"""
        patterns = []
        
        try:
            # 寻找DEA的极值点
            dea_peaks = self._find_local_extremes(dea, 'peak')
            dea_troughs = self._find_local_extremes(dea, 'trough')
            
            # 与价格极值点匹配
            for price_extreme in price_extremes:
                if price_extreme.type == 'peak':
                    matching_dea = self._find_matching_extreme(price_extreme, dea_peaks, dea)
                    if matching_dea:
                        pattern = self._create_divergence_pattern(
                            'dea_divergence', price_extreme, matching_dea, dea
                        )
                        if pattern:
                            patterns.append(pattern)
                
                elif price_extreme.type == 'trough':
                    matching_dea = self._find_matching_extreme(price_extreme, dea_troughs, dea)
                    if matching_dea:
                        pattern = self._create_divergence_pattern(
                            'dea_divergence', price_extreme, matching_dea, dea
                        )
                        if pattern:
                            patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            logger.warning(f"DEA背离分析失败: {e}")
            return []
    
    def _analyze_histogram_patterns(self, macd_hist: np.ndarray, 
                                  price_extremes: List[Any]) -> List[Dict[str, Any]]:
        """分析MACD柱状图模式"""
        patterns = []
        
        try:
            # 检测柱状图的收敛和发散
            convergence_points = self._detect_histogram_convergence(macd_hist)
            divergence_points = self._detect_histogram_divergence(macd_hist)
            
            # 分析与价格的关系
            for conv_point in convergence_points:
                # 寻找对应的价格极值
                nearby_price_extremes = [
                    pe for pe in price_extremes 
                    if abs(pe.index - conv_point['index']) <= 5
                ]
                
                if nearby_price_extremes:
                    pattern = {
                        'type': 'histogram_convergence',
                        'strength': conv_point['strength'],
                        'index': conv_point['index'],
                        'price_extremes': len(nearby_price_extremes),
                        'significance': self._calculate_histogram_significance(conv_point, macd_hist)
                    }
                    patterns.append(pattern)
            
            for div_point in divergence_points:
                nearby_price_extremes = [
                    pe for pe in price_extremes 
                    if abs(pe.index - div_point['index']) <= 5
                ]
                
                if nearby_price_extremes:
                    pattern = {
                        'type': 'histogram_divergence',
                        'strength': div_point['strength'],
                        'index': div_point['index'],
                        'price_extremes': len(nearby_price_extremes),
                        'significance': self._calculate_histogram_significance(div_point, macd_hist)
                    }
                    patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            logger.warning(f"柱状图模式分析失败: {e}")
            return []
    
    def _detect_golden_death_cross(self, dif: np.ndarray, dea: np.ndarray) -> List[Dict[str, Any]]:
        """检测金叉死叉"""
        signals = []
        
        try:
            if len(dif) < 2 or len(dea) < 2:
                return signals
            
            # 检测交叉点
            for i in range(1, len(dif)):
                prev_diff = dif[i-1] - dea[i-1]
                curr_diff = dif[i] - dea[i]
                
                # 金叉：DIF从下方穿越DEA
                if prev_diff <= 0 and curr_diff > 0:
                    # 确认金叉
                    confirmation = self._confirm_cross(dif, dea, i, 'golden')
                    if confirmation['confirmed']:
                        signal = {
                            'type': 'golden_cross',
                            'index': i,
                            'strength': confirmation['strength'],
                            'dif_value': dif[i],
                            'dea_value': dea[i],
                            'zero_line_position': 'above' if dif[i] > 0 else 'below'
                        }
                        signals.append(signal)
                
                # 死叉：DIF从上方穿越DEA
                elif prev_diff >= 0 and curr_diff < 0:
                    confirmation = self._confirm_cross(dif, dea, i, 'death')
                    if confirmation['confirmed']:
                        signal = {
                            'type': 'death_cross',
                            'index': i,
                            'strength': confirmation['strength'],
                            'dif_value': dif[i],
                            'dea_value': dea[i],
                            'zero_line_position': 'above' if dif[i] > 0 else 'below'
                        }
                        signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.warning(f"金叉死叉检测失败: {e}")
            return []
    
    def _analyze_zero_line_position(self, dif: np.ndarray, dea: np.ndarray, 
                                  macd_hist: np.ndarray) -> Dict[str, Any]:
        """分析零轴位置"""
        try:
            current_dif = dif[-1] if len(dif) > 0 else 0
            current_dea = dea[-1] if len(dea) > 0 else 0
            current_hist = macd_hist[-1] if len(macd_hist) > 0 else 0
            
            # 零轴上方/下方的时间比例
            dif_above_zero = np.sum(dif > 0) / len(dif) if len(dif) > 0 else 0
            dea_above_zero = np.sum(dea > 0) / len(dea) if len(dea) > 0 else 0
            
            # 当前趋势强度
            trend_strength = abs(current_dif) + abs(current_dea)
            
            return {
                'current_dif_position': 'above' if current_dif > 0 else 'below',
                'current_dea_position': 'above' if current_dea > 0 else 'below',
                'current_histogram': current_hist,
                'dif_above_zero_ratio': dif_above_zero,
                'dea_above_zero_ratio': dea_above_zero,
                'trend_strength': trend_strength,
                'zero_line_significance': self.zero_line_significance
            }
            
        except Exception as e:
            logger.warning(f"零轴分析失败: {e}")
            return {}
    
    def _find_local_extremes(self, data: np.ndarray, extreme_type: str) -> List[Dict[str, Any]]:
        """寻找局部极值"""
        extremes = []
        window = 3  # 窗口大小
        
        try:
            for i in range(window, len(data) - window):
                is_extreme = True
                current_value = data[i]
                
                # 检查是否为极值
                for j in range(i - window, i + window + 1):
                    if j == i:
                        continue
                    
                    if extreme_type == 'peak':
                        if current_value <= data[j]:
                            is_extreme = False
                            break
                    else:  # trough
                        if current_value >= data[j]:
                            is_extreme = False
                            break
                
                if is_extreme:
                    extremes.append({
                        'index': i,
                        'value': current_value,
                        'type': extreme_type
                    })
            
            return extremes

        except Exception as e:
            logger.warning(f"局部极值检测失败: {e}")
            return []

    def _find_matching_extreme(self, price_extreme: Any, indicator_extremes: List[Dict[str, Any]],
                              indicator_data: np.ndarray) -> Optional[Dict[str, Any]]:
        """寻找匹配的指标极值"""
        if not indicator_extremes:
            return None

        # 寻找时间上最接近的极值点
        min_distance = float('inf')
        best_match = None

        for ind_extreme in indicator_extremes:
            distance = abs(ind_extreme['index'] - price_extreme.index)
            if distance < min_distance and distance <= 10:  # 最大时间间隔
                min_distance = distance
                best_match = ind_extreme

        return best_match

    def _create_divergence_pattern(self, pattern_type: str, price_extreme: Any,
                                 indicator_extreme: Dict[str, Any],
                                 indicator_data: np.ndarray) -> Optional[Dict[str, Any]]:
        """创建背离模式"""
        try:
            # 计算背离强度
            time_diff = abs(indicator_extreme['index'] - price_extreme.index)
            if time_diff > 10:  # 时间差太大
                return None

            # 简化的背离强度计算
            strength = max(0, 1.0 - time_diff / 10.0) * price_extreme.strength

            return {
                'type': pattern_type,
                'strength': strength,
                'price_extreme': {
                    'index': price_extreme.index,
                    'price': price_extreme.price,
                    'type': price_extreme.type
                },
                'indicator_extreme': indicator_extreme,
                'time_synchronization': 1.0 - time_diff / 10.0
            }

        except Exception as e:
            logger.warning(f"背离模式创建失败: {e}")
            return None

    def _detect_histogram_convergence(self, macd_hist: np.ndarray) -> List[Dict[str, Any]]:
        """检测柱状图收敛"""
        convergence_points = []

        try:
            # 寻找柱状图绝对值减小的区域
            abs_hist = np.abs(macd_hist)

            for i in range(5, len(abs_hist) - 5):
                # 检查是否为收敛点（局部最小值）
                window_data = abs_hist[i-5:i+6]
                if abs_hist[i] == np.min(window_data) and abs_hist[i] < self.histogram_threshold:
                    convergence_points.append({
                        'index': i,
                        'value': macd_hist[i],
                        'strength': 1.0 - abs_hist[i] / np.max(abs_hist) if np.max(abs_hist) > 0 else 0
                    })

            return convergence_points

        except Exception as e:
            logger.warning(f"柱状图收敛检测失败: {e}")
            return []

    def _detect_histogram_divergence(self, macd_hist: np.ndarray) -> List[Dict[str, Any]]:
        """检测柱状图发散"""
        divergence_points = []

        try:
            # 寻找柱状图绝对值增大的区域
            abs_hist = np.abs(macd_hist)

            for i in range(5, len(abs_hist) - 5):
                # 检查是否为发散点（局部最大值）
                window_data = abs_hist[i-5:i+6]
                if abs_hist[i] == np.max(window_data) and abs_hist[i] > self.histogram_threshold:
                    divergence_points.append({
                        'index': i,
                        'value': macd_hist[i],
                        'strength': abs_hist[i] / np.max(abs_hist) if np.max(abs_hist) > 0 else 0
                    })

            return divergence_points

        except Exception as e:
            logger.warning(f"柱状图发散检测失败: {e}")
            return []

    def _calculate_histogram_significance(self, point: Dict[str, Any],
                                        macd_hist: np.ndarray) -> float:
        """计算柱状图点的重要性"""
        try:
            value = abs(point['value'])
            max_hist = np.max(np.abs(macd_hist))

            if max_hist == 0:
                return 0.0

            return value / max_hist

        except Exception as e:
            logger.warning(f"柱状图重要性计算失败: {e}")
            return 0.0

    def _confirm_cross(self, dif: np.ndarray, dea: np.ndarray,
                      cross_index: int, cross_type: str) -> Dict[str, Any]:
        """确认交叉信号"""
        try:
            # 检查后续几个周期是否保持交叉状态
            confirmed_periods = 0
            max_check_periods = min(self.cross_confirmation_periods, len(dif) - cross_index - 1)

            for i in range(1, max_check_periods + 1):
                if cross_index + i >= len(dif):
                    break

                if cross_type == 'golden':
                    if dif[cross_index + i] > dea[cross_index + i]:
                        confirmed_periods += 1
                else:  # death
                    if dif[cross_index + i] < dea[cross_index + i]:
                        confirmed_periods += 1

            confirmation_ratio = confirmed_periods / max_check_periods if max_check_periods > 0 else 0

            return {
                'confirmed': confirmation_ratio >= 0.6,
                'strength': confirmation_ratio,
                'confirmed_periods': confirmed_periods
            }

        except Exception as e:
            logger.warning(f"交叉确认失败: {e}")
            return {'confirmed': False, 'strength': 0.0, 'confirmed_periods': 0}

    def _add_volume_confirmation(self, patterns: List[Dict[str, Any]],
                               volume: np.ndarray) -> List[Dict[str, Any]]:
        """添加成交量确认"""
        try:
            for pattern in patterns:
                if 'price_extreme' in pattern:
                    price_index = pattern['price_extreme']['index']
                    if price_index < len(volume):
                        # 计算成交量相对强度
                        window = min(20, len(volume))
                        start_idx = max(0, price_index - window + 1)
                        recent_volume = volume[start_idx:price_index + 1]

                        if len(recent_volume) > 1:
                            current_volume = volume[price_index]
                            avg_volume = np.mean(recent_volume[:-1])

                            if avg_volume > 0:
                                volume_ratio = current_volume / avg_volume
                                volume_confirmation = min(volume_ratio / 2.0, 1.0)  # 标准化到0-1

                                # 调整模式强度
                                original_strength = pattern['strength']
                                pattern['strength'] = (
                                    original_strength * (1 - self.volume_confirmation_weight) +
                                    volume_confirmation * self.volume_confirmation_weight
                                )
                                pattern['volume_confirmation'] = volume_confirmation

            return patterns

        except Exception as e:
            logger.warning(f"成交量确认失败: {e}")
            return patterns

    def _generate_summary(self, patterns: List[Dict[str, Any]], signals: List[Dict[str, Any]],
                         zero_line_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成汇总信息"""
        try:
            total_patterns = len(patterns)
            confirmed_patterns = len([p for p in patterns if p.get('strength', 0) > 0.5])
            max_strength = max([p.get('strength', 0) for p in patterns], default=0)

            # 生成建议
            if max_strength > 0.7 and confirmed_patterns > 0:
                recommendation = 'strong_signal'
            elif max_strength > 0.5:
                recommendation = 'moderate_signal'
            else:
                recommendation = 'weak_signal'

            return {
                'total_patterns': total_patterns,
                'confirmed_patterns': confirmed_patterns,
                'max_strength': max_strength,
                'recommendation': recommendation,
                'golden_crosses': len([s for s in signals if s['type'] == 'golden_cross']),
                'death_crosses': len([s for s in signals if s['type'] == 'death_cross']),
                'zero_line_position': zero_line_analysis.get('current_dif_position', 'unknown'),
                'trend_strength': zero_line_analysis.get('trend_strength', 0)
            }

        except Exception as e:
            logger.warning(f"汇总生成失败: {e}")
            return {
                'total_patterns': 0,
                'confirmed_patterns': 0,
                'max_strength': 0,
                'recommendation': 'no_signal'
            }

    def _get_empty_result(self) -> IndicatorResult:
        """获取空结果"""
        return IndicatorResult(
            patterns=[],
            summary={
                'total_patterns': 0,
                'confirmed_patterns': 0,
                'max_strength': 0,
                'recommendation': 'no_signal'
            },
            signals=[],
            metadata={'error': 'No valid data'}
        )
