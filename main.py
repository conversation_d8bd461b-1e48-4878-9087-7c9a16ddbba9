#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantFM 主程序 - 量化交易系统核心控制器

这是 QuantFM 量化交易系统的主入口程序，负责整个系统的生命周期管理。

核心功能模块：
================

1. 系统初始化和配置管理
   - 加载和验证配置文件（TOML格式）
   - 设置跨平台兼容的日志系统
   - 初始化数据库连接和共享内存系统

2. 多进程管理和调度
   - 市场数据获取进程：实时行情数据获取和处理
   - 成交量比值分析器：监控异常成交量变化
   - 盘后策略调度器：执行技术分析和策略计算
   - 进程间通信、同步和故障恢复

3. 市场数据获取和处理
   - 支持通达信数据源的实时行情获取
   - 历史K线数据（1分钟、5分钟、15分钟、日线）下载和管理
   - 数据质量检查、清洗和标准化
   - 高性能共享内存数据存储（支持10GB+数据量）

4. 策略执行和信号生成
   - 多策略并行执行框架
   - 技术指标计算（基于TA-Lib库）
   - 交易信号生成、过滤和存储
   - 风险控制和信号质量评估

5. 系统监控和资源管理
   - 实时进程状态监控和健康检查
   - 内存使用监控和优化
   - 自动故障检测和恢复机制
   - 优雅的系统关闭和资源清理

技术特性：
==========
- 跨平台兼容（Windows 10/11, Debian Linux）
- 优化的共享内存系统（基于mmap，支持大数据量）
- 多进程并行处理架构（避免GIL限制）
- 完整的类型注解（Type Hints）和错误处理
- 企业级日志记录系统（支持文件和控制台输出）
- 自动化的资源清理机制（防止内存泄漏）
- 编码安全处理（支持emoji和特殊字符）

系统架构：
==========
主进程 (main.py)
├── ProcessManager (进程管理器)
│   ├── 市场数据获取进程 (MarketDataFetcher)
│   ├── 成交量分析进程 (VolumeRatioAnalyzer)
│   └── 盘后策略进程 (AfterMarketScheduler)
├── 共享内存系统 (SharedMemoryManager)
├── 配置管理器 (ConfigManager)
└── 日志系统 (LoggingSystem)

依赖模块：
==========
- multiprocessing: 多进程管理和进程间通信
- schedule: 定时任务调度
- pandas: 数据处理和分析
- numpy: 数值计算
- talib: 技术指标计算
- psycopg2: PostgreSQL数据库连接
- mootdx: 通达信数据接口

配置文件：
==========
- config/main.toml: 主配置文件
- config/market_data_fetcher.toml: 市场数据配置
- config/volume_ratio_analyzer.toml: 成交量分析配置
- config/after_market_scheduler.toml: 盘后策略配置

使用方法：
==========
# 正常启动
python main.py

# 指定配置文件
python main.py --config config/main.toml

# 下载历史数据
python main.py --download-history

# 强制执行盘后策略
python main.py --force-after-market

# 调试模式
python main.py --log-level DEBUG

作者: QuantFM Team
版本: 2.0.1
更新时间: 2025-01-10
许可证: MIT License
"""

import os
import sys
import time
import logging
import argparse
import multiprocessing
import warnings
from typing import Dict, Any, Callable, Tuple, Optional
from multiprocessing import Process
from multiprocessing.synchronize import Event as MPEvent
from multiprocessing import Queue as MPQueue

# 抑制第三方库的pandas链式赋值警告
warnings.filterwarnings("ignore", message=".*ChainedAssignmentError.*", category=FutureWarning)

# 导入项目自定义模块
from config.config_manager import get_config

# 进程管理类
class ProcessManager:
    """
    进程管理器 - 统一管理系统中的所有子进程

    这个类负责管理 QuantFM 系统中的所有子进程，包括：
    - 市场数据获取进程 (MarketDataFetcher)
    - 成交量比值分析器进程 (VolumeRatioAnalyzer)
    - 盘后策略调度器进程 (AfterMarketScheduler)

    主要功能：
    =========
    1. 进程生命周期管理
       - 启动进程：创建进程实例并启动
       - 停止进程：优雅关闭或强制终止
       - 重启进程：故障恢复机制
       - 监控进程：健康状态检查

    2. 进程间通信管理
       - 事件同步：使用 multiprocessing.Event
       - 消息队列：使用 multiprocessing.Queue
       - 共享内存：协调共享内存访问

    3. 错误处理和恢复
       - 进程异常检测
       - 自动重启机制
       - 资源清理和释放

    4. 系统调度管理
       - 定时任务调度（基于 schedule 库）
       - 交易时间管理
       - 进程启停时间控制

    设计模式：
    ==========
    - 单例模式：确保系统中只有一个进程管理器实例
    - 观察者模式：监控进程状态变化
    - 策略模式：不同类型进程使用不同的管理策略

    线程安全：
    ==========
    该类设计为线程安全，可以在多线程环境中使用。
    使用 multiprocessing 模块的同步原语确保进程间通信的安全性。

    注意事项：
    ==========
    - 进程启动顺序很重要：市场数据进程需要首先启动
    - 进程停止需要优雅关闭：避免数据丢失和资源泄漏
    - 共享内存需要正确清理：防止系统重启后的内存泄漏
    """

    def __init__(self):
        """
        初始化进程管理器

        创建必要的数据结构来跟踪和管理所有子进程：
        - processes: 存储进程实例的字典
        - process_events: 存储进程间事件的字典
        - process_queues: 存储进程间队列的字典
        - running: 管理器运行状态标志
        """
        self.logger = logging.getLogger("process_manager")

        # 进程实例字典：{进程名称: Process对象}
        self.processes: Dict[str, Process] = {}

        # 进程事件字典：{进程名称: {事件名称: Event对象}}
        # 用于进程间的同步和通信，如停止信号、暂停信号等
        self.process_events: Dict[str, Dict[str, MPEvent]] = {}

        # 进程队列字典：{进程名称: Queue对象}
        # 用于进程间的消息传递和数据交换
        self.process_queues: Dict[str, MPQueue[Any]] = {}

        # 管理器运行状态标志
        self.running = False

        self.logger.info("进程管理器初始化完成")

    def start_process(self, name: str, target: Callable[..., Any], args: Tuple[Any, ...] = (), max_retries: int = 3, retry_delay: int = 10) -> bool:
        """
        启动一个新的子进程

        这是进程管理器的核心方法，负责创建和启动新的子进程。
        该方法包含完整的错误处理、重试机制和资源管理。

        参数说明：
        =========
        name: str
            进程的唯一标识名称，用于后续的进程管理操作
            例如: "market_data_fetcher", "volume_ratio_analyzer"

        target: Callable[..., Any]
            进程的入口函数，通常是一个独立的函数或类方法
            该函数将在新进程中执行，需要是可序列化的

        args: Tuple[Any, ...] = ()
            传递给目标函数的参数元组
            默认为空元组，参数需要是可序列化的

        max_retries: int = 3
            进程启动失败时的最大重试次数
            用于处理临时性的启动失败（如资源不足）

        retry_delay: int = 10
            重试之间的等待时间（秒）
            给系统时间释放资源或恢复状态

        返回值：
        =======
        bool: 进程启动是否成功
            True: 进程成功启动并运行
            False: 进程启动失败（超过最大重试次数）

        工作流程：
        ==========
        1. 检查进程是否已存在
        2. 创建进程间通信资源（事件、队列）
        3. 创建并配置 Process 对象
        4. 启动进程并验证状态
        5. 注册进程到管理字典
        6. 错误处理和重试机制

        异常处理：
        ==========
        - 捕获进程创建异常
        - 处理进程启动超时
        - 管理资源清理
        - 记录详细的错误日志

        注意事项：
        ==========
        - 目标函数必须是可序列化的（pickle）
        - 参数必须是可序列化的
        - 避免在目标函数中使用全局变量
        - 确保目标函数有适当的异常处理
        """
        if name in self.processes and self.processes[name].is_alive():
            self.logger.warning(f"进程 {name} 已经在运行")
            return False

        # 创建事件和队列
        events: Dict[str, MPEvent] = {
            'stop': multiprocessing.Event(),
            'pause': multiprocessing.Event()
        }
        queue: MPQueue[Any] = multiprocessing.Queue()

        # 更新参数
        process_args = list(args)
        process_args.insert(0, queue)
        process_args.insert(1, events)

        # 创建进程
        process = multiprocessing.Process(
            target=target,
            args=tuple(process_args),
            name=name
        )

        # 启动进程
        try:
            process.start()
        except Exception as e:
            self.logger.error(f"启动进程 {name} 失败: {e}")
            return False

        # 记录进程
        self.processes[name] = process
        self.process_events[name] = events
        self.process_queues[name] = queue

        # 等待进程启动并检查是否存活
        retry_count = 0
        while retry_count < max_retries:
            # 等待一段时间，让进程有机会启动
            time.sleep(1)

            # 检查进程是否存活
            if process.is_alive():
                self.logger.info(f"进程 {name} 启动成功，PID: {process.pid}")
                return True
            else:
                # 进程已经退出，检查退出码
                exit_code = process.exitcode
                if exit_code is not None and exit_code != 0:
                    self.logger.error(f"进程 {name} 启动失败，退出码: {exit_code}")

                    # 如果是共享内存连接失败，不再重试
                    if exit_code == 1:  # 假设退出码1表示共享内存连接失败
                        self.logger.error(f"进程 {name} 无法连接到共享内存，不再重试")
                        break

                    # 重试
                    retry_count += 1
                    if retry_count < max_retries:
                        self.logger.info(f"尝试重新启动进程 {name}，第 {retry_count} 次重试...")
                        time.sleep(retry_delay)

                        # 创建新进程
                        process = multiprocessing.Process(
                            target=target,
                            args=tuple(process_args),
                            name=name
                        )

                        # 启动进程
                        try:
                            process.start()
                        except Exception as e:
                            self.logger.error(f"重新启动进程 {name} 失败: {e}")
                            continue

                        # 更新进程记录
                        self.processes[name] = process
                    else:
                        self.logger.error(f"进程 {name} 启动失败，已达到最大重试次数")

                        # 清理资源
                        self.processes.pop(name, None)
                        self.process_events.pop(name, None)
                        self.process_queues.pop(name, None)

                        return False
                else:
                    # 进程正常退出，不需要重试
                    self.logger.info(f"进程 {name} 正常退出")

                    # 清理资源
                    self.processes.pop(name, None)
                    self.process_events.pop(name, None)
                    self.process_queues.pop(name, None)

                    return True

        # 所有重试都失败了
        self.logger.error(f"进程 {name} 启动失败，已达到最大重试次数")

        # 清理资源
        self.processes.pop(name, None)
        self.process_events.pop(name, None)
        self.process_queues.pop(name, None)

        return False

    def stop_process(self, name: str) -> bool:
        """
        停止一个进程

        参数:
            name: 进程名称
        """
        if name not in self.processes:
            self.logger.warning(f"进程 {name} 不存在")
            return False

        process = self.processes[name]
        events = self.process_events.get(name, {})

        # 发送停止信号
        if 'stop' in events:
            self.logger.info(f"发送停止信号到进程: {name}")
            events['stop'].set()

        # 等待进程结束
        if process.is_alive():
            self.logger.info(f"等待进程 {name} 结束")

            try:
                process.join(timeout=5)
            except Exception as e:
                self.logger.error(f"等待进程 {name} 结束时出错: {e}")

            # 如果超时，强制终止
            if process.is_alive():
                self.logger.warning(f"进程 {name} 未能在5秒内停止，强制终止")
                try:
                    process.terminate()
                    process.join(timeout=2)
                except Exception as e:
                    self.logger.error(f"终止进程 {name} 时出错: {e}")

                # 如果仍然存活，尝试更强力的终止
                if process.is_alive():
                    self.logger.error(f"进程 {name} 无法终止，尝试kill")
                    try:
                        if process.pid is not None:
                            if sys.platform == 'win32':
                                # Windows 系统使用 terminate
                                process.kill()
                            else:
                                # Unix/Linux 系统使用 SIGKILL
                                import signal
                                os.kill(process.pid, signal.SIGKILL)
                    except Exception as e:
                        self.logger.error(f"kill进程 {name} 时出错: {e}")

        # 移除进程记录
        self.processes.pop(name, None)
        self.process_events.pop(name, None)
        self.process_queues.pop(name, None)

        self.logger.info(f"进程 {name} 已停止")
        return True

    def stop_all(self) -> None:
        """停止所有进程"""
        if not self.processes:
            return

        # 首先发送所有停止信号
        for name, events in self.process_events.items():
            if 'stop' in events:
                self.logger.info(f"发送停止信号到进程: {name}")
                events['stop'].set()

        # 然后等待所有进程结束
        for name, process in list(self.processes.items()):
            if process.is_alive():
                self.logger.info(f"等待进程 {name} 结束")

                try:
                    process.join(timeout=5)
                except Exception as e:
                    self.logger.error(f"等待进程 {name} 结束时出错: {e}")

                # 如果超时，强制终止
                if process.is_alive():
                    self.logger.warning(f"进程 {name} 未能在5秒内停止，强制终止")
                    try:
                        process.terminate()
                        process.join(timeout=2)
                    except Exception as e:
                        self.logger.error(f"终止进程 {name} 时出错: {e}")

                    # 如果仍然存活，尝试更强力的终止
                    if process.is_alive():
                        self.logger.error(f"进程 {name} 无法终止，尝试kill")
                        try:
                            if process.pid is not None:
                                if sys.platform == 'win32':
                                    # Windows 系统使用 terminate
                                    process.kill()
                                else:
                                    # Unix/Linux 系统使用 SIGKILL
                                    import signal
                                    os.kill(process.pid, signal.SIGKILL)
                        except Exception as e:
                            self.logger.error(f"kill进程 {name} 时出错: {e}")

        # 清空记录
        self.processes.clear()
        self.process_events.clear()
        self.process_queues.clear()

        self.logger.info("所有进程已停止")

    def is_process_alive(self, name: str) -> bool:
        """
        检查进程是否存活

        参数:
            name: 进程名称
        """
        if name not in self.processes:
            return False

        return self.processes[name].is_alive()

    def pause_process(self, name: str) -> bool:
        """
        暂停一个进程

        参数:
            name: 进程名称
        """
        if name not in self.process_events:
            self.logger.warning(f"进程 {name} 不存在")
            return False

        events = self.process_events[name]
        if 'pause' in events:
            self.logger.info(f"发送暂停信号到进程: {name}")
            events['pause'].set()
            return True

        return False

    def resume_process(self, name: str) -> bool:
        """
        恢复一个进程

        参数:
            name: 进程名称
        """
        if name not in self.process_events:
            self.logger.warning(f"进程 {name} 不存在")
            return False

        events = self.process_events[name]
        if 'pause' in events and events['pause'].is_set():
            self.logger.info(f"发送恢复信号到进程: {name}")
            events['pause'].clear()
            return True

        return False

# 跨平台安全输出函数
def safe_print(message: str, level: str = "INFO") -> None:
    """
    跨平台安全的输出函数，避免编码问题

    Args:
        message: 要输出的消息
        level: 日志级别标识
    """
    try:
        # 移除可能导致编码问题的字符
        safe_message = message.encode('ascii', errors='ignore').decode('ascii')
        if safe_message != message:
            # 如果有非ASCII字符被移除，添加标识
            safe_message = f"[{level}] {safe_message}"
        print(safe_message)
    except Exception:
        # 如果仍然有问题，使用最基本的输出
        print(f"[{level}] <message encoding error>")

def safe_log_format(message: str) -> str:
    """
    格式化日志消息，确保跨平台兼容

    Args:
        message: 原始消息

    Returns:
        格式化后的安全消息
    """
    # 替换可能有问题的字符
    replacements = {
        '🚀': '[启动]',
        '✅': '[成功]',
        '❌': '[错误]',
        '⚠️': '[警告]',
        '🧹': '[清理]',
        '🎉': '[完成]',
        '📊': '[统计]',
        '🔧': '[配置]',
        '💡': '[提示]',
        '🎯': '[目标]'
    }

    safe_message = message
    for emoji, replacement in replacements.items():
        safe_message = safe_message.replace(emoji, replacement)

    return safe_message

# 配置日志
def setup_logging(log_level: str = "INFO"):
    """
    设置跨平台兼容的日志配置

    Args:
        log_level: 日志级别
    """
    # 创建日志目录
    os.makedirs("logs", exist_ok=True)

    # 设置日志级别
    level = getattr(logging, log_level.upper(), logging.INFO)

    # 清除现有的处理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建自定义格式化器
    class SafeFormatter(logging.Formatter):
        def format(self, record: logging.LogRecord) -> str:
            # 确保消息是跨平台安全的
            if hasattr(record, 'msg') and isinstance(record.msg, str):
                record.msg = safe_log_format(record.msg)
            result = super().format(record)
            return safe_log_format(result)

    formatter = SafeFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 文件处理器 - 使用UTF-8编码
    file_handler = logging.FileHandler("logs/stockfm.log", mode='w', encoding='utf-8')
    file_handler.setFormatter(formatter)

    # 控制台处理器 - 简化编码处理
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    # 配置根日志记录器
    root_logger.setLevel(level)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

    # 设置所有日志记录器的级别
    for name in logging.root.manager.loggerDict:
        logger_instance = logging.getLogger(name)
        # 对于第三方库，设置更高的日志级别
        if any(lib in name for lib in ['tdxpy', 'mootdx', 'urllib3', 'requests', 'schedule']):
            logger_instance.setLevel(logging.WARNING)  # 第三方库只显示警告和错误
        else:
            logger_instance.setLevel(level)  # 项目代码使用配置的级别

    # 输出日志配置信息 - 使用安全输出
    safe_print(f"日志级别设置为: {log_level}", "INFO")
    safe_print(f"日志文件路径: logs/stockfm.log", "INFO")

# 主程序类
class StockFM:
    """
    QuantFM 主程序控制器类

    这是整个 QuantFM 系统的核心控制器，负责协调和管理所有子系统。
    该类实现了系统的完整生命周期管理，从初始化到优雅关闭。

    核心职责：
    =========
    1. 系统初始化
       - 加载和验证配置文件
       - 初始化日志系统
       - 设置环境变量和系统参数
       - 创建进程管理器

    2. 进程生命周期管理
       - 市场数据获取进程：负责实时数据获取和历史数据下载
       - 成交量比值分析器：监控异常成交量变化
       - 盘后策略调度器：执行技术分析和策略计算

    3. 定时任务调度
       - 交易时间管理：根据交易日历控制进程启停
       - 数据更新任务：定期更新交易日历和基础数据
       - 系统维护任务：内存清理、日志轮转等

    4. 系统监控和故障恢复
       - 进程健康检查
       - 自动重启机制
       - 资源使用监控
       - 异常处理和恢复

    5. 优雅关闭和资源清理
       - 进程安全停止
       - 共享内存清理
       - 数据库连接关闭
       - 临时文件清理

    设计特点：
    ==========
    - 模块化设计：各个功能模块相对独立
    - 配置驱动：通过配置文件控制系统行为
    - 容错性强：具备完善的错误处理和恢复机制
    - 可扩展性：易于添加新的进程和功能模块
    - 跨平台：支持 Windows 和 Linux 系统

    配置文件结构：
    ==============
    - main.toml: 主配置文件，包含系统级设置
    - market_data_fetcher.toml: 市场数据获取配置
    - volume_ratio_analyzer.toml: 成交量分析配置
    - after_market_scheduler.toml: 盘后策略配置

    使用示例：
    ==========
    ```python
    # 创建系统实例
    stockfm = StockFM("config/main.toml")

    # 启动系统
    stockfm.start()

    # 系统运行...

    # 停止系统
    stockfm.stop()
    ```

    注意事项：
    ==========
    - 确保配置文件路径正确且可读
    - 系统需要足够的内存（建议8GB+）
    - 确保数据库连接配置正确
    - 在生产环境中建议使用 systemd 管理
    """

    def __init__(self, config_path: str = "config/main.toml"):
        """
        初始化 QuantFM 主程序控制器

        执行系统初始化的所有必要步骤，包括配置加载、日志设置、
        进程管理器创建等。这个方法不会启动任何子进程，只是准备
        系统运行所需的基础设施。

        参数说明：
        =========
        config_path: str = "config/main.toml"
            主配置文件的路径，支持相对路径和绝对路径
            配置文件必须是有效的 TOML 格式

        初始化步骤：
        ===========
        1. 设置优化系统环境变量
        2. 创建日志记录器
        3. 加载和验证配置文件
        4. 配置日志系统
        5. 创建进程管理器
        6. 初始化系统状态变量

        异常处理：
        ==========
        如果配置文件不存在或格式错误，会抛出相应异常
        如果日志系统初始化失败，会使用默认配置

        注意事项：
        ==========
        - 该方法是线程安全的
        - 可以多次调用，但只有第一次调用有效
        - 不会启动任何子进程或网络连接
        """
        # 确保优化系统环境变量已设置
        import os
        if 'QUANTFM_USE_OPTIMIZED_MEMORY' not in os.environ:
            os.environ['QUANTFM_USE_OPTIMIZED_MEMORY'] = '1'

        # 设置日志
        self.logger = logging.getLogger("stockfm")

        # 加载配置
        self.config = get_config(None, config_path)

        # 设置日志级别
        log_level = self.config.get("logging", {}).get("level", "INFO")
        setup_logging(log_level)

        # 初始化进程管理器
        self.process_manager = ProcessManager()

        # 初始化市场数据获取进程
        self.market_data_process: Optional[Process] = None

        # 数据就绪标志文件已删除 - 不再需要等待历史数据加载

        # KDB+ 相关组件已删除

        # 初始化标志
        self.running = False

        self.logger.info("StockFM 主程序初始化完成")

    def _update_trade_calendar(self):
        """强制更新交易日历"""
        try:
            self.logger.info("开始强制更新交易日历...")

            # 导入交易日历模块
            from utils.time import TradeCalendar

            # 创建交易日历实例
            trade_calendar = TradeCalendar()

            # 强制更新交易日历
            update_success = trade_calendar.refresh_trade_dates(force=True)

            if update_success:
                self.logger.info("交易日历更新成功")
            else:
                self.logger.warning("交易日历更新失败")

            return update_success
        except Exception as e:
            self.logger.error(f"更新交易日历时发生错误: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def _check_trade_date_and_schedule_processes(self):
        """
        检查当前日期是否是交易日，如果是则调度进程启动
        """
        self.logger.info("检查当前日期是否是交易日...")

        # 导入交易日历模块
        from utils.time import TradeCalendar

        # 创建交易日历实例
        trade_calendar = TradeCalendar()

        # 检查当前日期是否是交易日
        is_trade_date = trade_calendar.is_trade_date()

        if is_trade_date:
            self.logger.info("当前日期是交易日，调度进程启动")

            # 调度market_data_fetcher进程在指定时间启动
            self._schedule_market_data_fetcher()

            # 调度market_data_fetcher进程在23点停止
            self._schedule_market_data_fetcher_stop()

            # 调度volume_ratio_analyzer进程
            self._schedule_volume_ratio_analyzer()

            # 调度成交量激增处理器进程
            self._schedule_volume_surge_processor()

            # 调度盘后策略选股进程
            self._schedule_after_market_strategy()

            # 启动监控进程（如果启用）
            self._start_monitoring_processes()

            # KDB+ 触发器监听器已删除
        else:
            self.logger.info("当前日期不是交易日，等待下一次检查")
            # 不做任何操作，等到第二天早上6点再次检查

    def _schedule_market_data_fetcher(self):
        """
        根据主配置文件中的统一调度时间配置market_data_fetcher进程启动
        """
        import schedule

        # 获取统一调度配置
        schedule_config = self.config.get("process_schedule", {}).get("market_data_fetcher", {})
        start_time = schedule_config.get("start_time", "09:05:00")

        self.logger.info(f"调度market_data_fetcher进程在 {start_time} 启动")

        # 清除之前可能存在的启动调度
        schedule.clear(tag="market_data_fetcher_start")

        # 设置在指定时间启动进程
        schedule.every().day.at(start_time).do(self._start_market_data_fetcher).tag("market_data_fetcher_start")  # type: ignore

    def _schedule_market_data_fetcher_stop(self):
        """
        根据主配置文件中的统一调度时间配置market_data_fetcher进程停止
        """
        import schedule

        # 获取统一调度配置
        schedule_config = self.config.get("process_schedule", {}).get("market_data_fetcher", {})
        stop_time = schedule_config.get("stop_time", "15:20:00")

        self.logger.info(f"调度market_data_fetcher进程在 {stop_time} 停止")

        # 清除之前可能存在的停止调度
        schedule.clear(tag="market_data_fetcher_stop")

        # 设置在指定时间停止进程
        schedule.every().day.at(stop_time).do(self._stop_market_data_fetcher).tag("market_data_fetcher_stop")  # type: ignore

    def _stop_market_data_fetcher(self):
        """
        停止market_data_fetcher进程，断开所有连接并销毁进程
        """
        if self.market_data_process and self.market_data_process.is_alive():
            self.logger.info("停止market_data_fetcher进程，断开所有连接并销毁进程")

            try:
                # 强制终止进程
                self.logger.info("终止market_data_fetcher进程")
                self.market_data_process.terminate()
                self.market_data_process.join(timeout=5)

                # 检查进程是否已终止
                if self.market_data_process.is_alive():
                    self.logger.error("无法终止market_data_fetcher进程")
                else:
                    self.logger.info("market_data_fetcher进程已终止")
                    self.market_data_process: Optional[Process] = None
            except Exception as e:
                self.logger.error(f"停止market_data_fetcher进程时发生错误: {e}")
                import traceback
                self.logger.error(traceback.format_exc())
        else:
            self.logger.info("没有正在运行的market_data_fetcher进程")

    def start(self):
        """
        启动 StockFM 系统
        """
        if self.running:
            self.logger.warning("StockFM 系统已经在运行")
            return

        self.logger.info("启动 StockFM 系统")

        # 设置运行标志
        self.running = True

        # 检查当前是否是交易日，以及当前时间是否在market_data_fetcher运行时间段内
        from utils.time import TradeCalendar
        import datetime
        trade_calendar = TradeCalendar()
        now = trade_calendar.get_current_time()
        now_time = now.time()

        # 从统一配置获取market_data_fetcher的运行时间段
        schedule_config = self.config.get("process_schedule", {}).get("market_data_fetcher", {})
        start_time_str = schedule_config.get("start_time", "09:05:00")
        stop_time_str = schedule_config.get("stop_time", "15:20:00")

        # 解析时间
        start_time_parts = list(map(int, start_time_str.split(":")))
        stop_time_parts = list(map(int, stop_time_str.split(":")))
        start_time = datetime.time(start_time_parts[0], start_time_parts[1], start_time_parts[2] if len(start_time_parts) > 2 else 0)
        stop_time = datetime.time(stop_time_parts[0], stop_time_parts[1], stop_time_parts[2] if len(stop_time_parts) > 2 else 0)

        # 检查是否是交易日且在运行时间段内（即使错过了启动时间也启动）
        if trade_calendar.is_trade_date() and start_time <= now_time <= stop_time:
            self.logger.info(f"当前是交易日，且时间在 {start_time_str} 至 {stop_time_str} 之间，立即启动 market_data_fetcher 进程")
            self._start_market_data_fetcher()
        else:
            self.logger.info(f"当前不是交易日或时间不在 {start_time_str} 至 {stop_time_str} 之间，等待调度器启动 market_data_fetcher 进程")

        # 检查volume_ratio_analyzer是否也应该立即启动
        vra_schedule_config = self.config.get("process_schedule", {}).get("volume_ratio_analyzer", {})
        vra_start_time_str = vra_schedule_config.get("start_time", "09:28:00")
        vra_stop_time_str = vra_schedule_config.get("stop_time", "15:00:00")

        vra_start_time_parts = list(map(int, vra_start_time_str.split(":")))
        vra_stop_time_parts = list(map(int, vra_stop_time_str.split(":")))
        vra_start_time = datetime.time(vra_start_time_parts[0], vra_start_time_parts[1], vra_start_time_parts[2] if len(vra_start_time_parts) > 2 else 0)
        vra_stop_time = datetime.time(vra_stop_time_parts[0], vra_stop_time_parts[1], vra_stop_time_parts[2] if len(vra_stop_time_parts) > 2 else 0)

        # 检查是否是交易日且在volume_ratio_analyzer运行时间段内（即使错过了启动时间也启动）
        if trade_calendar.is_trade_date() and vra_start_time <= now_time <= vra_stop_time:
            self.logger.info(f"当前是交易日，且时间在 {vra_start_time_str} 至 {vra_stop_time_str} 之间，立即启动 volume_ratio_analyzer 进程")
            self._start_volume_ratio_analyzer()
        else:
            self.logger.info(f"当前不是交易日或时间不在 {vra_start_time_str} 至 {vra_stop_time_str} 之间，等待调度器启动 volume_ratio_analyzer 进程")

        # [新增] 检查volume_surge_processor是否也应该立即启动
        if self.config.get("processes", {}).get("volume_surge_processor", True):
            # volume_surge_processor 在 09:27:00 启动，运行到 15:05:00
            vsp_start_time = datetime.time(9, 27, 0)
            vsp_stop_time = datetime.time(15, 5, 0)

            if trade_calendar.is_trade_date() and vsp_start_time <= now_time <= vsp_stop_time:
                self.logger.info(f"当前是交易日，且时间在 09:27:00 至 15:05:00 之间，立即启动 volume_surge_processor 进程")
                self._start_volume_surge_processor()
            else:
                self.logger.info(f"当前不是交易日或时间不在 09:27:00 至 15:05:00 之间，等待调度器启动 volume_surge_processor 进程")

        # 启动定时任务线程
        import threading
        self.scheduler_thread = threading.Thread(target=self._run_scheduler)
        self.scheduler_thread.daemon = True
        self.scheduler_thread.start()
        self.logger.info("定时任务调度器已启动")

        self.logger.info("StockFM 系统启动完成")

    def _run_scheduler(self):
        """运行定时任务调度器"""
        import schedule

        # 设置定时更新交易日历任务
        schedule.every().day.at("01:00").do(self._update_trade_calendar)  # type: ignore
        self.logger.info("已设置每天凌晨1点自动更新交易日历")

        # 添加每天6点检查交易日并调度进程的任务
        schedule.every().day.at("06:00").do(self._check_trade_date_and_schedule_processes)  # type: ignore
        self.logger.info("已设置每天6点检查交易日并调度进程")

        # 立即执行一次交易日检查和进程调度
        self._check_trade_date_and_schedule_processes()

        while self.running:
            schedule.run_pending()
            time.sleep(1)


    def stop(self):
        """
        停止 StockFM 系统
        """
        if not self.running:
            self.logger.warning("StockFM 系统已经停止")
            return

        self.logger.info("停止 StockFM 系统")

        # 设置运行标志
        self.running = False

        # 停止所有监控进程
        self.process_manager.stop_all()

        # KDB+ 触发器监听器已删除

        # 停止市场数据获取进程
        if self.market_data_process and self.market_data_process.is_alive():
            self.logger.info("停止市场数据获取进程")
            self.market_data_process.terminate()
            self.market_data_process.join(timeout=5)

        # 停止成交量比值分析器进程
        if hasattr(self, 'volume_ratio_analyzer_process') and self.volume_ratio_analyzer_process and self.volume_ratio_analyzer_process.is_alive():
            self.logger.info("停止成交量比值分析器进程")
            self.volume_ratio_analyzer_process.terminate()
            self.volume_ratio_analyzer_process.join(timeout=30)

        # 停止成交量激增处理器进程
        self._stop_volume_surge_processor()

        # 等待调度器线程结束
        if hasattr(self, 'scheduler_thread') and self.scheduler_thread.is_alive():
            self.logger.info("等待调度器线程结束")
            self.scheduler_thread.join(timeout=5)

        self.logger.info("StockFM 系统停止完成")

    def _start_market_data_fetcher(self):
        """
        启动市场数据获取进程

        优化说明：
        1. 避免直接传递 multiprocessing.Event 对象，防止序列化问题
        2. 使用进程间通信文件来标识数据就绪状态
        3. 增加详细的错误处理和日志记录
        """
        # 检查主配置文件中的进程开关
        if not self.config.get("processes", {}).get("market_data_fetcher", True):
            self.logger.info("市场数据获取进程在主配置中被禁用")
            return

        self.logger.info("启动市场数据获取进程")

        # 检查是否已经有一个进程在运行
        if hasattr(self, 'market_data_process') and self.market_data_process and self.market_data_process.is_alive():
            self.logger.info("市场数据获取进程已经在运行，不再创建新的进程")
            return

        try:
            # 创建数据就绪标志文件路径（避免传递 Event 对象）
            # 创建进程，使用独立函数避免序列化问题
            self.market_data_process = multiprocessing.Process(
                target=_run_market_data_fetcher_process,
                name="MarketDataFetcher"
            )

            # 启动进程
            self.market_data_process.start()

            self.logger.info(f"市场数据获取进程启动成功，PID: {self.market_data_process.pid}")

        except Exception as e:
            self.logger.error(f"启动市场数据获取进程失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            raise

    def _start_monitoring_processes(self):
        """
        启动所有启用的监控进程
        """
        self.logger.info("开始启动监控进程...")

        # 盘后任务调度器已删除，后期重构

        self.logger.info("监控进程启动完成")

    def _schedule_volume_ratio_analyzer(self):
        """
        根据主配置文件中的统一调度时间配置成交量比值分析器进程
        """
        # 检查主配置文件中的进程开关
        if not self.config.get("processes", {}).get("volume_ratio_analyzer", True):
            self.logger.info("成交量比值分析器在主配置中被禁用")
            return

        import schedule

        # 获取统一调度配置
        schedule_config = self.config.get("process_schedule", {}).get("volume_ratio_analyzer", {})
        start_time = schedule_config.get("start_time", "09:28:00")
        stop_time = schedule_config.get("stop_time", "15:00:00")

        self.logger.info(f"调度成交量比值分析器进程在 {start_time} 启动，{stop_time} 停止")

        # 清除之前可能存在的调度
        schedule.clear(tag="volume_ratio_analyzer_start")
        schedule.clear(tag="volume_ratio_analyzer_stop")

        # 设置启动和停止调度
        schedule.every().day.at(start_time).do(self._start_volume_ratio_analyzer).tag("volume_ratio_analyzer_start")  # type: ignore
        schedule.every().day.at(stop_time).do(self._stop_volume_ratio_analyzer).tag("volume_ratio_analyzer_stop")  # type: ignore

    def _start_volume_ratio_analyzer(self):
        """
        启动成交量比值分析器进程
        """
        # 检查主配置文件中的进程开关
        if not self.config.get("processes", {}).get("volume_ratio_analyzer", True):
            self.logger.info("成交量比值分析器在主配置中被禁用")
            return

        self.logger.info("启动成交量比值分析器进程")

        # 检查依赖进程是否运行
        schedule_config = self.config.get("process_schedule", {}).get("volume_ratio_analyzer", {})
        depends_on = schedule_config.get("depends_on", [])

        for dependency in depends_on:
            if dependency == "market_data_fetcher":
                # 首先检查进程是否存活
                if not (hasattr(self, 'market_data_process') and
                       self.market_data_process and
                       self.market_data_process.is_alive()):
                    self.logger.warning(f"依赖进程 {dependency} 未运行，延迟启动成交量比值分析器")
                    # 延迟5分钟重试
                    import schedule
                    schedule.every(5).minutes.do(self._retry_start_volume_ratio_analyzer).tag("volume_ratio_analyzer_retry")  # type: ignore
                    return

                # 历史数据加载检查已删除 - 直接启动成交量比值分析器

        # 检查是否已经有一个进程在运行
        if hasattr(self, 'volume_ratio_analyzer_process') and self.volume_ratio_analyzer_process and self.volume_ratio_analyzer_process.is_alive():
            self.logger.info("成交量比值分析器进程已经在运行，不再创建新的进程")
            return

        # 创建进程（使用独立函数避免序列化问题）
        self.volume_ratio_analyzer_process = multiprocessing.Process(
            target=_run_volume_ratio_analyzer_process,
            name="VolumeRatioAnalyzer"
        )

        # 启动进程
        self.volume_ratio_analyzer_process.start()

        self.logger.info(f"成交量比值分析器进程启动成功，PID: {self.volume_ratio_analyzer_process.pid}")

    # _wait_for_market_data_ready方法已删除 - 不再需要等待历史数据加载

    def _retry_start_volume_ratio_analyzer(self):
        """
        重试启动成交量比值分析器
        """
        import schedule
        # 清除重试调度
        schedule.clear(tag="volume_ratio_analyzer_retry")
        # 重新尝试启动
        self._start_volume_ratio_analyzer()

    def _stop_volume_ratio_analyzer(self):
        """
        停止成交量比值分析器进程
        """
        self.logger.info("停止成交量比值分析器进程")

        if hasattr(self, 'volume_ratio_analyzer_process') and self.volume_ratio_analyzer_process:
            if self.volume_ratio_analyzer_process.is_alive():
                self.logger.info("正在优雅停止成交量比值分析器进程...")

                # 发送终止信号
                self.volume_ratio_analyzer_process.terminate()

                # 等待进程结束
                self.volume_ratio_analyzer_process.join(timeout=30)

                if self.volume_ratio_analyzer_process.is_alive():
                    self.logger.warning("成交量比值分析器进程未在超时时间内结束，强制杀死")
                    self.volume_ratio_analyzer_process.kill()
                    self.volume_ratio_analyzer_process.join()

                self.logger.info("成交量比值分析器进程已停止")
            else:
                self.logger.info("成交量比值分析器进程已经停止")
        else:
            self.logger.info("成交量比值分析器进程未启动")

    def _schedule_volume_surge_processor(self):
        """
        调度成交量激增处理器进程在9:27启动
        """
        import schedule

        # 检查主配置文件中的进程开关
        if not self.config.get("processes", {}).get("volume_surge_processor", True):
            self.logger.info("成交量激增处理器进程在主配置中被禁用")
            return

        self.logger.info("调度成交量激增处理器进程在 09:27:00 启动")

        # 清除之前可能存在的调度
        schedule.clear(tag="volume_surge_processor_start")

        # 设置在9:27启动成交量激增处理器进程
        schedule.every().day.at("09:27:00").do(self._start_volume_surge_processor).tag("volume_surge_processor_start")  # type: ignore

    def _start_volume_surge_processor(self):
        """
        启动成交量激增处理器进程
        """
        self.logger.info("🚀 启动成交量激增处理器进程")

        try:
            # 导入成交量激增处理器进程
            from processes.volume_surge_processor import VolumeSurgeProcessor

            # 创建并启动进程
            self.volume_surge_processor = VolumeSurgeProcessor()
            self.volume_surge_processor.start()

            self.logger.info("✅ 成交量激增处理器进程启动成功")

        except Exception as e:
            self.logger.error(f"启动成交量激增处理器进程异常: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    def _stop_volume_surge_processor(self):
        """
        停止成交量激增处理器进程
        """
        self.logger.info("停止成交量激增处理器进程")

        try:
            if hasattr(self, 'volume_surge_processor') and self.volume_surge_processor:
                self.volume_surge_processor.stop()
                self.volume_surge_processor = None
                self.logger.info("✅ 成交量激增处理器进程已停止")
            else:
                self.logger.info("成交量激增处理器进程未启动")
        except Exception as e:
            self.logger.error(f"停止成交量激增处理器进程异常: {e}")

    def _schedule_after_market_strategy(self):
        """
        调度盘后策略选股进程在17:00启动
        """
        import schedule

        self.logger.info("调度盘后策略选股进程在 17:00:00 启动")

        # 清除之前可能存在的调度
        schedule.clear(tag="after_market_strategy_start")

        # 设置在17:00启动盘后策略选股进程
        schedule.every().day.at("17:00:00").do(self._start_after_market_strategy).tag("after_market_strategy_start")  # type: ignore

    def _start_after_market_strategy(self):
        """
        启动盘后策略选股进程
        """
        self.logger.info("🚀 启动盘后策略选股进程")

        try:
            # 导入盘后策略选股进程
            from processes.after_market_schedule import AfterMarketScheduler

            # 创建并运行调度器
            scheduler = AfterMarketScheduler()
            success = scheduler.run()

            if success:
                self.logger.info("✅ 盘后策略选股执行成功")
            else:
                self.logger.error("❌ 盘后策略选股执行失败")

        except Exception as e:
            self.logger.error(f"启动盘后策略选股进程异常: {e}")
            import traceback
            self.logger.error(traceback.format_exc())










    def _start_kdb_triggers_listener(self):
        """
        启动KDB+ 触发器监听器
        """
        # 检查主配置文件中的进程开关
        if not self.config.get("processes", {}).get("kdb_triggers_listener", True):
            self.logger.info("KDB+ 触发器监听器在主配置中被禁用")
            return

        self.logger.info("KDB+ 触发器监听器已删除，跳过启动")

    # AfterMarketSchedulerProcess相关代码已删除，后期重构

    # _run_after_market_scheduler方法已删除，后期重构

def force_run_after_market_strategy() -> bool:
    """
    强制执行盘后策略
    
    Returns:
        bool: 执行是否成功
    """
    logger = logging.getLogger("main")
    logger.info("开始强制执行盘后策略")
    
    try:
        # 导入盘后策略调度器
        from processes.after_market_schedule import AfterMarketScheduler
        
        # 创建调度器实例
        scheduler = AfterMarketScheduler()
        
        # 执行策略
        success = scheduler.run()
        
        if success:
            logger.info("盘后策略执行成功")
        else:
            logger.error("盘后策略执行失败")
        
        return success
        
    except Exception as e:
        logger.error(f"强制执行盘后策略时发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False



def _run_volume_ratio_analyzer_process() -> None:
    """
    运行成交量比值分析器进程（独立函数，避免序列化问题）
    """
    try:
        # 在新进程中重新设置日志
        import logging
        import os
        import time

        # 创建日志目录
        os.makedirs("logs", exist_ok=True)

        # 清除现有的处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler("logs/volume_ratio_analyzer_process.log", mode='w'),
                logging.StreamHandler()
            ]
        )

        # 设置所有日志记录器的级别
        for name in logging.root.manager.loggerDict:
            logging.getLogger(name).setLevel(logging.INFO)

        from utils.logger import get_logger
        logger = get_logger(f"VolumeRatioAnalyzer_{os.getpid()}")
        logger.info("成交量比值分析器进程启动")


        # VolumeRatioAnalyzer类暂未实现，使用占位符
        logger.info("VolumeRatioAnalyzer功能暂未实现，进程将保持运行状态")

        # 保持运行直到收到停止信号
        running = True
        try:
            while running:
                time.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            logger.info("收到停止信号")
            running = False

        logger.info("成交量比值分析器进程正常结束")

    except Exception as e:
        # 在新进程中创建日志记录器
        try:
            import os
            from utils.logger import get_logger
            logger = get_logger(f"VolumeRatioAnalyzer_{os.getpid()}")
            logger.error(f"成交量比值分析器运行异常: {e}")
        except:
            print(f"成交量比值分析器运行异常: {e}")
        import traceback
        traceback.print_exc()


# _wait_for_historical_data_loaded函数已删除 - 不再需要等待历史数据加载


def _run_market_data_fetcher_process() -> None:
    """
    运行市场数据获取进程（独立函数，避免序列化问题）

    优化说明：
    1. 简化启动流程，不再需要等待历史数据加载
    2. 增加详细的错误处理和进程状态监控
    3. 专注于tick数据获取和数据库存储
    """
    process_logger: Any = None
    fetcher: Any = None
    try:
        # 设置进程级别的日志配置
        import logging
        import time
        # 创建日志目录
        import os
        os.makedirs("logs", exist_ok=True)

        # 清除现有的处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler("logs/market_data_fetcher_process.log", mode='w'),  # 使用'w'模式，覆盖现有日志文件
                logging.StreamHandler()
            ]
        )

        # 设置所有日志记录器的级别
        for name in logging.root.manager.loggerDict:
            logger_instance = logging.getLogger(name)
            # 对于第三方库，设置更高的日志级别
            if any(lib in name for lib in ['tdxpy', 'mootdx', 'urllib3', 'requests']):
                logger_instance.setLevel(logging.WARNING)  # 第三方库只显示警告和错误
            else:
                logger_instance.setLevel(logging.INFO)  # 项目代码显示INFO级别

        process_logger = logging.getLogger("market_data_fetcher_process")
        process_logger.setLevel(logging.INFO)

        process_logger.info("市场数据获取进程启动")

        # 在子进程中导入MarketDataFetcher，避免序列化问题
        from processes.market_data_fetcher import MarketDataFetcher

        # 创建市场数据获取器
        process_logger.info("创建市场数据获取器")
        fetcher = MarketDataFetcher()
        process_logger.info("市场数据获取器创建完成")

        # 启动市场数据获取器，强制启动，不检查交易时间
        process_logger.info("启动市场数据获取器，强制启动模式")
        fetcher.start(force_start=True)
        process_logger.info("市场数据获取器启动完成")

        # 历史数据加载和数据就绪标志文件已删除 - 直接开始数据获取

        # 保持进程运行
        process_logger.info("进入主循环，按Ctrl+C退出")
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        if process_logger is not None:
            process_logger.info("市场数据获取进程接收到退出信号")
    except Exception as e:
        if process_logger is not None:
            process_logger.error(f"市场数据获取进程发生错误: {e}")
            import traceback
            process_logger.error(traceback.format_exc())
    finally:
        # 停止市场数据获取器
        if fetcher is not None:
            if process_logger is not None:
                process_logger.info("停止市场数据获取器")
            try:
                fetcher.stop()
                if process_logger is not None:
                    process_logger.info("市场数据获取器已停止")
            except Exception as e:
                if process_logger is not None:
                    process_logger.error(f"停止市场数据获取器时发生错误: {e}")

        if process_logger is not None:
            process_logger.info("市场数据获取进程退出")

def parse_args() -> argparse.Namespace:
    """
    解析命令行参数

    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description="StockFM - 股票行情监控系统")
    parser.add_argument("--config", "-c", default="config/main.toml", help="配置文件路径")
    parser.add_argument("--log-level", "-l", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], help="日志级别")
    parser.add_argument("--download-history", action="store_true", help="下载历史K线数据（1min、5min、15min和日线）")
    parser.add_argument("--force-after-market", action="store_true", help="强制执行盘后策略任务（包括EMA策略和技术指标计算）")
    return parser.parse_args()

def close_all_db_connections() -> None:
    """
    关闭所有数据库连接

    清理TimescaleDB连接池和其他数据库连接
    """
    logger = logging.getLogger("db_cleanup")
    logger.info("开始关闭所有数据库连接...")

    # PyKX连接池已删除

    try:
        # 关闭数据库连接池
        from data.db_manager import close_all_db_connections
        close_all_db_connections()
        logger.info("[成功] 数据库连接池已关闭")
    except Exception as e:
        logger.error(f"[错误] 关闭数据库连接池失败: {e}")

    # DolphinDB连接器已删除

    logger.info("数据库连接清理完成")

def cleanup_resources() -> None:
    """
    系统资源清理函数
    
    在程序退出时执行资源清理，包括：
    1. 数据库连接清理
    2. 日志记录清理状态
    3. 其他系统资源清理
    
    该函数会在以下情况被调用：
    - 程序正常退出时
    - 接收到终止信号时
    - 发生未捕获异常时
    """
    logger = logging.getLogger("cleanup")
    
    try:
        logger.info("🧹 开始系统资源清理...")
        
        # 清理数据库连接
        try:
            logger.info("清理数据库连接...")
            close_all_db_connections()
            logger.info("✅ 数据库连接已关闭")
        except Exception as e:
            logger.error(f"❌ 数据库连接清理失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
        
        # 清理其他系统资源
        try:
            logger.info("清理其他系统资源...")
            # 这里可以添加其他资源清理逻辑
            logger.info("✅ 系统资源清理完成")
        except Exception as e:
            logger.error(f"❌ 系统资源清理失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
    except Exception as e:
        logger.error(f"❌ 资源清理过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
    
    logger.info("🏁 系统资源清理完成")

def download_historical_data() -> bool:
    """
    下载历史K线数据（1min、5min、15min和日线）
    """
    logger = logging.getLogger("main")
    logger.info("开始下载历史K线数据...")

    try:
        # 导入MarketDataFetcher
        from processes.market_data_fetcher import MarketDataFetcher

        # 创建MarketDataFetcher实例
        logger.info("创建MarketDataFetcher实例...")
        fetcher = MarketDataFetcher()
        logger.info("MarketDataFetcher实例创建成功")

        # 下载历史K线数据
        logger.info("开始下载历史K线数据（初始化模式）...")
        # 使用 getattr 安全访问私有方法
        download_method = getattr(fetcher, '_download_klines_optimized', None)
        if download_method:
            download_method(init_mode=True)
            logger.info("历史K线数据下载完成")
        else:
            logger.warning("_download_klines_optimized 方法不存在，跳过历史数据下载")

    except Exception as e:
        logger.error(f"下载历史K线数据失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

    return True







def main() -> None:
    """
    QuantFM 系统主入口函数

    这是整个 QuantFM 系统的启动入口点，负责：
    1. 系统环境初始化
    2. 多进程启动方法配置
    3. 命令行参数解析
    4. 系统实例创建和启动
    5. 信号处理和优雅关闭
    6. 异常处理和资源清理

    执行流程：
    =========
    1. 设置系统环境变量
    2. 配置多进程启动方法（跨平台兼容）
    3. 解析命令行参数
    4. 根据参数执行相应操作：
       - 下载历史数据
       - 启动完整系统
       - 强制执行盘后策略
    5. 处理键盘中断和异常
    6. 执行资源清理

    特殊处理：
    ==========
    - 跨平台多进程启动方法配置
    - 编码安全的输出处理
    - 优雅的系统关闭机制
    - 完整的异常捕获和日志记录

    环境要求：
    ==========
    - Python 3.8+
    - 足够的系统内存（建议8GB+）
    - 有效的配置文件
    - 数据库连接（PostgreSQL）

    注意事项：
    ==========
    - 该函数会阻塞直到系统关闭
    - 使用 Ctrl+C 可以优雅关闭系统
    - 所有异常都会被捕获和记录
    - 系统关闭时会自动清理资源
    """
    # 设置系统环境变量
    import os
    os.environ['PYTHONPATH'] = os.path.dirname(os.path.abspath(__file__))

    # 解析命令行参数
    args = parse_args()

    # 设置日志
    setup_logging(args.log_level)

    # 获取日志记录器
    logger = logging.getLogger("main")

    # 记录系统启动信息
    logger.info("🚀 QuantFM 系统启动")

    # 注册程序退出时的清理函数
    import atexit
    atexit.register(cleanup_resources)

    # 如果指定了下载历史数据参数，则执行下载操作并退出
    if args.download_history:
        logger.info("检测到--download-history参数，将下载历史K线数据")
        success = download_historical_data()
        if success:
            logger.info("历史K线数据下载成功，程序退出")
        else:
            logger.error("历史K线数据下载失败，程序退出")
        return
        
    # 如果指定了强制执行盘后策略参数，则执行策略并退出
    if args.force_after_market:
        logger.info("检测到--force-after-market参数，开始执行盘后策略")
        success = force_run_after_market_strategy()
        if success:
            logger.info("盘后策略执行成功")
        else:
            logger.error("盘后策略执行失败")
        logger.info("程序退出")
        return

    stockfm: Optional[StockFM] = None
    try:
        # 创建 StockFM 实例
        stockfm = StockFM(args.config)

        # 启动系统
        stockfm.start()

        # 等待用户输入退出
        logger.info("StockFM 系统已启动，按 Ctrl+C 退出")
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("接收到退出信号")
    except Exception as e:
        logger.error(f"发生错误: {e}")
    finally:
        # 停止系统
        if stockfm is not None:
            stockfm.stop()

        logger.info("程序退出")

def _setup_multiprocessing() -> bool:
    """
    设置多进程启动方法和相关配置

    在 Debian/Linux 系统上，使用 'spawn' 方法可以避免共享内存序列化问题
    """
    import platform

    try:
        # 获取当前系统信息
        system_info = platform.system()
        print(f"检测到系统: {system_info}")

        # 根据系统类型设置最佳的多进程启动方法
        if sys.platform == 'win32':
            # Windows 系统使用 spawn
            try:
                multiprocessing.set_start_method('spawn')
                safe_print("✅ Windows 系统: 多进程启动方法设置为 spawn", "INFO")
            except RuntimeError as e:
                safe_print(f"Windows 系统: 多进程启动方法已设置 - {e}", "INFO")
        else:
            # Linux/Unix 系统优先使用 spawn，避免共享内存序列化问题
            try:
                multiprocessing.set_start_method('spawn')
                safe_print("✅ Linux/Unix 系统: 多进程启动方法设置为 spawn (避免序列化问题)", "INFO")
            except RuntimeError as e:
                if "context has already been set" in str(e):
                    safe_print(f"Linux/Unix 系统: 多进程启动方法已设置 - {e}", "INFO")
                else:
                    # 如果 spawn 不可用，尝试 forkserver
                    try:
                        multiprocessing.set_start_method('forkserver')
                        safe_print("✅ Linux/Unix 系统: 多进程启动方法设置为 forkserver (备选方案)", "INFO")
                    except RuntimeError as e2:
                        safe_print(f"⚠️ Linux/Unix 系统: 使用默认多进程启动方法 - {e2}", "WARNING")

        # 设置多进程相关的环境变量
        import os
        os.environ['PYTHONHASHSEED'] = '0'  # 确保哈希一致性

        return True

    except Exception as e:
        safe_print(f"❌ 设置多进程启动方法失败: {e}", "ERROR")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    try:
        # 1. 首先设置多进程启动方法
        print("=" * 60)
        print("QuantFM 系统启动")
        print("=" * 60)

        if not _setup_multiprocessing():
            safe_print("❌ 多进程设置失败，但继续尝试启动...", "ERROR")

        # 2. 运行主函数
        print("\n开始运行主函数...")
        main()

    except RuntimeError as e:
        # 处理多进程启动方法已设置的情况
        if "context has already been set" in str(e):
            print(f"多进程启动方法已设置，继续执行: {e}")
            main()
        else:
            safe_print(f"❌ 运行主函数错误: {e}", "ERROR")
            import traceback
            traceback.print_exc()
    except Exception as e:
        safe_print(f"❌ 程序启动失败: {e}", "ERROR")
        import traceback
        traceback.print_exc()



