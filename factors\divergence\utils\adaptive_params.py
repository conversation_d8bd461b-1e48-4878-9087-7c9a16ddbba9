#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应参数管理器

根据市场状态、波动率和数据长度动态调整参数。

作者: QuantFM Team
创建时间: 2025-08-11
"""

import numpy as np
from typing import Dict, Any, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class AdaptiveParameterManager:
    """自适应参数管理器
    
    核心功能:
    1. 基于ATR的波动率调整
    2. 市场状态检测和参数适配
    3. 数据长度自适应
    4. 参数边界检查
    5. 性能影响考虑
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化参数管理器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 基础参数
        self.base_distance = self.config.get('base_distance', 5)
        self.base_prominence_ratio = self.config.get('base_prominence_ratio', 0.02)
        self.volatility_sensitivity = self.config.get('volatility_sensitivity', 1.5)
        
        # 参数边界
        self.min_distance = self.config.get('min_distance', 2)
        self.max_distance = self.config.get('max_distance', 20)
        self.min_prominence_ratio = self.config.get('min_prominence_ratio', 0.005)
        self.max_prominence_ratio = self.config.get('max_prominence_ratio', 0.1)
        
        # 市场状态阈值
        self.trending_threshold = self.config.get('trending_threshold', 0.7)
        self.volatile_threshold = self.config.get('volatile_threshold', 2.0)
        
        # 缓存
        self._atr_cache = {}
        self._regime_cache = {}
        
    def adjust_for_volatility(self, prices: np.ndarray, high: Optional[np.ndarray] = None,
                             low: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """基于波动率调整参数
        
        Args:
            prices: 价格序列
            high: 最高价序列
            low: 最低价序列
            
        Returns:
            调整后的参数字典
        """
        try:
            # 计算ATR
            atr = self._calculate_atr(prices, high, low)
            
            # 计算波动率因子
            price_level = np.mean(prices[-20:]) if len(prices) >= 20 else np.mean(prices)
            volatility_factor = atr / price_level if price_level > 0 else 0.02
            
            # 调整距离参数
            adjusted_distance = self._adjust_distance_for_volatility(volatility_factor)
            
            # 调整突出度参数
            adjusted_prominence = self._adjust_prominence_for_volatility(volatility_factor, price_level)
            
            return {
                'distance': adjusted_distance,
                'prominence': adjusted_prominence,
                'volatility_factor': volatility_factor,
                'atr': atr,
                'adjustment_reason': 'volatility_based'
            }
            
        except Exception as e:
            logger.error(f"波动率参数调整失败: {e}")
            return self._get_default_params()
    
    def adjust_for_market_regime(self, prices: np.ndarray, volumes: np.ndarray) -> Dict[str, Any]:
        """基于市场状态调整参数
        
        Args:
            prices: 价格序列
            volumes: 成交量序列
            
        Returns:
            调整后的参数字典
        """
        try:
            # 检测市场状态
            regime = self._detect_market_regime(prices, volumes)
            
            # 根据市场状态调整参数
            if regime == 'trending_bull' or regime == 'trending_bear':
                # 趋势市场：增加距离，降低突出度要求
                distance_multiplier = 1.3
                prominence_multiplier = 0.8
            elif regime == 'sideways':
                # 横盘市场：减少距离，提高突出度要求
                distance_multiplier = 0.8
                prominence_multiplier = 1.2
            elif regime == 'high_volatility':
                # 高波动市场：增加距离和突出度要求
                distance_multiplier = 1.5
                prominence_multiplier = 1.3
            else:
                # 默认情况
                distance_multiplier = 1.0
                prominence_multiplier = 1.0
            
            adjusted_distance = int(self.base_distance * distance_multiplier)
            adjusted_prominence = self.base_prominence_ratio * prominence_multiplier
            
            # 应用边界限制
            adjusted_distance = max(self.min_distance, min(self.max_distance, adjusted_distance))
            adjusted_prominence = max(self.min_prominence_ratio, 
                                    min(self.max_prominence_ratio, adjusted_prominence))
            
            return {
                'distance': adjusted_distance,
                'prominence_ratio': adjusted_prominence,
                'market_regime': regime,
                'distance_multiplier': distance_multiplier,
                'prominence_multiplier': prominence_multiplier,
                'adjustment_reason': 'market_regime_based'
            }
            
        except Exception as e:
            logger.error(f"市场状态参数调整失败: {e}")
            return self._get_default_params()
    
    def adjust_for_data_length(self, data_length: int) -> Dict[str, Any]:
        """基于数据长度调整参数
        
        Args:
            data_length: 数据长度
            
        Returns:
            调整后的参数字典
        """
        try:
            # 数据长度调整策略
            if data_length < 30:
                # 数据不足：放宽参数
                distance_factor = 0.7
                prominence_factor = 0.8
                adjustment_reason = 'insufficient_data'
            elif data_length < 60:
                # 数据较少：轻微放宽
                distance_factor = 0.85
                prominence_factor = 0.9
                adjustment_reason = 'limited_data'
            elif data_length > 200:
                # 数据充足：可以更严格
                distance_factor = 1.2
                prominence_factor = 1.1
                adjustment_reason = 'abundant_data'
            else:
                # 正常数据量
                distance_factor = 1.0
                prominence_factor = 1.0
                adjustment_reason = 'normal_data_length'
            
            adjusted_distance = int(self.base_distance * distance_factor)
            adjusted_prominence = self.base_prominence_ratio * prominence_factor
            
            # 应用边界限制
            adjusted_distance = max(self.min_distance, min(self.max_distance, adjusted_distance))
            adjusted_prominence = max(self.min_prominence_ratio, 
                                    min(self.max_prominence_ratio, adjusted_prominence))
            
            return {
                'distance': adjusted_distance,
                'prominence_ratio': adjusted_prominence,
                'data_length': data_length,
                'distance_factor': distance_factor,
                'prominence_factor': prominence_factor,
                'adjustment_reason': adjustment_reason
            }
            
        except Exception as e:
            logger.error(f"数据长度参数调整失败: {e}")
            return self._get_default_params()
    
    def get_optimal_distance(self, prices: np.ndarray, volumes: np.ndarray,
                           high: Optional[np.ndarray] = None, 
                           low: Optional[np.ndarray] = None) -> int:
        """获取最优距离参数
        
        Args:
            prices: 价格序列
            volumes: 成交量序列
            high: 最高价序列
            low: 最低价序列
            
        Returns:
            最优距离参数
        """
        try:
            # 综合多种调整策略
            volatility_params = self.adjust_for_volatility(prices, high, low)
            regime_params = self.adjust_for_market_regime(prices, volumes)
            length_params = self.adjust_for_data_length(len(prices))
            
            # 加权平均
            distances = [
                volatility_params['distance'],
                regime_params['distance'],
                length_params['distance']
            ]
            
            weights = [0.4, 0.4, 0.2]  # 波动率和市场状态权重更高
            
            optimal_distance = int(np.average(distances, weights=weights))
            
            # 边界检查
            optimal_distance = max(self.min_distance, min(self.max_distance, optimal_distance))
            
            return optimal_distance
            
        except Exception as e:
            logger.error(f"最优距离参数计算失败: {e}")
            return self.base_distance
    
    def get_optimal_prominence(self, prices: np.ndarray, volumes: np.ndarray,
                             high: Optional[np.ndarray] = None, 
                             low: Optional[np.ndarray] = None) -> float:
        """获取最优突出度参数
        
        Args:
            prices: 价格序列
            volumes: 成交量序列
            high: 最高价序列
            low: 最低价序列
            
        Returns:
            最优突出度参数
        """
        try:
            # 综合多种调整策略
            volatility_params = self.adjust_for_volatility(prices, high, low)
            regime_params = self.adjust_for_market_regime(prices, volumes)
            length_params = self.adjust_for_data_length(len(prices))
            
            # 计算绝对突出度值
            price_level = np.mean(prices[-20:]) if len(prices) >= 20 else np.mean(prices)
            
            prominences = [
                volatility_params['prominence'],
                regime_params['prominence_ratio'] * price_level,
                length_params['prominence_ratio'] * price_level
            ]
            
            weights = [0.4, 0.4, 0.2]
            
            optimal_prominence = np.average(prominences, weights=weights)
            
            # 边界检查
            min_prominence = self.min_prominence_ratio * price_level
            max_prominence = self.max_prominence_ratio * price_level
            optimal_prominence = max(min_prominence, min(max_prominence, optimal_prominence))
            
            return optimal_prominence
            
        except Exception as e:
            logger.error(f"最优突出度参数计算失败: {e}")
            return self.base_prominence_ratio * np.mean(prices)
    
    def _calculate_atr(self, prices: np.ndarray, high: Optional[np.ndarray] = None,
                      low: Optional[np.ndarray] = None, period: int = 14) -> float:
        """计算平均真实波幅"""
        cache_key = f"atr_{len(prices)}_{period}"
        if cache_key in self._atr_cache:
            return self._atr_cache[cache_key]
        
        try:
            if high is None or low is None:
                # 简化ATR计算
                price_changes = np.abs(np.diff(prices))
                atr = np.mean(price_changes[-period:]) if len(price_changes) >= period else np.mean(price_changes)
            else:
                # 标准ATR计算
                high_low = high - low
                high_close = np.abs(high[1:] - prices[:-1])
                low_close = np.abs(low[1:] - prices[:-1])
                
                true_range = np.maximum(high_low[1:], np.maximum(high_close, low_close))
                atr = np.mean(true_range[-period:]) if len(true_range) >= period else np.mean(true_range)
            
            self._atr_cache[cache_key] = atr
            return atr
            
        except Exception as e:
            logger.warning(f"ATR计算失败: {e}")
            return np.std(prices) if len(prices) > 1 else 0.01
    
    def _detect_market_regime(self, prices: np.ndarray, volumes: np.ndarray) -> str:
        """检测市场状态"""
        cache_key = f"regime_{len(prices)}"
        if cache_key in self._regime_cache:
            return self._regime_cache[cache_key]
        
        try:
            if len(prices) < 20:
                return 'insufficient_data'
            
            # 趋势强度分析
            recent_prices = prices[-20:]
            price_trend = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
            
            # 波动率分析
            price_volatility = np.std(np.diff(recent_prices)) / np.mean(recent_prices)
            
            # 成交量分析
            recent_volumes = volumes[-20:]
            volume_trend = np.mean(recent_volumes[-5:]) / np.mean(recent_volumes[:-5])
            
            # 状态判断
            if abs(price_trend) > 0.1 and price_volatility < self.volatile_threshold:
                if price_trend > 0:
                    regime = 'trending_bull'
                else:
                    regime = 'trending_bear'
            elif price_volatility > self.volatile_threshold:
                regime = 'high_volatility'
            else:
                regime = 'sideways'
            
            self._regime_cache[cache_key] = regime
            return regime
            
        except Exception as e:
            logger.warning(f"市场状态检测失败: {e}")
            return 'unknown'
    
    def _adjust_distance_for_volatility(self, volatility_factor: float) -> int:
        """基于波动率调整距离参数"""
        # 高波动率 -> 增加距离
        distance_multiplier = 1.0 + volatility_factor * self.volatility_sensitivity
        adjusted_distance = int(self.base_distance * distance_multiplier)
        
        return max(self.min_distance, min(self.max_distance, adjusted_distance))
    
    def _adjust_prominence_for_volatility(self, volatility_factor: float, price_level: float) -> float:
        """基于波动率调整突出度参数"""
        # 高波动率 -> 增加突出度要求
        prominence_multiplier = 1.0 + volatility_factor * self.volatility_sensitivity * 0.5
        adjusted_prominence = self.base_prominence_ratio * prominence_multiplier * price_level
        
        min_prominence = self.min_prominence_ratio * price_level
        max_prominence = self.max_prominence_ratio * price_level
        
        return max(min_prominence, min(max_prominence, adjusted_prominence))
    
    def _get_default_params(self) -> Dict[str, Any]:
        """获取默认参数"""
        return {
            'distance': self.base_distance,
            'prominence_ratio': self.base_prominence_ratio,
            'adjustment_reason': 'default_fallback'
        }
