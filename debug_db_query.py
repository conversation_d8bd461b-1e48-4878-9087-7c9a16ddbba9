#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据库查询

调试数据库查询问题。

作者: QuantFM Team
创建时间: 2025-08-08
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import get_db_manager
from utils.logger import get_logger


def debug_db_query():
    """调试数据库查询"""
    logger = get_logger("DebugDBQuery")
    
    try:
        db_manager = get_db_manager()
        
        # 测试基本查询
        print("1. 测试基本查询...")
        test_sql = "SELECT 1 as test_value"
        result = db_manager.fetch_one(test_sql)
        print(f"基本查询结果: {result}")
        print(f"结果类型: {type(result)}")
        
        # 检查表是否存在
        print("\n2. 检查strategy_configs表是否存在...")
        table_check_sql = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'strategy_configs'
            );
        """
        table_exists = db_manager.fetch_one(table_check_sql)
        print(f"表存在检查: {table_exists}")
        
        if table_exists and table_exists.get('exists', False):
            # 查看表结构
            print("\n3. 查看表结构...")
            structure_sql = """
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'strategy_configs'
                ORDER BY ordinal_position;
            """
            columns = db_manager.fetch_all(structure_sql)
            print("表结构:")
            for col in columns:
                if isinstance(col, dict):
                    print(f"  {col.get('column_name')}: {col.get('data_type')}")
                else:
                    print(f"  {col[0]}: {col[1]}")
            
            # 查看表数据
            print("\n4. 查看表数据...")
            data_sql = "SELECT * FROM strategy_configs LIMIT 5"
            rows = db_manager.fetch_all(data_sql)
            print(f"表中有 {len(rows)} 行数据")
            
            for i, row in enumerate(rows):
                print(f"行 {i+1}: {row}")
                print(f"行类型: {type(row)}")
                if isinstance(row, dict):
                    for key, value in row.items():
                        print(f"  {key}: {value} (类型: {type(value)})")
                elif row:
                    for j, col in enumerate(row):
                        print(f"  列 {j}: {col} (类型: {type(col)})")
        
        return True
        
    except Exception as e:
        logger.error(f"调试数据库查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    debug_db_query()
