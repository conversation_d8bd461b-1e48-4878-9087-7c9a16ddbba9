# 市场数据获取器配置

[market_data_fetcher]
# 基本配置
fetch_interval = 2              # 获取间隔（秒）
batch_size = 50                 # 每批次获取的股票数量
max_threads = 100               # 最大线程数
max_retries = 5                 # 最大重试次数
retry_delay = 0.3               # 重试延迟（秒）
timeout = 10                    # 连接超时时间（秒）

# 数据保存配置
save_tick_to_db = true          # 保存tick数据到数据库
save_kline_to_db = true         # 保存K线数据到数据库
data_save_interval = 30         # 数据保存间隔（分钟）

# K线计算配置
enable_kline_calculation = true # 启用K线计算
kline_periods = ["5min", "1day"] # 支持的K线周期

# 监控配置
initialization_time = "09:05:00" # 初始化时间
start_time = "09:28:00"         # 启动时间
market_open_time = "09:30:00"   # 开盘时间
market_close_time = "15:01:00"  # 收盘时间
lunch_break_start = "11:31:00"  # 午休开始时间
lunch_break_end = "13:00:00"    # 午休结束时间

# 历史数据下载配置
download_history_time = "16:30" # 每天下载历史数据的时间
init_download_history = false   # 启动时下载历史数据
enable_post_market_download = false # 盘后下载收盘数据

[logging]
level = "DEBUG"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
file_path = "logs/market_data_fetcher.log"




