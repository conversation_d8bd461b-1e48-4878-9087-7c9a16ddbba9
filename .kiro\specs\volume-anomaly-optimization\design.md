# Design Document

## Overview

本设计文档描述了实时成交量异动检测进程的优化方案。当前系统虽然功能完整，但在高并发处理、资源利用、错误恢复等方面存在性能瓶颈。通过系统性的架构重构和性能优化，将显著提升系统的处理能力、稳定性和可维护性。

### 当前系统分析

**优点：**
- 多线程并发处理架构
- 完整的开盘期和常规期检测策略
- mootdx连接池管理
- 飞书通知集成

**问题：**
- 线程管理缺乏动态调整能力
- 数据库连接未使用连接池
- 缓存机制简单，缺乏过期和压缩策略
- 错误处理机制不够健壮
- 缺乏系统性能监控
- 算法效率有待提升

## Architecture

### 整体架构设计

```mermaid
graph TB
    subgraph "应用层"
        A[主进程管理器] --> B[线程池管理器]
        A --> C[配置管理器]
        A --> D[监控管理器]
    end
    
    subgraph "处理层"
        B --> E[工作线程池]
        E --> F[开盘期处理器]
        E --> G[常规期处理器]
        F --> H[异动检测引擎]
        G --> H
    end
    
    subgraph "数据层"
        H --> I[缓存管理器]
        H --> J[数据库连接池]
        I --> K[多级内存缓存]
        I --> L[本地文件缓存]
        J --> M[TimescaleDB]
    end
    
    subgraph "外部服务"
        H --> N[mootdx连接池]
        H --> O[飞书通知服务]
        N --> P[通达信服务器]
        O --> Q[飞书API]
    end
    
    subgraph "监控层"
        D --> R[性能监控]
        D --> S[日志管理]
        D --> T[告警系统]
    end
```

### 核心组件架构

#### 1. 主进程管理器 (MainProcessManager)
- 负责整个系统的生命周期管理
- 协调各个子系统的启动和停止
- 处理系统级别的配置变更和热重载

#### 2. 线程池管理器 (ThreadPoolManager)
- 动态管理工作线程数量
- 实现负载均衡和任务分配
- 提供线程健康检查和自动恢复

#### 3. 异动检测引擎 (AnomalyDetectionEngine)
- 核心业务逻辑处理
- 支持多种检测算法
- 提供可扩展的策略接口

## Components and Interfaces

### 1. 线程池管理组件

```python
class OptimizedThreadPoolManager:
    """优化的线程池管理器"""
    
    def __init__(self, config: ThreadPoolConfig):
        self.config = config
        self.thread_pool: List[WorkerThread] = []
        self.task_queue = queue.PriorityQueue()
        self.load_balancer = LoadBalancer()
        self.health_monitor = ThreadHealthMonitor()
    
    def adjust_pool_size(self, target_size: int) -> bool:
        """动态调整线程池大小"""
        pass
    
    def distribute_tasks(self, tasks: List[Task]) -> None:
        """智能任务分配"""
        pass
    
    def get_pool_statistics(self) -> PoolStatistics:
        """获取线程池统计信息"""
        pass
```

### 2. 高性能缓存管理组件

```python
class OptimizedCacheManager:
    """优化的纯内存缓存管理器"""
    
    def __init__(self, config: CacheConfig):
        self.l1_cache = LRUCache(config.hot_cache_size)
        self.l2_cache = CompressedCache(config.cold_cache_size)
        self.file_cache = LocalFileCache(config.cache_dir)
        self.compression = CacheCompression()
        self.metrics = CacheMetrics()
    
    def get(self, key: str) -> Optional[Any]:
        """多级缓存获取：热缓存 -> 冷缓存 -> 文件缓存"""
        pass
    
    def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """智能缓存设置，根据访问频率选择存储层级"""
        pass
    
    def evict_expired(self) -> int:
        """清理过期缓存，支持后台异步清理"""
        pass
    
    def compress_cold_cache(self) -> int:
        """压缩冷缓存数据，释放内存"""
        pass
```

### 3. 数据库连接池组件

```python
class OptimizedDatabasePool:
    """优化的数据库连接池"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.connection_pool = ConnectionPool()
        self.query_cache = QueryResultCache()
        self.batch_processor = BatchProcessor()
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """执行查询（带缓存）"""
        pass
    
    def batch_insert(self, table: str, records: List[Dict]) -> bool:
        """批量插入"""
        pass
    
    def get_connection_stats(self) -> ConnectionStats:
        """获取连接统计"""
        pass
```

### 4. 异动检测算法组件

```python
class OptimizedAnomalyDetector:
    """优化的异动检测器"""
    
    def __init__(self, config: DetectionConfig):
        self.sliding_window = SlidingWindowCalculator()
        self.ring_buffer = RingBuffer(config.buffer_size)
        self.vectorized_calc = VectorizedCalculator()
    
    def detect_opening_anomaly(self, stock_data: StockData) -> AnomalyResult:
        """开盘期异动检测"""
        pass
    
    def detect_regular_anomaly(self, stock_data: StockData) -> AnomalyResult:
        """常规期异动检测"""
        pass
    
    def calculate_volume_ratio(self, current: float, historical: List[float]) -> float:
        """向量化计算成交量比值"""
        pass
```

### 5. 错误处理和恢复组件

```python
class RobustErrorHandler:
    """健壮的错误处理器"""
    
    def __init__(self, config: ErrorHandlingConfig):
        self.retry_strategy = ExponentialBackoffRetry()
        self.circuit_breaker = CircuitBreaker()
        self.fallback_manager = FallbackManager()
    
    def handle_network_error(self, error: Exception) -> RecoveryAction:
        """处理网络错误"""
        pass
    
    def handle_database_error(self, error: Exception) -> RecoveryAction:
        """处理数据库错误"""
        pass
    
    def enter_degraded_mode(self) -> None:
        """进入降级模式"""
        pass
```

## Data Models

### 1. 配置数据模型

```python
@dataclass
class OptimizedConfig:
    """优化后的配置模型"""
    
    # 线程池配置
    thread_pool: ThreadPoolConfig
    
    # 缓存配置
    cache: CacheConfig
    
    # 数据库配置
    database: DatabaseConfig
    
    # 检测策略配置
    detection: DetectionConfig
    
    # 监控配置
    monitoring: MonitoringConfig
    
    # 错误处理配置
    error_handling: ErrorHandlingConfig

@dataclass
class ThreadPoolConfig:
    min_threads: int = 5
    max_threads: int = 20
    core_threads: int = 10
    queue_size: int = 1000
    keep_alive_time: int = 60
    load_balance_strategy: str = "round_robin"
    health_check_interval: int = 30

@dataclass
class CacheConfig:
    # 内存缓存配置
    hot_cache_size: int = 1024 * 1024 * 50   # 50MB 热缓存
    cold_cache_size: int = 1024 * 1024 * 100  # 100MB 冷缓存
    
    # 本地文件缓存配置
    cache_dir: str = "./cache"
    file_cache_enabled: bool = True
    max_file_cache_size: int = 1024 * 1024 * 500  # 500MB
    
    # 缓存策略配置
    compression_enabled: bool = True
    compression_level: int = 6  # zlib压缩级别
    ttl_default: int = 3600
    eviction_policy: str = "lru"
    
    # 清理配置
    cleanup_interval: int = 300  # 5分钟清理一次
    max_memory_usage: float = 0.8  # 最大内存使用率80%
```

### 2. 缓存实现细节

#### 多级内存缓存架构

```python
class LRUCache:
    """热缓存：最近最少使用算法"""
    
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.cache = OrderedDict()
        self.access_count = defaultdict(int)
    
    def get(self, key: str) -> Optional[Any]:
        if key in self.cache:
            # 移动到末尾（最近使用）
            self.cache.move_to_end(key)
            self.access_count[key] += 1
            return self.cache[key]
        return None
    
    def set(self, key: str, value: Any) -> None:
        if key in self.cache:
            self.cache.move_to_end(key)
        elif len(self.cache) >= self.capacity:
            # 移除最少使用的项
            oldest = next(iter(self.cache))
            del self.cache[oldest]
            del self.access_count[oldest]
        
        self.cache[key] = value
        self.access_count[key] += 1

class CompressedCache:
    """冷缓存：压缩存储，节省内存"""
    
    def __init__(self, capacity: int, compression_level: int = 6):
        self.capacity = capacity
        self.cache = {}
        self.compression_level = compression_level
        self.size_tracker = 0
    
    def get(self, key: str) -> Optional[Any]:
        if key in self.cache:
            compressed_data = self.cache[key]
            return pickle.loads(zlib.decompress(compressed_data))
        return None
    
    def set(self, key: str, value: Any) -> None:
        # 序列化并压缩数据
        serialized = pickle.dumps(value)
        compressed = zlib.compress(serialized, self.compression_level)
        
        # 检查容量限制
        if key not in self.cache and self.size_tracker + len(compressed) > self.capacity:
            self._evict_lru()
        
        old_size = len(self.cache.get(key, b''))
        self.cache[key] = compressed
        self.size_tracker += len(compressed) - old_size

class LocalFileCache:
    """本地文件缓存：持久化存储"""
    
    def __init__(self, cache_dir: str, max_size: int):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.max_size = max_size
        self.index_file = self.cache_dir / "cache_index.json"
        self.load_index()
    
    def get(self, key: str) -> Optional[Any]:
        file_path = self.cache_dir / f"{key}.cache"
        if file_path.exists():
            try:
                with open(file_path, 'rb') as f:
                    compressed_data = f.read()
                return pickle.loads(zlib.decompress(compressed_data))
            except Exception:
                # 文件损坏，删除
                file_path.unlink(missing_ok=True)
        return None
    
    def set(self, key: str, value: Any) -> None:
        # 序列化并压缩
        serialized = pickle.dumps(value)
        compressed = zlib.compress(serialized, 6)
        
        file_path = self.cache_dir / f"{key}.cache"
        with open(file_path, 'wb') as f:
            f.write(compressed)
        
        # 更新索引
        self.update_index(key, len(compressed))
        self.cleanup_if_needed()
```

#### 智能缓存策略

```python
class CacheStrategy:
    """缓存策略管理器"""
    
    def __init__(self):
        self.access_patterns = defaultdict(list)
        self.promotion_threshold = 3  # 访问3次后提升到热缓存
        self.demotion_threshold = 300  # 5分钟未访问降级到冷缓存
    
    def should_promote_to_hot(self, key: str) -> bool:
        """判断是否应该提升到热缓存"""
        recent_accesses = len([
            t for t in self.access_patterns[key]
            if time.time() - t < 300  # 5分钟内的访问
        ])
        return recent_accesses >= self.promotion_threshold
    
    def should_demote_to_cold(self, key: str) -> bool:
        """判断是否应该降级到冷缓存"""
        if not self.access_patterns[key]:
            return True
        last_access = max(self.access_patterns[key])
        return time.time() - last_access > self.demotion_threshold
    
    def record_access(self, key: str) -> None:
        """记录访问模式"""
        now = time.time()
        self.access_patterns[key].append(now)
        
        # 只保留最近的访问记录
        cutoff = now - 3600  # 1小时
        self.access_patterns[key] = [
            t for t in self.access_patterns[key] if t > cutoff
        ]
```

### 3. 性能监控数据模型

```python
@dataclass
class PerformanceMetrics:
    """性能指标数据模型"""
    
    # 系统指标
    cpu_usage: float
    memory_usage: float
    network_io: NetworkIO
    
    # 业务指标
    stocks_processed_per_second: float
    anomalies_detected_per_minute: int
    average_processing_latency: float
    
    # 线程池指标
    active_threads: int
    queued_tasks: int
    completed_tasks: int
    
    # 缓存指标
    cache_hit_rate: float
    cache_memory_usage: int
    
    # 数据库指标
    db_connections_active: int
    db_query_latency: float
    db_error_rate: float

@dataclass
class AnomalyResult:
    """异动检测结果"""
    
    stock_code: str
    detection_time: datetime
    anomaly_type: str  # "opening" or "regular"
    volume_ratio: float
    threshold: float
    current_volume: float
    historical_average: float
    confidence_score: float
    additional_metrics: Dict[str, Any]
```

## Error Handling

### 1. 分层错误处理策略

#### 网络层错误处理
- **连接超时**: 指数退避重试，最多5次
- **数据源不可用**: 自动切换备用服务器
- **网络抖动**: 短期缓存机制，减少重复请求

#### 数据层错误处理
- **数据库连接失败**: 连接池自动重连，最多3次
- **查询超时**: 查询优化和索引检查
- **数据不一致**: 自动数据校验和修复

#### 业务层错误处理
- **计算异常**: 降级到简化算法
- **内存不足**: 自动缓存清理和垃圾回收
- **处理超时**: 任务分片和并行处理

### 2. 熔断器模式实现

```python
class CircuitBreaker:
    """熔断器实现"""
    
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def call(self, func, *args, **kwargs):
        """执行函数调用，带熔断保护"""
        if self.state == "OPEN":
            if time.time() - self.last_failure_time > self.timeout:
                self.state = "HALF_OPEN"
            else:
                raise CircuitBreakerOpenException()
        
        try:
            result = func(*args, **kwargs)
            self.on_success()
            return result
        except Exception as e:
            self.on_failure()
            raise e
```

### 3. 降级模式设计

当系统遇到严重问题时，自动进入降级模式：

- **处理频率降级**: 从3秒间隔调整为10秒间隔
- **检测精度降级**: 使用简化的异动检测算法
- **通知降级**: 减少通知频率，合并相似告警
- **缓存降级**: 关闭复杂缓存，使用基础内存缓存

## Testing Strategy

### 1. 单元测试策略

#### 核心组件测试
- **线程池管理器**: 测试动态扩缩容、负载均衡、故障恢复
- **缓存管理器**: 测试LRU策略、压缩算法、分层缓存
- **异动检测器**: 测试各种异动场景、边界条件、算法准确性
- **错误处理器**: 测试各种异常情况、重试机制、降级逻辑

#### 性能测试
- **并发测试**: 模拟4500只股票同时处理
- **压力测试**: 测试系统在高负载下的表现
- **内存测试**: 验证内存使用优化效果
- **延迟测试**: 测试处理延迟和响应时间

### 2. 集成测试策略

#### 端到端测试
- **完整流程测试**: 从数据获取到异动通知的完整链路
- **故障恢复测试**: 模拟各种故障场景，验证恢复能力
- **配置变更测试**: 测试热重载和配置验证功能

#### 兼容性测试
- **数据库兼容性**: 测试不同版本的TimescaleDB
- **Python版本兼容性**: 测试Python 3.8-3.11
- **依赖库兼容性**: 测试关键依赖库的版本兼容性

### 3. 性能基准测试

#### 关键指标基准
- **处理吞吐量**: 目标 > 1500 stocks/second
- **异动检测延迟**: 目标 < 2 seconds
- **内存使用**: 目标 < 2GB
- **CPU使用率**: 目标 < 70%
- **缓存命中率**: 目标 > 85%

#### 对比测试
- **优化前后对比**: 量化优化效果
- **不同配置对比**: 找到最优配置参数
- **算法对比**: 验证算法优化效果

### 4. 监控和告警测试

#### 监控指标验证
- **实时指标准确性**: 验证监控数据的准确性
- **告警触发测试**: 测试各种告警场景
- **仪表板功能**: 验证监控仪表板的功能

#### 日志系统测试
- **日志完整性**: 验证关键操作都有日志记录
- **日志格式**: 验证日志格式的一致性和可读性
- **日志性能**: 验证日志记录不影响系统性能

通过这个全面的测试策略，确保优化后的系统在各种场景下都能稳定、高效地运行。