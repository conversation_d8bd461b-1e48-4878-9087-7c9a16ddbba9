#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模式分析器

检测四种经典背离模式：
1. 底底高（看涨背离）
2. 顶顶低（看跌背离）  
3. 顶顶高（趋势延续）
4. 底底低（趋势延续）

作者: QuantFM Team
创建时间: 2025-08-11
"""

import numpy as np
from dataclasses import dataclass
from typing import List, Tuple, Optional, Dict, Any
import logging
from .extreme_detector import ExtremePoint

logger = logging.getLogger(__name__)


@dataclass
class Pattern:
    """背离模式数据类"""
    type: str                           # 模式类型
    strength: float                     # 模式强度 (0-1)
    confidence: float                   # 置信度 (0-1)
    price_points: List[ExtremePoint]    # 价格极值点
    indicator_points: List[ExtremePoint] # 指标极值点
    is_forming: bool                    # 是否正在形成中
    completion_probability: float       # 完成概率 (0-1)
    
    def __post_init__(self):
        """数据验证"""
        if self.strength < 0 or self.strength > 1:
            raise ValueError(f"强度必须在0-1之间，当前值: {self.strength}")
        if self.confidence < 0 or self.confidence > 1:
            raise ValueError(f"置信度必须在0-1之间，当前值: {self.confidence}")
        if self.completion_probability < 0 or self.completion_probability > 1:
            raise ValueError(f"完成概率必须在0-1之间，当前值: {self.completion_probability}")


class PatternAnalyzer:
    """模式分析器
    
    核心功能:
    1. 检测四种经典背离模式
    2. 计算模式强度和置信度
    3. 处理形成中的模式
    4. 时间衰减和成交量确认
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化分析器
        
        Args:
            config: 配置参数字典
        """
        self.config = config or {}
        
        # 配置参数
        self.min_divergence_strength = self.config.get('min_divergence_strength', 0.3)
        self.confirmation_threshold = self.config.get('confirmation_threshold', 0.6)
        self.time_weight_factor = self.config.get('time_weight_factor', 0.8)
        self.volume_weight_factor = self.config.get('volume_weight_factor', 0.2)
        self.max_time_gap = self.config.get('max_time_gap', 20)  # 最大时间间隔
        
    def analyze_patterns(self, price_extremes: List[ExtremePoint], 
                        indicator_extremes: List[ExtremePoint]) -> List[Pattern]:
        """分析背离模式
        
        Args:
            price_extremes: 价格极值点列表
            indicator_extremes: 指标极值点列表
            
        Returns:
            检测到的模式列表
        """
        if len(price_extremes) < 2 or len(indicator_extremes) < 2:
            logger.debug("极值点数量不足，无法分析模式")
            return []
            
        patterns = []
        
        try:
            # 检测各种背离模式
            patterns.extend(self._detect_bottom_bottom_high(price_extremes, indicator_extremes))
            patterns.extend(self._detect_top_top_low(price_extremes, indicator_extremes))
            patterns.extend(self._detect_top_top_high(price_extremes, indicator_extremes))
            patterns.extend(self._detect_bottom_bottom_low(price_extremes, indicator_extremes))
            
            # 过滤弱模式
            strong_patterns = [p for p in patterns if p.strength >= self.min_divergence_strength]
            
            # 按强度排序
            strong_patterns.sort(key=lambda x: x.strength, reverse=True)
            
            logger.debug(f"检测到 {len(strong_patterns)} 个有效模式")
            return strong_patterns
            
        except Exception as e:
            logger.error(f"模式分析失败: {e}")
            return []
    
    def _detect_bottom_bottom_high(self, price_extremes: List[ExtremePoint], 
                                  indicator_extremes: List[ExtremePoint]) -> List[Pattern]:
        """检测底底高模式（看涨背离）
        
        价格：低点 → 更低点
        指标：低点 → 更高点
        """
        patterns = []
        
        # 获取价格谷值点
        price_troughs = [e for e in price_extremes if e.type == 'trough']
        indicator_troughs = [e for e in indicator_extremes if e.type == 'trough']
        
        if len(price_troughs) < 2 or len(indicator_troughs) < 2:
            return patterns
            
        # 寻找匹配的谷值对
        for i in range(len(price_troughs) - 1):
            for j in range(i + 1, len(price_troughs)):
                price_point1 = price_troughs[i]
                price_point2 = price_troughs[j]
                
                # 检查价格条件：第二个低点更低
                if price_point2.price >= price_point1.price:
                    continue
                    
                # 寻找对应的指标点
                indicator_pair = self._find_matching_indicator_points(
                    price_point1, price_point2, indicator_troughs, 'higher'
                )
                
                if indicator_pair:
                    pattern = self._create_pattern(
                        'bullish_divergence',
                        [price_point1, price_point2],
                        indicator_pair
                    )
                    patterns.append(pattern)
        
        return patterns
    
    def _detect_top_top_low(self, price_extremes: List[ExtremePoint], 
                           indicator_extremes: List[ExtremePoint]) -> List[Pattern]:
        """检测顶顶低模式（看跌背离）
        
        价格：高点 → 更高点
        指标：高点 → 更低点
        """
        patterns = []
        
        # 获取价格峰值点
        price_peaks = [e for e in price_extremes if e.type == 'peak']
        indicator_peaks = [e for e in indicator_extremes if e.type == 'peak']
        
        if len(price_peaks) < 2 or len(indicator_peaks) < 2:
            return patterns
            
        # 寻找匹配的峰值对
        for i in range(len(price_peaks) - 1):
            for j in range(i + 1, len(price_peaks)):
                price_point1 = price_peaks[i]
                price_point2 = price_peaks[j]
                
                # 检查价格条件：第二个高点更高
                if price_point2.price <= price_point1.price:
                    continue
                    
                # 寻找对应的指标点
                indicator_pair = self._find_matching_indicator_points(
                    price_point1, price_point2, indicator_peaks, 'lower'
                )
                
                if indicator_pair:
                    pattern = self._create_pattern(
                        'bearish_divergence',
                        [price_point1, price_point2],
                        indicator_pair
                    )
                    patterns.append(pattern)
        
        return patterns
    
    def _detect_top_top_high(self, price_extremes: List[ExtremePoint], 
                            indicator_extremes: List[ExtremePoint]) -> List[Pattern]:
        """检测顶顶高模式（趋势延续）
        
        价格：高点 → 更高点
        指标：高点 → 更高点
        """
        patterns = []
        
        # 获取价格峰值点
        price_peaks = [e for e in price_extremes if e.type == 'peak']
        indicator_peaks = [e for e in indicator_extremes if e.type == 'peak']
        
        if len(price_peaks) < 2 or len(indicator_peaks) < 2:
            return patterns
            
        # 寻找匹配的峰值对
        for i in range(len(price_peaks) - 1):
            for j in range(i + 1, len(price_peaks)):
                price_point1 = price_peaks[i]
                price_point2 = price_peaks[j]
                
                # 检查价格条件：第二个高点更高
                if price_point2.price <= price_point1.price:
                    continue
                    
                # 寻找对应的指标点
                indicator_pair = self._find_matching_indicator_points(
                    price_point1, price_point2, indicator_peaks, 'higher'
                )
                
                if indicator_pair:
                    pattern = self._create_pattern(
                        'trend_continuation_bullish',
                        [price_point1, price_point2],
                        indicator_pair
                    )
                    patterns.append(pattern)
        
        return patterns
    
    def _detect_bottom_bottom_low(self, price_extremes: List[ExtremePoint], 
                                 indicator_extremes: List[ExtremePoint]) -> List[Pattern]:
        """检测底底低模式（趋势延续）
        
        价格：低点 → 更低点
        指标：低点 → 更低点
        """
        patterns = []
        
        # 获取价格谷值点
        price_troughs = [e for e in price_extremes if e.type == 'trough']
        indicator_troughs = [e for e in indicator_extremes if e.type == 'trough']
        
        if len(price_troughs) < 2 or len(indicator_troughs) < 2:
            return patterns
            
        # 寻找匹配的谷值对
        for i in range(len(price_troughs) - 1):
            for j in range(i + 1, len(price_troughs)):
                price_point1 = price_troughs[i]
                price_point2 = price_troughs[j]
                
                # 检查价格条件：第二个低点更低
                if price_point2.price >= price_point1.price:
                    continue
                    
                # 寻找对应的指标点
                indicator_pair = self._find_matching_indicator_points(
                    price_point1, price_point2, indicator_troughs, 'lower'
                )
                
                if indicator_pair:
                    pattern = self._create_pattern(
                        'trend_continuation_bearish',
                        [price_point1, price_point2],
                        indicator_pair
                    )
                    patterns.append(pattern)
        
        return patterns
    
    def _find_matching_indicator_points(self, price_point1: ExtremePoint, price_point2: ExtremePoint,
                                      indicator_points: List[ExtremePoint], 
                                      direction: str) -> Optional[List[ExtremePoint]]:
        """寻找匹配的指标点对
        
        Args:
            price_point1: 第一个价格点
            price_point2: 第二个价格点
            indicator_points: 指标极值点列表
            direction: 'higher' 或 'lower'
            
        Returns:
            匹配的指标点对，如果没有找到则返回None
        """
        # 为每个价格点寻找最近的指标点
        indicator1 = self._find_closest_indicator_point(price_point1, indicator_points)
        indicator2 = self._find_closest_indicator_point(price_point2, indicator_points)
        
        if not indicator1 or not indicator2:
            return None
            
        # 检查时间间隔
        if abs(indicator1.index - price_point1.index) > self.max_time_gap:
            return None
        if abs(indicator2.index - price_point2.index) > self.max_time_gap:
            return None
            
        # 检查指标方向
        if direction == 'higher':
            if indicator2.price <= indicator1.price:
                return None
        elif direction == 'lower':
            if indicator2.price >= indicator1.price:
                return None
        else:
            return None
            
        return [indicator1, indicator2]
    
    def _find_closest_indicator_point(self, price_point: ExtremePoint, 
                                    indicator_points: List[ExtremePoint]) -> Optional[ExtremePoint]:
        """寻找最接近的指标点"""
        if not indicator_points:
            return None
            
        # 寻找时间上最接近的指标点
        closest_point = None
        min_distance = float('inf')
        
        for indicator_point in indicator_points:
            distance = abs(indicator_point.index - price_point.index)
            if distance < min_distance and distance <= self.max_time_gap:
                min_distance = distance
                closest_point = indicator_point
                
        return closest_point
    
    def _create_pattern(self, pattern_type: str, price_points: List[ExtremePoint], 
                       indicator_points: List[ExtremePoint]) -> Pattern:
        """创建模式对象"""
        # 计算模式强度
        strength = self._calculate_pattern_strength(price_points, indicator_points, pattern_type)
        
        # 计算置信度
        confidence = self._calculate_pattern_confidence(price_points, indicator_points)
        
        # 检查是否为形成中的模式
        is_forming = any(not point.confirmed for point in price_points + indicator_points)
        
        # 计算完成概率
        completion_prob = self._calculate_pattern_completion_probability(price_points, indicator_points)
        
        return Pattern(
            type=pattern_type,
            strength=strength,
            confidence=confidence,
            price_points=price_points,
            indicator_points=indicator_points,
            is_forming=is_forming,
            completion_probability=completion_prob
        )
    
    def _calculate_pattern_strength(self, price_points: List[ExtremePoint], 
                                  indicator_points: List[ExtremePoint], pattern_type: str) -> float:
        """计算模式强度"""
        try:
            # 价格变化幅度
            price_change = abs(price_points[1].price - price_points[0].price)
            price_base = (price_points[0].price + price_points[1].price) / 2
            price_strength = price_change / price_base if price_base > 0 else 0
            
            # 指标变化幅度
            indicator_change = abs(indicator_points[1].price - indicator_points[0].price)
            indicator_base = (abs(indicator_points[0].price) + abs(indicator_points[1].price)) / 2
            indicator_strength = indicator_change / indicator_base if indicator_base > 0 else 0
            
            # 背离强度（价格和指标方向的差异）
            if 'divergence' in pattern_type:
                # 背离模式：价格和指标方向相反
                divergence_strength = min(price_strength, indicator_strength)
            else:
                # 趋势延续：价格和指标方向一致
                divergence_strength = (price_strength + indicator_strength) / 2
            
            # 时间因子
            time_factor = self._calculate_time_decay_factor(price_points)
            
            # 综合强度
            total_strength = divergence_strength * time_factor
            
            return np.clip(total_strength, 0.0, 1.0)
            
        except Exception as e:
            logger.warning(f"模式强度计算失败: {e}")
            return 0.5
    
    def _calculate_pattern_confidence(self, price_points: List[ExtremePoint], 
                                    indicator_points: List[ExtremePoint]) -> float:
        """计算模式置信度"""
        try:
            # 极值点强度
            avg_price_strength = np.mean([p.strength for p in price_points])
            avg_indicator_strength = np.mean([p.strength for p in indicator_points])
            
            # 时间同步性
            time_sync = self._calculate_time_synchronization(price_points, indicator_points)
            
            # 确认状态
            confirmation_factor = np.mean([p.confirmed for p in price_points + indicator_points])
            
            # 综合置信度
            confidence = (avg_price_strength * 0.3 + avg_indicator_strength * 0.3 + 
                         time_sync * 0.2 + confirmation_factor * 0.2)
            
            return np.clip(confidence, 0.0, 1.0)
            
        except Exception as e:
            logger.warning(f"置信度计算失败: {e}")
            return 0.5
    
    def _calculate_pattern_completion_probability(self, price_points: List[ExtremePoint], 
                                                indicator_points: List[ExtremePoint]) -> float:
        """计算模式完成概率"""
        try:
            # 所有点的平均完成概率
            all_points = price_points + indicator_points
            avg_completion = np.mean([p.completion_prob for p in all_points])
            
            # 确认点的比例
            confirmed_ratio = sum(p.confirmed for p in all_points) / len(all_points)
            
            # 综合完成概率
            completion_prob = (avg_completion * 0.7 + confirmed_ratio * 0.3)
            
            return np.clip(completion_prob, 0.0, 1.0)
            
        except Exception as e:
            logger.warning(f"完成概率计算失败: {e}")
            return 0.5
    
    def _calculate_time_decay_factor(self, price_points: List[ExtremePoint]) -> float:
        """计算时间衰减因子"""
        try:
            # 基于最新点的时间位置计算衰减
            latest_index = max(p.index for p in price_points)
            
            # 假设总长度为最新索引+一些缓冲
            estimated_total_length = latest_index + 10
            
            # 时间衰减：越新的模式权重越高
            time_factor = 1.0 - (estimated_total_length - latest_index) / estimated_total_length
            
            # 应用衰减权重
            decay_factor = time_factor ** self.time_weight_factor
            
            return np.clip(decay_factor, 0.1, 1.0)
            
        except Exception as e:
            logger.warning(f"时间衰减计算失败: {e}")
            return 0.8
    
    def _calculate_time_synchronization(self, price_points: List[ExtremePoint], 
                                      indicator_points: List[ExtremePoint]) -> float:
        """计算时间同步性"""
        try:
            # 计算价格点和指标点的时间差异
            time_diffs = []
            for i in range(len(price_points)):
                if i < len(indicator_points):
                    time_diff = abs(price_points[i].index - indicator_points[i].index)
                    time_diffs.append(time_diff)
            
            if not time_diffs:
                return 0.5
                
            # 平均时间差异
            avg_time_diff = np.mean(time_diffs)
            
            # 转换为同步性评分（差异越小，同步性越高）
            max_acceptable_diff = self.max_time_gap
            sync_score = 1.0 - min(avg_time_diff / max_acceptable_diff, 1.0)
            
            return np.clip(sync_score, 0.0, 1.0)
            
        except Exception as e:
            logger.warning(f"时间同步性计算失败: {e}")
            return 0.5
