#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建策略数据库表脚本

执行SQL脚本来创建策略相关的数据库表。

作者: QuantFM Team
创建时间: 2025-08-08
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import get_db_manager
from utils.logger import get_logger


def create_strategy_tables():
    """创建策略数据库表"""
    logger = get_logger("CreateStrategyTables")
    
    try:
        # 读取SQL脚本
        sql_file_path = "sql/create_strategy_tables.sql"
        
        if not os.path.exists(sql_file_path):
            logger.error(f"SQL文件不存在: {sql_file_path}")
            return False
        
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 获取数据库管理器
        db_manager = get_db_manager()
        
        # 分割SQL语句（按分号分割）
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        logger.info(f"开始执行 {len(sql_statements)} 个SQL语句")
        
        # 逐个执行SQL语句
        for i, sql_statement in enumerate(sql_statements, 1):
            try:
                if sql_statement.strip():
                    logger.debug(f"执行第 {i} 个SQL语句...")
                    db_manager.execute_query(sql_statement)
                    
            except Exception as e:
                # 某些语句可能因为表已存在等原因失败，这是正常的
                if "already exists" in str(e).lower() or "duplicate" in str(e).lower():
                    logger.debug(f"第 {i} 个SQL语句跳过（对象已存在）: {e}")
                else:
                    logger.warning(f"第 {i} 个SQL语句执行失败: {e}")
        
        logger.info("策略数据库表创建完成")
        
        # 验证表是否创建成功
        verify_tables(db_manager, logger)
        
        return True
        
    except Exception as e:
        logger.error(f"创建策略数据库表失败: {e}")
        return False


def verify_tables(db_manager, logger):
    """验证表是否创建成功"""
    try:
        # 检查主要表是否存在
        tables_to_check = [
            'dual_channel_fibonacci_signals',
            'strategy_signals',
            'strategy_configs',
            'strategy_execution_logs',
            'strategy_performance_stats'
        ]
        
        for table_name in tables_to_check:
            sql = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = %s
                );
            """
            
            result = db_manager.fetch_one(sql, (table_name,))
            
            if result and result[0]:
                logger.info(f"✅ 表 {table_name} 创建成功")
            else:
                logger.error(f"❌ 表 {table_name} 创建失败")
        
        # 检查视图是否存在
        views_to_check = [
            'v_strategy_signals_summary',
            'v_latest_strategy_signals'
        ]
        
        for view_name in views_to_check:
            sql = """
                SELECT EXISTS (
                    SELECT FROM information_schema.views 
                    WHERE table_schema = 'public' 
                    AND table_name = %s
                );
            """
            
            result = db_manager.fetch_one(sql, (view_name,))
            
            if result and result[0]:
                logger.info(f"✅ 视图 {view_name} 创建成功")
            else:
                logger.error(f"❌ 视图 {view_name} 创建失败")
        
        # 检查策略配置是否插入成功
        sql = "SELECT COUNT(*) FROM strategy_configs WHERE strategy_name = '双通道斐波那契突破'"
        result = db_manager.fetch_one(sql)
        
        if result and result[0] > 0:
            logger.info("✅ 默认策略配置插入成功")
        else:
            logger.warning("⚠️ 默认策略配置未找到")
            
    except Exception as e:
        logger.error(f"验证表创建失败: {e}")


if __name__ == "__main__":
    success = create_strategy_tables()
    if success:
        print("✅ 策略数据库表创建成功！")
    else:
        print("❌ 策略数据库表创建失败！")
        sys.exit(1)
