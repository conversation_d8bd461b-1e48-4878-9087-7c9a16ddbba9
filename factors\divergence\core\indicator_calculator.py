#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指标计算器

封装talib函数，提供缓存机制以提升性能。
支持MACD、RSI、KDJ、CCI、OBV等技术指标的计算。

作者: QuantFM Team
创建时间: 2025-08-11
"""

import numpy as np
import hashlib
from typing import Dict, Any, Optional, Tuple, Union
import logging
from functools import lru_cache

# 导入talib函数
try:
    from factors.technical_indicators import (
        calculate_macd, calculate_rsi, calculate_kdj, 
        calculate_cci, calculate_obv
    )
    TALIB_AVAILABLE = True
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("无法导入talib函数，将使用简化实现")
    TALIB_AVAILABLE = False

logger = logging.getLogger(__name__)

# 模块级缓存
_indicator_cache = {}
_cache_max_size = 1000


class IndicatorCalculator:
    """指标计算器
    
    核心功能:
    1. 封装talib函数调用
    2. 提供LRU缓存机制
    3. 标准化输出格式
    4. 处理NaN值
    5. 支持增量计算
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化计算器
        
        Args:
            config: 配置参数字典
        """
        self.config = config or {}
        self.cache_enabled = self.config.get('cache_enabled', True)
        
        # 默认参数
        self.default_params = {
            'macd': {
                'fast_period': 12,
                'slow_period': 26,
                'signal_period': 9
            },
            'rsi': {
                'period': 14
            },
            'kdj': {
                'k_period': 9,
                'd_period': 3,
                'j_period': 3
            },
            'cci': {
                'period': 14
            },
            'obv': {}
        }
    
    def calculate_all_indicators(self, high: np.ndarray, low: np.ndarray, 
                               close: np.ndarray, volume: np.ndarray) -> Dict[str, Any]:
        """计算所有指标
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            volume: 成交量序列
            
        Returns:
            包含所有指标结果的字典
        """
        results = {}
        
        try:
            # 计算各个指标
            results['macd'] = self.calculate_macd(high, low, close)
            results['rsi'] = self.calculate_rsi(close)
            results['kdj'] = self.calculate_kdj(high, low, close)
            results['cci'] = self.calculate_cci(high, low, close)
            results['obv'] = self.calculate_obv(close, volume)
            
            logger.debug("所有指标计算完成")
            return results
            
        except Exception as e:
            logger.error(f"指标计算失败: {e}")
            return {}
    
    def calculate_macd(self, high: np.ndarray, low: np.ndarray, close: np.ndarray,
                      fast_period: Optional[int] = None, slow_period: Optional[int] = None,
                      signal_period: Optional[int] = None) -> Dict[str, np.ndarray]:
        """计算MACD指标
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            fast_period: 快线周期
            slow_period: 慢线周期
            signal_period: 信号线周期
            
        Returns:
            包含DIF、DEA、MACD的字典
        """
        # 使用默认参数
        params = self.default_params['macd'].copy()
        if fast_period is not None:
            params['fast_period'] = fast_period
        if slow_period is not None:
            params['slow_period'] = slow_period
        if signal_period is not None:
            params['signal_period'] = signal_period
        
        # 检查缓存
        cache_key = self._generate_cache_key('macd', params, close)
        if self.cache_enabled and cache_key in _indicator_cache:
            return _indicator_cache[cache_key]
        
        try:
            if TALIB_AVAILABLE:
                # 使用talib函数
                result = calculate_macd(
                    close, 
                    fast_period=params['fast_period'],
                    slow_period=params['slow_period'], 
                    signal_period=params['signal_period']
                )
            else:
                # 简化实现
                result = self._calculate_macd_simple(close, params)
            
            # 处理NaN值
            result = self._handle_nan_values(result)
            
            # 缓存结果
            if self.cache_enabled:
                self._cache_result(cache_key, result)
            
            return result
            
        except Exception as e:
            logger.error(f"MACD计算失败: {e}")
            return self._get_empty_macd_result(len(close))
    
    def calculate_rsi(self, close: np.ndarray, period: Optional[int] = None) -> Dict[str, np.ndarray]:
        """计算RSI指标
        
        Args:
            close: 收盘价序列
            period: 计算周期
            
        Returns:
            包含RSI值的字典
        """
        # 使用默认参数
        params = self.default_params['rsi'].copy()
        if period is not None:
            params['period'] = period
        
        # 检查缓存
        cache_key = self._generate_cache_key('rsi', params, close)
        if self.cache_enabled and cache_key in _indicator_cache:
            return _indicator_cache[cache_key]
        
        try:
            if TALIB_AVAILABLE:
                # 使用talib函数
                rsi_values = calculate_rsi(close, period=params['period'])
                result = {'rsi': rsi_values}
            else:
                # 简化实现
                result = self._calculate_rsi_simple(close, params['period'])
            
            # 处理NaN值
            result = self._handle_nan_values(result)
            
            # 缓存结果
            if self.cache_enabled:
                self._cache_result(cache_key, result)
            
            return result
            
        except Exception as e:
            logger.error(f"RSI计算失败: {e}")
            return {'rsi': np.full(len(close), 50.0)}
    
    def calculate_kdj(self, high: np.ndarray, low: np.ndarray, close: np.ndarray,
                     k_period: Optional[int] = None, d_period: Optional[int] = None,
                     j_period: Optional[int] = None) -> Dict[str, np.ndarray]:
        """计算KDJ指标
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            k_period: K线周期
            d_period: D线周期
            j_period: J线周期
            
        Returns:
            包含K、D、J值的字典
        """
        # 使用默认参数
        params = self.default_params['kdj'].copy()
        if k_period is not None:
            params['k_period'] = k_period
        if d_period is not None:
            params['d_period'] = d_period
        if j_period is not None:
            params['j_period'] = j_period
        
        # 检查缓存
        cache_key = self._generate_cache_key('kdj', params, close)
        if self.cache_enabled and cache_key in _indicator_cache:
            return _indicator_cache[cache_key]
        
        try:
            if TALIB_AVAILABLE:
                # 使用talib函数
                result = calculate_kdj(
                    high, low, close,
                    k_period=params['k_period'],
                    d_period=params['d_period']
                )
                # 计算J线
                if 'k' in result and 'd' in result:
                    result['j'] = 3 * result['k'] - 2 * result['d']
            else:
                # 简化实现
                result = self._calculate_kdj_simple(high, low, close, params)
            
            # 处理NaN值
            result = self._handle_nan_values(result)
            
            # 缓存结果
            if self.cache_enabled:
                self._cache_result(cache_key, result)
            
            return result
            
        except Exception as e:
            logger.error(f"KDJ计算失败: {e}")
            return self._get_empty_kdj_result(len(close))
    
    def calculate_cci(self, high: np.ndarray, low: np.ndarray, close: np.ndarray,
                     period: Optional[int] = None) -> Dict[str, np.ndarray]:
        """计算CCI指标
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            period: 计算周期
            
        Returns:
            包含CCI值的字典
        """
        # 使用默认参数
        params = self.default_params['cci'].copy()
        if period is not None:
            params['period'] = period
        
        # 检查缓存
        cache_key = self._generate_cache_key('cci', params, close)
        if self.cache_enabled and cache_key in _indicator_cache:
            return _indicator_cache[cache_key]
        
        try:
            if TALIB_AVAILABLE:
                # 使用talib函数
                cci_values = calculate_cci(high, low, close, period=params['period'])
                result = {'cci': cci_values}
            else:
                # 简化实现
                result = self._calculate_cci_simple(high, low, close, params['period'])
            
            # 处理NaN值
            result = self._handle_nan_values(result)
            
            # 缓存结果
            if self.cache_enabled:
                self._cache_result(cache_key, result)
            
            return result
            
        except Exception as e:
            logger.error(f"CCI计算失败: {e}")
            return {'cci': np.zeros(len(close))}
    
    def calculate_obv(self, close: np.ndarray, volume: np.ndarray) -> Dict[str, np.ndarray]:
        """计算OBV指标
        
        Args:
            close: 收盘价序列
            volume: 成交量序列
            
        Returns:
            包含OBV值的字典
        """
        # 检查缓存
        cache_key = self._generate_cache_key('obv', {}, close, volume)
        if self.cache_enabled and cache_key in _indicator_cache:
            return _indicator_cache[cache_key]
        
        try:
            if TALIB_AVAILABLE:
                # 使用talib函数
                obv_values = calculate_obv(close, volume)
                result = {'obv': obv_values}
            else:
                # 简化实现
                result = self._calculate_obv_simple(close, volume)
            
            # 处理NaN值
            result = self._handle_nan_values(result)
            
            # 缓存结果
            if self.cache_enabled:
                self._cache_result(cache_key, result)
            
            return result
            
        except Exception as e:
            logger.error(f"OBV计算失败: {e}")
            return {'obv': np.zeros(len(close))}
    
    def _generate_cache_key(self, indicator_name: str, params: Dict[str, Any], 
                           *data_arrays: np.ndarray) -> str:
        """生成缓存键"""
        try:
            # 参数哈希
            params_str = str(sorted(params.items()))
            params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
            
            # 数据哈希
            data_hashes = []
            for data in data_arrays:
                if len(data) > 0:
                    # 使用最后几个值和长度生成哈希
                    sample_data = np.concatenate([data[-min(5, len(data)):], [len(data)]])
                    data_hash = hashlib.md5(sample_data.tobytes()).hexdigest()[:8]
                    data_hashes.append(data_hash)
            
            data_hash = '_'.join(data_hashes)
            
            return f"{indicator_name}_{params_hash}_{data_hash}"
            
        except Exception as e:
            logger.warning(f"缓存键生成失败: {e}")
            return f"{indicator_name}_{np.random.randint(0, 1000000)}"
    
    def _cache_result(self, cache_key: str, result: Dict[str, np.ndarray]):
        """缓存结果"""
        global _indicator_cache
        
        # 检查缓存大小
        if len(_indicator_cache) >= _cache_max_size:
            # 简单的LRU：删除一半的缓存
            keys_to_remove = list(_indicator_cache.keys())[:_cache_max_size // 2]
            for key in keys_to_remove:
                del _indicator_cache[key]
        
        _indicator_cache[cache_key] = result.copy()
    
    def _handle_nan_values(self, result: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """处理NaN值"""
        cleaned_result = {}
        
        for key, values in result.items():
            if isinstance(values, np.ndarray):
                # 用前向填充处理NaN
                cleaned_values = values.copy()
                mask = np.isnan(cleaned_values)
                
                if np.any(mask):
                    # 前向填充
                    for i in range(1, len(cleaned_values)):
                        if mask[i] and not mask[i-1]:
                            cleaned_values[i] = cleaned_values[i-1]
                    
                    # 如果开头有NaN，用第一个有效值填充
                    first_valid_idx = np.where(~mask)[0]
                    if len(first_valid_idx) > 0:
                        first_valid_value = cleaned_values[first_valid_idx[0]]
                        cleaned_values[:first_valid_idx[0]] = first_valid_value
                
                cleaned_result[key] = cleaned_values
            else:
                cleaned_result[key] = values
        
        return cleaned_result

    # 简化实现方法（当talib不可用时）
    def _calculate_macd_simple(self, close: np.ndarray, params: Dict[str, int]) -> Dict[str, np.ndarray]:
        """MACD简化实现"""
        fast_period = params['fast_period']
        slow_period = params['slow_period']
        signal_period = params['signal_period']

        # 计算EMA
        ema_fast = self._calculate_ema(close, fast_period)
        ema_slow = self._calculate_ema(close, slow_period)

        # DIF线
        dif = ema_fast - ema_slow

        # DEA线（DIF的EMA）
        dea = self._calculate_ema(dif, signal_period)

        # MACD柱状图
        macd = 2 * (dif - dea)

        return {'dif': dif, 'dea': dea, 'macd': macd}

    def _calculate_rsi_simple(self, close: np.ndarray, period: int) -> Dict[str, np.ndarray]:
        """RSI简化实现"""
        delta = np.diff(close)
        gain = np.where(delta > 0, delta, 0)
        loss = np.where(delta < 0, -delta, 0)

        # 计算平均收益和损失
        avg_gain = np.zeros(len(close))
        avg_loss = np.zeros(len(close))

        if len(gain) >= period:
            avg_gain[period] = np.mean(gain[:period])
            avg_loss[period] = np.mean(loss[:period])

            for i in range(period + 1, len(close)):
                avg_gain[i] = (avg_gain[i-1] * (period - 1) + gain[i-1]) / period
                avg_loss[i] = (avg_loss[i-1] * (period - 1) + loss[i-1]) / period

        # 计算RSI
        rs = np.divide(avg_gain, avg_loss, out=np.zeros_like(avg_gain), where=avg_loss!=0)
        rsi = 100 - (100 / (1 + rs))

        return {'rsi': rsi}

    def _calculate_kdj_simple(self, high: np.ndarray, low: np.ndarray,
                             close: np.ndarray, params: Dict[str, int]) -> Dict[str, np.ndarray]:
        """KDJ简化实现"""
        k_period = params['k_period']
        d_period = params['d_period']

        # 计算RSV
        rsv = np.zeros(len(close))
        for i in range(k_period - 1, len(close)):
            highest = np.max(high[i - k_period + 1:i + 1])
            lowest = np.min(low[i - k_period + 1:i + 1])
            if highest != lowest:
                rsv[i] = (close[i] - lowest) / (highest - lowest) * 100
            else:
                rsv[i] = 50

        # 计算K值
        k = np.zeros(len(close))
        k[0] = 50  # 初始值
        for i in range(1, len(close)):
            k[i] = (2 * k[i-1] + rsv[i]) / 3

        # 计算D值
        d = np.zeros(len(close))
        d[0] = 50  # 初始值
        for i in range(1, len(close)):
            d[i] = (2 * d[i-1] + k[i]) / 3

        # 计算J值
        j = 3 * k - 2 * d

        return {'k': k, 'd': d, 'j': j}

    def _calculate_cci_simple(self, high: np.ndarray, low: np.ndarray,
                             close: np.ndarray, period: int) -> Dict[str, np.ndarray]:
        """CCI简化实现"""
        # 典型价格
        tp = (high + low + close) / 3

        # 移动平均
        sma = np.zeros(len(close))
        for i in range(period - 1, len(close)):
            sma[i] = np.mean(tp[i - period + 1:i + 1])

        # 平均绝对偏差
        mad = np.zeros(len(close))
        for i in range(period - 1, len(close)):
            mad[i] = np.mean(np.abs(tp[i - period + 1:i + 1] - sma[i]))

        # CCI计算
        cci = np.divide((tp - sma), (0.015 * mad), out=np.zeros_like(tp), where=mad!=0)

        return {'cci': cci}

    def _calculate_obv_simple(self, close: np.ndarray, volume: np.ndarray) -> Dict[str, np.ndarray]:
        """OBV简化实现"""
        obv = np.zeros(len(close))
        obv[0] = volume[0]

        for i in range(1, len(close)):
            if close[i] > close[i-1]:
                obv[i] = obv[i-1] + volume[i]
            elif close[i] < close[i-1]:
                obv[i] = obv[i-1] - volume[i]
            else:
                obv[i] = obv[i-1]

        return {'obv': obv}

    def _calculate_ema(self, data: np.ndarray, period: int) -> np.ndarray:
        """计算指数移动平均"""
        alpha = 2.0 / (period + 1)
        ema = np.zeros(len(data))
        ema[0] = data[0]

        for i in range(1, len(data)):
            ema[i] = alpha * data[i] + (1 - alpha) * ema[i-1]

        return ema

    def _get_empty_macd_result(self, length: int) -> Dict[str, np.ndarray]:
        """获取空的MACD结果"""
        return {
            'dif': np.zeros(length),
            'dea': np.zeros(length),
            'macd': np.zeros(length)
        }

    def _get_empty_kdj_result(self, length: int) -> Dict[str, np.ndarray]:
        """获取空的KDJ结果"""
        return {
            'k': np.full(length, 50.0),
            'd': np.full(length, 50.0),
            'j': np.full(length, 50.0)
        }
