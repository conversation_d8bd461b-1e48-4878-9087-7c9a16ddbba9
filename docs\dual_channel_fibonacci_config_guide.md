# 双通道斐波那契策略配置指南

## 概述

本指南详细介绍了双通道斐波那契突破策略的配置方法、参数说明和最佳实践。

## 配置文件结构

### 主配置文件 (config/main.toml)

```toml
[dual_channel_fibonacci]
# 通道参数
ema_short_1 = 144      # 通道1短期EMA周期
ema_long_1 = 169       # 通道1长期EMA周期
ema_short_2 = 576      # 通道2短期EMA周期
ema_long_2 = 676       # 通道2长期EMA周期

# 时间窗参数
min_days = 2           # 最小突破天数
max_days = 62          # 最大突破天数
pre_check_days = 120   # 历史结构验证天数
pivot_window = 10      # 关键点提取窗口

# 成交量参数
volume_window = 20     # 成交量均值计算窗口
volume_ratio = 1.2     # 突破时成交量比率

# 容错参数
max_intrusion_days = 1 # 最大允许影线穿透次数

[strategies]
dual_channel_fibonacci_enabled = true  # 策略启用开关
```

## 参数详细说明

### 通道参数

#### ema_short_1 (通道1短期EMA)
- **默认值**: 144
- **取值范围**: 50-200
- **作用**: 计算通道1的上轨，用于捕捉短期趋势变化
- **调优建议**: 
  - 增大值：信号更稳定，但响应较慢
  - 减小值：信号更敏感，但可能增加假信号

#### ema_long_1 (通道1长期EMA)
- **默认值**: 169
- **取值范围**: ema_short_1 + 10 到 300
- **约束**: 必须大于ema_short_1
- **作用**: 计算通道1的下轨
- **调优建议**: 与ema_short_1的差值决定通道1的宽度

#### ema_short_2 (通道2短期EMA)
- **默认值**: 576
- **取值范围**: ema_long_1 + 50 到 800
- **约束**: 必须大于ema_long_1
- **作用**: 计算通道2的上轨，用于确认长期趋势
- **调优建议**: 应该明显大于通道1的参数

#### ema_long_2 (通道2长期EMA)
- **默认值**: 676
- **取值范围**: ema_short_2 + 10 到 1000
- **约束**: 必须大于ema_short_2
- **作用**: 计算通道2的下轨
- **调优建议**: 与ema_short_2的差值决定通道2的宽度

### 时间窗参数

#### min_days (最小突破天数)
- **默认值**: 2
- **取值范围**: 1-10
- **作用**: 通道1突破到通道2突破的最小时间间隔
- **调优建议**: 
  - 过小：可能捕捉到噪音信号
  - 过大：可能错过快速突破的机会

#### max_days (最大突破天数)
- **默认值**: 62
- **取值范围**: min_days + 10 到 120
- **作用**: 通道1突破到通道2突破的最大时间间隔
- **调优建议**: 
  - 根据市场特性调整，牛市可适当增大
  - 熊市或震荡市可适当减小

#### pre_check_days (历史结构验证天数)
- **默认值**: 120
- **取值范围**: 60-200
- **作用**: 验证通道压制结构的历史回看天数
- **调优建议**: 
  - 增大：结构验证更严格，信号质量更高
  - 减小：信号数量增加，但质量可能下降

#### pivot_window (关键点提取窗口)
- **默认值**: 10
- **取值范围**: 5-20
- **作用**: 寻找起始低点和目标高点的搜索窗口
- **调优建议**: 根据股票的波动特性调整

### 成交量参数

#### volume_window (成交量均值计算窗口)
- **默认值**: 20
- **取值范围**: 10-50
- **作用**: 计算成交量移动平均的窗口大小
- **调优建议**: 
  - 增大：成交量基准更稳定
  - 减小：对成交量变化更敏感

#### volume_ratio (突破时成交量比率)
- **默认值**: 1.2
- **取值范围**: 1.0-3.0
- **作用**: 突破时成交量相对于均值的最小倍数
- **调优建议**: 
  - 增大：要求更强的成交量确认
  - 减小：降低成交量要求，增加信号数量

### 容错参数

#### max_intrusion_days (最大影线穿透次数)
- **默认值**: 1
- **取值范围**: 0-5
- **作用**: 允许的最大影线式跌破次数
- **调优建议**: 
  - 0：要求完美的连续性
  - 1-2：允许少量技术性回调
  - 3+：过于宽松，可能降低信号质量

## 配置模板

### 保守型配置
适用于风险偏好较低的投资者：

```toml
[dual_channel_fibonacci]
ema_short_1 = 120
ema_long_1 = 150
ema_short_2 = 500
ema_long_2 = 600
min_days = 3
max_days = 45
volume_ratio = 1.5
max_intrusion_days = 0
```

### 激进型配置
适用于追求更多交易机会的投资者：

```toml
[dual_channel_fibonacci]
ema_short_1 = 100
ema_long_1 = 120
ema_short_2 = 400
ema_long_2 = 480
min_days = 1
max_days = 80
volume_ratio = 1.1
max_intrusion_days = 2
```

### 大盘股配置
适用于大盘蓝筹股：

```toml
[dual_channel_fibonacci]
ema_short_1 = 200
ema_long_1 = 250
ema_short_2 = 800
ema_long_2 = 1000
min_days = 5
max_days = 90
volume_ratio = 1.3
max_intrusion_days = 1
```

### 小盘股配置
适用于中小盘成长股：

```toml
[dual_channel_fibonacci]
ema_short_1 = 89
ema_long_1 = 144
ema_short_2 = 377
ema_long_2 = 610
min_days = 1
max_days = 40
volume_ratio = 1.8
max_intrusion_days = 2
```

## 配置验证

### 自动验证
策略会自动验证配置的合理性：

```python
from strategies.trending.dual_channel_fibonacci import StrategyConfig

config = StrategyConfig.from_config_file()
if not config.validate():
    errors = config.get_validation_errors()
    print("配置错误:", errors)
```

### 验证规则

1. **EMA周期递增**: ema_short_1 < ema_long_1 < ema_short_2 < ema_long_2
2. **时间窗合理**: 0 < min_days <= max_days
3. **成交量参数**: volume_window > 0, volume_ratio > 1.0
4. **容错参数**: max_intrusion_days >= 0

## 参数调优方法

### 1. 历史回测法

```python
def optimize_parameters():
    # 定义参数范围
    ema_short_1_range = [100, 120, 144, 169]
    volume_ratio_range = [1.1, 1.2, 1.3, 1.5]
    
    best_config = None
    best_score = 0
    
    for ema1 in ema_short_1_range:
        for vol_ratio in volume_ratio_range:
            config = StrategyConfig(
                ema_short_1=ema1,
                volume_ratio=vol_ratio
            )
            
            score = backtest_config(config)
            if score > best_score:
                best_score = score
                best_config = config
    
    return best_config
```

### 2. 网格搜索法

```python
from itertools import product

def grid_search_optimization():
    param_grid = {
        'ema_short_1': [120, 144, 169],
        'ema_long_1': [150, 169, 200],
        'volume_ratio': [1.1, 1.2, 1.3]
    }
    
    best_params = None
    best_performance = 0
    
    for params in product(*param_grid.values()):
        config_dict = dict(zip(param_grid.keys(), params))
        config = StrategyConfig(**config_dict)
        
        if config.validate():
            performance = evaluate_config(config)
            if performance > best_performance:
                best_performance = performance
                best_params = config_dict
    
    return best_params
```

### 3. 市场环境适应

```python
def adapt_to_market_condition(market_condition):
    """根据市场环境调整参数"""
    base_config = StrategyConfig()
    
    if market_condition == "bull":
        # 牛市：放宽条件，捕捉更多机会
        base_config.volume_ratio = 1.1
        base_config.max_days = 80
        base_config.max_intrusion_days = 2
        
    elif market_condition == "bear":
        # 熊市：严格条件，提高信号质量
        base_config.volume_ratio = 1.5
        base_config.max_days = 40
        base_config.max_intrusion_days = 0
        
    elif market_condition == "sideways":
        # 震荡市：平衡配置
        base_config.volume_ratio = 1.2
        base_config.max_days = 62
        base_config.max_intrusion_days = 1
    
    return base_config
```

## 配置管理最佳实践

### 1. 版本控制
```toml
[dual_channel_fibonacci]
# 配置版本: v1.2
# 更新日期: 2025-01-06
# 更新原因: 优化成交量参数
ema_short_1 = 144
# ... 其他参数
```

### 2. 环境分离
```python
# 开发环境
config_dev = StrategyConfig(volume_ratio=1.0)  # 更宽松的条件

# 生产环境
config_prod = StrategyConfig(volume_ratio=1.2)  # 标准条件

# 回测环境
config_backtest = StrategyConfig(volume_ratio=1.5)  # 更严格的条件
```

### 3. 动态配置
```python
def get_dynamic_config(stock_code):
    """根据股票特性动态调整配置"""
    base_config = StrategyConfig()
    
    # 根据股票代码调整参数
    if stock_code.startswith("00"):  # 主板股票
        base_config.ema_short_1 = 144
        base_config.volume_ratio = 1.2
    elif stock_code.startswith("30"):  # 创业板股票
        base_config.ema_short_1 = 89
        base_config.volume_ratio = 1.8
    
    return base_config
```

## 监控和调整

### 1. 性能监控
```python
def monitor_config_performance(config, period_days=30):
    """监控配置的性能表现"""
    signals = get_recent_signals(period_days)
    
    metrics = {
        'signal_count': len(signals),
        'success_rate': calculate_success_rate(signals),
        'avg_return': calculate_avg_return(signals),
        'max_drawdown': calculate_max_drawdown(signals)
    }
    
    return metrics
```

### 2. 自动调整
```python
def auto_adjust_config(current_config, performance_metrics):
    """根据性能指标自动调整配置"""
    new_config = current_config.copy()
    
    if performance_metrics['success_rate'] < 0.6:
        # 成功率过低，提高要求
        new_config.volume_ratio *= 1.1
        new_config.max_intrusion_days = max(0, new_config.max_intrusion_days - 1)
    
    elif performance_metrics['signal_count'] < 5:
        # 信号过少，放宽条件
        new_config.volume_ratio *= 0.95
        new_config.max_days = min(120, new_config.max_days + 10)
    
    return new_config
```

## 故障排除

### 常见配置问题

1. **EMA周期设置错误**
   - 错误：ema_short_1 >= ema_long_1
   - 解决：确保短期EMA < 长期EMA

2. **时间窗设置不合理**
   - 错误：min_days > max_days
   - 解决：确保min_days <= max_days

3. **成交量比率过低**
   - 错误：volume_ratio <= 1.0
   - 解决：设置volume_ratio > 1.0

### 配置测试

```python
def test_config(config):
    """测试配置的有效性"""
    try:
        # 验证配置
        assert config.validate(), "配置验证失败"
        
        # 创建策略实例
        strategy = DualChannelFibonacciStrategy(config)
        
        # 使用测试数据验证
        test_data = generate_test_data()
        signal = strategy.analyze("TEST001", "测试股票", preprocessed_df=test_data)
        
        print("配置测试通过")
        return True
        
    except Exception as e:
        print(f"配置测试失败: {e}")
        return False
```

## 总结

合理的参数配置是策略成功的关键。建议：

1. **从默认配置开始**：使用经过验证的默认参数
2. **逐步调优**：一次只调整一个参数
3. **充分回测**：使用足够长的历史数据验证
4. **定期评估**：根据市场变化调整参数
5. **记录变更**：保持配置变更的完整记录

通过合理的配置管理，可以显著提高策略的稳定性和盈利能力。