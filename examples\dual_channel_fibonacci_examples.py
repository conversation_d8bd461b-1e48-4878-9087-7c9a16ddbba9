#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双通道斐波那契策略使用示例

本文件包含了双通道斐波那契策略的各种使用示例，
包括基本使用、自定义配置、批量处理等场景。

作者: QuantFM Team
创建时间: 2025-01-06
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入策略相关模块
from strategies.trending.dual_channel_fibonacci import DualChannelFibonacciStrategy, StrategyConfig
from data.models import Signal

# 导入指标计算模块
from indicators.talib_wrapper import calculate_ema_series, calculate_volume_ma
from indicators.channel_indicators import calculate_dual_channels
from indicators.position_indicators import calculate_price_position


def generate_sample_data(stock_code: str = "000001", length: int = 2000) -> pd.DataFrame:
    """
    生成示例股票数据
    
    Args:
        stock_code: 股票代码
        length: 数据长度
        
    Returns:
        包含完整K线数据的DataFrame
    """
    np.random.seed(hash(stock_code) % 2**32)  # 根据股票代码设置随机种子
    
    # 生成基础价格走势
    base_price = 10.0 + (hash(stock_code) % 100)  # 基础价格
    
    # 生成价格变化序列（带趋势）
    trend = np.linspace(0, 0.5, length)  # 上升趋势
    noise = np.random.normal(0, 0.02, length)  # 随机噪音
    price_changes = trend + noise
    
    # 计算累积价格
    prices = [base_price]
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1.0))
    
    prices = np.array(prices)
    
    # 生成OHLC数据
    highs = prices * (1 + np.abs(np.random.normal(0, 0.01, length)))
    lows = prices * (1 - np.abs(np.random.normal(0, 0.01, length)))
    opens = np.roll(prices, 1)  # 开盘价为前一日收盘价
    opens[0] = prices[0]
    
    # 生成成交量（在突破点附近放大）
    base_volume = np.random.lognormal(12, 0.3, length)
    
    # 在价格大幅上涨时增加成交量
    price_change_pct = np.diff(prices) / prices[:-1]
    volume_multiplier = np.ones(length)
    volume_multiplier[1:] = 1 + np.maximum(0, price_change_pct * 5)
    
    volumes = (base_volume * volume_multiplier).astype(int)
    amounts = prices * volumes
    
    # 生成时间序列
    start_date = datetime.now() - timedelta(days=length)
    dates = pd.date_range(start=start_date, periods=length, freq='D')
    
    # 创建DataFrame
    df = pd.DataFrame({
        'trade_time': dates,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': prices,
        'volume': volumes,
        'amount': amounts
    })
    
    return df


def preprocess_sample_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    预处理示例数据，计算所有必要的技术指标
    
    Args:
        df: 原始K线数据
        
    Returns:
        包含所有指标的DataFrame
    """
    # 1. 计算EMA指标
    ema_periods = [144, 169, 576, 676]
    df = calculate_ema_series(df, ema_periods, 'close')
    
    # 2. 计算成交量移动平均
    volume_periods = [20]
    df = calculate_volume_ma(df, volume_periods, 'volume')
    
    # 3. 计算双通道指标
    channel1_params = {'upper': 144, 'lower': 169}
    channel2_params = {'upper': 576, 'lower': 676}
    df = calculate_dual_channels(df, channel1_params, channel2_params, 'close')
    
    # 4. 计算价格位置关系
    df = calculate_price_position(df, 'close', ['channel1', 'channel2'])
    
    return df


def example_basic_usage():
    """
    示例1: 基本使用方法
    """
    print("=== 示例1: 基本使用方法 ===")
    
    # 创建策略实例（使用默认配置）
    strategy = DualChannelFibonacciStrategy()
    
    # 生成示例数据
    stock_code = "000001"
    stock_name = "平安银行"
    df = generate_sample_data(stock_code, 2000)
    
    # 预处理数据
    df = preprocess_sample_data(df)
    
    # 执行策略分析
    signal = strategy.analyze(
        stock_code=stock_code,
        stock_name=stock_name,
        preprocessed_df=df
    )
    
    # 输出结果
    if signal:
        print(f"✓ 发现信号: {signal.stock_code} ({signal.stock_name})")
        print(f"  信号强度: {signal.signal_strength:.3f}")
        print(f"  最新收盘价: {signal.latest_close:.2f}")
        print(f"  成交量比率: {signal.volume_ratio:.2f}")
        print(f"  信号详情: {signal.signal_note}")
    else:
        print("✗ 未发现信号")
    
    print()


def example_custom_config():
    """
    示例2: 自定义配置使用
    """
    print("=== 示例2: 自定义配置使用 ===")
    
    # 创建自定义配置
    custom_config = StrategyConfig(
        ema_short_1=100,
        ema_long_1=120,
        ema_short_2=400,
        ema_long_2=500,
        volume_ratio=1.5,
        max_days=45
    )
    
    # 验证配置
    if custom_config.validate():
        print("✓ 自定义配置验证通过")
        
        # 创建策略实例
        strategy = DualChannelFibonacciStrategy(custom_config)
        
        # 生成和处理数据
        stock_code = "000002"
        stock_name = "万科A"
        df = generate_sample_data(stock_code, 1500)
        
        # 使用自定义配置的EMA周期
        ema_periods = [100, 120, 400, 500]
        df = calculate_ema_series(df, ema_periods, 'close')
        
        volume_periods = [20]
        df = calculate_volume_ma(df, volume_periods, 'volume')
        
        channel1_params = {'upper': 100, 'lower': 120}
        channel2_params = {'upper': 400, 'lower': 500}
        df = calculate_dual_channels(df, channel1_params, channel2_params, 'close')
        
        df = calculate_price_position(df, 'close', ['channel1', 'channel2'])
        
        # 执行分析
        signal = strategy.analyze(stock_code, stock_name, preprocessed_df=df)
        
        if signal:
            print(f"✓ 自定义配置发现信号: {signal.stock_code}")
            print(f"  配置参数: EMA({custom_config.ema_short_1}/{custom_config.ema_long_1}, "
                  f"{custom_config.ema_short_2}/{custom_config.ema_long_2})")
        else:
            print("✗ 自定义配置未发现信号")
    else:
        print("✗ 自定义配置验证失败:")
        for error in custom_config.get_validation_errors():
            print(f"  - {error}")
    
    print()


def example_batch_processing():
    """
    示例3: 批量处理多只股票
    """
    print("=== 示例3: 批量处理多只股票 ===")
    
    # 定义股票列表
    stock_list = [
        ("000001", "平安银行"),
        ("000002", "万科A"),
        ("000858", "五粮液"),
        ("002415", "海康威视"),
        ("300059", "东方财富")
    ]
    
    # 创建策略实例
    strategy = DualChannelFibonacciStrategy()
    
    # 批量处理
    signals = []
    for stock_code, stock_name in stock_list:
        try:
            print(f"正在处理: {stock_code} ({stock_name})")
            
            # 生成和预处理数据
            df = generate_sample_data(stock_code, 1800)
            df = preprocess_sample_data(df)
            
            # 执行策略
            signal = strategy.analyze(stock_code, stock_name, preprocessed_df=df)
            
            if signal:
                signals.append(signal)
                print(f"  ✓ 发现信号，强度: {signal.signal_strength:.3f}")
            else:
                print(f"  ✗ 未发现信号")
                
        except Exception as e:
            print(f"  ✗ 处理失败: {e}")
    
    # 汇总结果
    print(f"\n批量处理完成:")
    print(f"  处理股票数: {len(stock_list)}")
    print(f"  发现信号数: {len(signals)}")
    print(f"  信号命中率: {len(signals)/len(stock_list)*100:.1f}%")
    
    if signals:
        print("\n信号详情:")
        for signal in sorted(signals, key=lambda x: x.signal_strength, reverse=True):
            print(f"  {signal.stock_code}: 强度={signal.signal_strength:.3f}, "
                  f"价格={signal.latest_close:.2f}")
    
    print()


def example_config_optimization():
    """
    示例4: 配置参数优化
    """
    print("=== 示例4: 配置参数优化 ===")
    
    # 定义参数搜索空间
    volume_ratios = [1.1, 1.2, 1.3, 1.5]
    max_days_options = [40, 50, 62, 80]
    
    # 生成测试数据
    test_stocks = [
        ("TEST001", "测试股票1"),
        ("TEST002", "测试股票2"),
        ("TEST003", "测试股票3")
    ]
    
    test_data = {}
    for stock_code, stock_name in test_stocks:
        df = generate_sample_data(stock_code, 2000)
        df = preprocess_sample_data(df)
        test_data[stock_code] = (stock_name, df)
    
    # 参数优化
    best_config = None
    best_score = 0
    results = []
    
    for volume_ratio in volume_ratios:
        for max_days in max_days_options:
            # 创建配置
            config = StrategyConfig(
                volume_ratio=volume_ratio,
                max_days=max_days
            )
            
            if not config.validate():
                continue
            
            # 测试配置
            strategy = DualChannelFibonacciStrategy(config)
            signal_count = 0
            total_strength = 0
            
            for stock_code, (stock_name, df) in test_data.items():
                signal = strategy.analyze(stock_code, stock_name, preprocessed_df=df)
                if signal:
                    signal_count += 1
                    total_strength += signal.signal_strength
            
            # 计算评分（信号数量 * 平均强度）
            avg_strength = total_strength / signal_count if signal_count > 0 else 0
            score = signal_count * avg_strength
            
            results.append({
                'volume_ratio': volume_ratio,
                'max_days': max_days,
                'signal_count': signal_count,
                'avg_strength': avg_strength,
                'score': score
            })
            
            if score > best_score:
                best_score = score
                best_config = config
    
    # 输出优化结果
    print("参数优化结果:")
    print(f"{'成交量比率':<8} {'最大天数':<8} {'信号数':<6} {'平均强度':<8} {'评分':<8}")
    print("-" * 50)
    
    for result in sorted(results, key=lambda x: x['score'], reverse=True)[:5]:
        print(f"{result['volume_ratio']:<8} {result['max_days']:<8} "
              f"{result['signal_count']:<6} {result['avg_strength']:<8.3f} "
              f"{result['score']:<8.3f}")
    
    if best_config:
        print(f"\n最优配置:")
        print(f"  成交量比率: {best_config.volume_ratio}")
        print(f"  最大天数: {best_config.max_days}")
        print(f"  最优评分: {best_score:.3f}")
    
    print()


def example_signal_analysis():
    """
    示例5: 信号分析和可视化
    """
    print("=== 示例5: 信号分析 ===")
    
    # 创建策略
    strategy = DualChannelFibonacciStrategy()
    
    # 生成数据
    stock_code = "000858"
    stock_name = "五粮液"
    df = generate_sample_data(stock_code, 2500)
    df = preprocess_sample_data(df)
    
    # 执行分析
    signal = strategy.analyze(stock_code, stock_name, preprocessed_df=df)
    
    if signal:
        print(f"信号详细分析: {signal.stock_code} ({signal.stock_name})")
        print(f"{'='*50}")
        
        # 基本信息
        print(f"信号日期: {signal.signal_date}")
        print(f"信号强度: {signal.signal_strength:.3f}")
        print(f"策略名称: {signal.strategy_name}")
        
        # 价格信息
        print(f"\n价格信息:")
        print(f"  最新收盘价: {signal.latest_close:.2f}")
        if hasattr(signal, 'max_high_20d') and signal.max_high_20d:
            print(f"  20日最高价: {signal.max_high_20d:.2f}")
        if hasattr(signal, 'breakout_ratio') and signal.breakout_ratio:
            print(f"  突破幅度: {signal.breakout_ratio:.2%}")
        
        # 成交量信息
        print(f"\n成交量信息:")
        if signal.latest_volume:
            print(f"  最新成交量: {signal.latest_volume:,}")
        if signal.avg_volume:
            print(f"  平均成交量: {signal.avg_volume:,}")
        if signal.volume_ratio:
            print(f"  成交量比率: {signal.volume_ratio:.2f}")
        
        # 技术指标分析
        print(f"\n技术指标分析:")
        latest_row = df.iloc[-1]
        print(f"  EMA144: {latest_row['ema_144']:.2f}")
        print(f"  EMA169: {latest_row['ema_169']:.2f}")
        print(f"  EMA576: {latest_row['ema_576']:.2f}")
        print(f"  EMA676: {latest_row['ema_676']:.2f}")
        print(f"  通道1位置: {latest_row['channel1_position']}")
        print(f"  通道2位置: {latest_row['channel2_position']}")
        
        # 信号备注
        if signal.signal_note:
            print(f"\n信号备注:")
            print(f"  {signal.signal_note}")
        
        # 风险提示
        print(f"\n风险提示:")
        if signal.signal_strength < 0.3:
            print("  ⚠️  信号强度较低，建议谨慎操作")
        elif signal.signal_strength > 0.7:
            print("  ✅ 信号强度较高，可考虑关注")
        else:
            print("  ℹ️  信号强度中等，建议结合其他指标")
        
    else:
        print(f"未发现信号: {stock_code} ({stock_name})")
        
        # 分析原因
        print("\n可能原因分析:")
        latest_row = df.iloc[-1]
        
        if latest_row['close'] < latest_row['channel1_lower']:
            print("  - 当前价格仍在通道1下方")
        elif latest_row['close'] < latest_row['channel1_upper']:
            print("  - 当前价格在通道1内部")
        elif latest_row['close'] < latest_row['channel2_upper']:
            print("  - 当前价格在通道1上方但未突破通道2")
        else:
            print("  - 可能不满足其他条件（成交量、时间窗等）")
    
    print()


def example_performance_monitoring():
    """
    示例6: 性能监控
    """
    print("=== 示例6: 性能监控 ===")
    
    import time
    from indicators.cache_manager import get_cache_stats, cleanup_expired_cache
    
    # 创建策略
    strategy = DualChannelFibonacciStrategy()
    
    # 性能测试
    stock_codes = [f"TEST{i:03d}" for i in range(20)]
    
    print("开始性能测试...")
    start_time = time.time()
    
    processed_count = 0
    signal_count = 0
    
    for i, stock_code in enumerate(stock_codes):
        try:
            # 生成数据
            df = generate_sample_data(stock_code, 1500)
            df = preprocess_sample_data(df)
            
            # 执行策略
            signal = strategy.analyze(stock_code, f"测试股票{i+1}", preprocessed_df=df)
            
            processed_count += 1
            if signal:
                signal_count += 1
            
            # 每5只股票显示一次进度
            if (i + 1) % 5 == 0:
                elapsed = time.time() - start_time
                print(f"  已处理: {i+1}/{len(stock_codes)}, "
                      f"耗时: {elapsed:.2f}秒, "
                      f"平均: {elapsed/(i+1):.3f}秒/只")
        
        except Exception as e:
            print(f"  处理 {stock_code} 失败: {e}")
    
    total_time = time.time() - start_time
    
    # 性能统计
    print(f"\n性能统计:")
    print(f"  总处理时间: {total_time:.2f}秒")
    print(f"  处理股票数: {processed_count}")
    print(f"  平均处理时间: {total_time/processed_count:.3f}秒/只")
    print(f"  发现信号数: {signal_count}")
    print(f"  信号命中率: {signal_count/processed_count*100:.1f}%")
    
    # 缓存统计
    cache_stats = get_cache_stats()
    print(f"\n缓存统计:")
    print(f"  缓存条目数: {cache_stats['total_entries']}")
    print(f"  有效条目数: {cache_stats['valid_entries']}")
    print(f"  过期条目数: {cache_stats['expired_entries']}")
    
    # 清理过期缓存
    cleaned_count = cleanup_expired_cache()
    if cleaned_count > 0:
        print(f"  清理过期缓存: {cleaned_count}个")
    
    print()


def main():
    """
    主函数：运行所有示例
    """
    print("双通道斐波那契策略使用示例")
    print("=" * 50)
    
    try:
        # 运行各个示例
        example_basic_usage()
        example_custom_config()
        example_batch_processing()
        example_config_optimization()
        example_signal_analysis()
        example_performance_monitoring()
        
        print("所有示例运行完成！")
        
    except Exception as e:
        print(f"示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()