#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场状态配置

定义不同市场状态下的指标权重和确认要求。

作者: QuantFM Team
创建时间: 2025-08-11
"""

import numpy as np
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)

# 市场状态配置
MARKET_REGIMES = {
    # 牛市趋势
    'trending_bull': {
        'indicator_weights': {
            'macd': 0.3,      # MACD在趋势市场中更重要
            'rsi': 0.2,       # RSI权重适中
            'kdj': 0.15,      # KDJ敏感度高，权重稍低
            'cci': 0.2,       # CCI趋势指标，权重较高
            'obv': 0.15       # OBV确认趋势
        },
        'confirmation_requirements': [
            'macd_golden_cross',
            'rsi_above_centerline',
            'volume_confirmation'
        ],
        'signal_sensitivity': 0.8,  # 中等敏感度
        'min_confirmations': 2,
        'pattern_reliability_bonus': {
            'bullish_divergence': 0.2,
            'trend_continuation_bullish': 0.1
        }
    },
    
    # 熊市趋势
    'trending_bear': {
        'indicator_weights': {
            'macd': 0.3,
            'rsi': 0.2,
            'kdj': 0.15,
            'cci': 0.2,
            'obv': 0.15
        },
        'confirmation_requirements': [
            'macd_death_cross',
            'rsi_below_centerline',
            'volume_confirmation'
        ],
        'signal_sensitivity': 0.8,
        'min_confirmations': 2,
        'pattern_reliability_bonus': {
            'bearish_divergence': 0.2,
            'trend_continuation_bearish': 0.1
        }
    },
    
    # 横盘震荡
    'sideways': {
        'indicator_weights': {
            'macd': 0.15,     # MACD在震荡市场中效果较差
            'rsi': 0.35,      # RSI在震荡市场中更可靠
            'kdj': 0.25,      # KDJ适合震荡市场
            'cci': 0.15,      # CCI中等权重
            'obv': 0.1        # OBV在震荡市场中作用有限
        },
        'confirmation_requirements': [
            'rsi_extreme_zones',
            'kdj_cross_signals',
            'multiple_timeframe_confirmation'
        ],
        'signal_sensitivity': 1.2,  # 更高敏感度
        'min_confirmations': 3,      # 需要更多确认
        'pattern_reliability_bonus': {
            'bullish_divergence': 0.3,
            'bearish_divergence': 0.3
        }
    },
    
    # 高波动市场
    'high_volatility': {
        'indicator_weights': {
            'macd': 0.2,
            'rsi': 0.25,
            'kdj': 0.1,       # KDJ在高波动中容易误判
            'cci': 0.25,      # CCI适合高波动环境
            'obv': 0.2        # OBV帮助确认真实突破
        },
        'confirmation_requirements': [
            'volume_surge_confirmation',
            'multiple_indicator_agreement',
            'breakout_confirmation'
        ],
        'signal_sensitivity': 0.6,  # 降低敏感度，减少噪音
        'min_confirmations': 3,
        'pattern_reliability_bonus': {
            'trend_continuation_bullish': 0.15,
            'trend_continuation_bearish': 0.15
        }
    }
}

# 市场状态检测参数
REGIME_DETECTION_PARAMS = {
    'trend_strength_threshold': 0.1,      # 趋势强度阈值
    'volatility_threshold': 2.0,          # 波动率阈值
    'volume_surge_threshold': 1.5,        # 成交量激增阈值
    'lookback_period': 20,                # 回看周期
    'confirmation_period': 5,             # 确认周期
    'regime_change_threshold': 0.3        # 状态变化阈值
}


def detect_market_regime(indicators: Dict[str, Any], prices: Optional[np.ndarray] = None,
                        volumes: Optional[np.ndarray] = None) -> str:
    """检测市场状态
    
    Args:
        indicators: 技术指标数据
        prices: 价格数据
        volumes: 成交量数据
        
    Returns:
        市场状态字符串
    """
    try:
        # 默认状态
        if not indicators:
            return 'sideways'
        
        # 从指标中提取信息
        regime_scores = {
            'trending_bull': 0.0,
            'trending_bear': 0.0,
            'sideways': 0.0,
            'high_volatility': 0.0
        }
        
        # MACD分析
        if 'macd' in indicators:
            macd_data = indicators['macd']
            if isinstance(macd_data, dict):
                dif = macd_data.get('dif')
                dea = macd_data.get('dea')
                
                if dif is not None and dea is not None and len(dif) > 0 and len(dea) > 0:
                    current_dif = dif[-1]
                    current_dea = dea[-1]
                    
                    if current_dif > current_dea and current_dif > 0:
                        regime_scores['trending_bull'] += 0.3
                    elif current_dif < current_dea and current_dif < 0:
                        regime_scores['trending_bear'] += 0.3
                    else:
                        regime_scores['sideways'] += 0.2
        
        # RSI分析
        if 'rsi' in indicators:
            rsi_data = indicators['rsi']
            if isinstance(rsi_data, dict) and 'rsi' in rsi_data:
                rsi_values = rsi_data['rsi']
                if hasattr(rsi_values, '__len__') and len(rsi_values) > 0:
                    current_rsi = rsi_values[-1]
                    
                    if current_rsi > 70:
                        regime_scores['trending_bull'] += 0.2
                    elif current_rsi < 30:
                        regime_scores['trending_bear'] += 0.2
                    elif 40 <= current_rsi <= 60:
                        regime_scores['sideways'] += 0.3
        
        # 价格波动率分析
        if prices is not None and len(prices) >= 20:
            recent_prices = prices[-20:]
            price_changes = np.diff(recent_prices) / recent_prices[:-1]
            volatility = np.std(price_changes)
            
            if volatility > REGIME_DETECTION_PARAMS['volatility_threshold'] * 0.01:
                regime_scores['high_volatility'] += 0.4
            
            # 趋势分析
            trend = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
            if abs(trend) > REGIME_DETECTION_PARAMS['trend_strength_threshold']:
                if trend > 0:
                    regime_scores['trending_bull'] += 0.3
                else:
                    regime_scores['trending_bear'] += 0.3
            else:
                regime_scores['sideways'] += 0.2
        
        # 成交量分析
        if volumes is not None and len(volumes) >= 10:
            recent_volumes = volumes[-10:]
            avg_volume = np.mean(recent_volumes[:-5])
            current_avg_volume = np.mean(recent_volumes[-5:])
            
            if avg_volume > 0:
                volume_ratio = current_avg_volume / avg_volume
                if volume_ratio > REGIME_DETECTION_PARAMS['volume_surge_threshold']:
                    regime_scores['high_volatility'] += 0.2
        
        # 确定最终状态
        max_score = max(regime_scores.values())
        if max_score < 0.3:  # 分数太低，默认为横盘
            return 'sideways'
        
        for regime, score in regime_scores.items():
            if score == max_score:
                return regime
        
        return 'sideways'
        
    except Exception as e:
        logger.warning(f"市场状态检测失败: {e}")
        return 'sideways'


def get_regime_config(regime: str) -> Dict[str, Any]:
    """获取市场状态配置
    
    Args:
        regime: 市场状态
        
    Returns:
        状态配置字典
    """
    return MARKET_REGIMES.get(regime, MARKET_REGIMES['sideways'])


def adjust_weights_for_regime(base_weights: Dict[str, float], regime: str) -> Dict[str, float]:
    """根据市场状态调整指标权重
    
    Args:
        base_weights: 基础权重
        regime: 市场状态
        
    Returns:
        调整后的权重
    """
    try:
        regime_config = get_regime_config(regime)
        regime_weights = regime_config.get('indicator_weights', {})
        
        # 合并权重
        adjusted_weights = {}
        total_weight = 0
        
        for indicator in base_weights.keys():
            base_weight = base_weights.get(indicator, 0)
            regime_weight = regime_weights.get(indicator, base_weight)
            
            # 加权平均
            adjusted_weight = (base_weight + regime_weight) / 2
            adjusted_weights[indicator] = adjusted_weight
            total_weight += adjusted_weight
        
        # 标准化权重
        if total_weight > 0:
            for indicator in adjusted_weights:
                adjusted_weights[indicator] /= total_weight
        
        return adjusted_weights
        
    except Exception as e:
        logger.warning(f"权重调整失败: {e}")
        return base_weights


def check_regime_confirmations(regime: str, signals: List[str]) -> bool:
    """检查市场状态确认条件
    
    Args:
        regime: 市场状态
        signals: 信号列表
        
    Returns:
        是否满足确认条件
    """
    try:
        regime_config = get_regime_config(regime)
        required_confirmations = regime_config.get('confirmation_requirements', [])
        min_confirmations = regime_config.get('min_confirmations', 1)
        
        # 检查满足的确认条件数量
        satisfied_confirmations = 0
        for requirement in required_confirmations:
            if any(requirement in signal for signal in signals):
                satisfied_confirmations += 1
        
        return satisfied_confirmations >= min_confirmations
        
    except Exception as e:
        logger.warning(f"状态确认检查失败: {e}")
        return False


def get_pattern_reliability_adjustment(pattern_type: str, regime: str) -> float:
    """获取模式可靠性调整
    
    Args:
        pattern_type: 模式类型
        regime: 市场状态
        
    Returns:
        可靠性调整值
    """
    try:
        regime_config = get_regime_config(regime)
        reliability_bonus = regime_config.get('pattern_reliability_bonus', {})
        
        return reliability_bonus.get(pattern_type, 0.0)
        
    except Exception as e:
        logger.warning(f"模式可靠性调整获取失败: {e}")
        return 0.0


def calculate_regime_stability(price_history: np.ndarray, regime_history: List[str]) -> float:
    """计算市场状态稳定性
    
    Args:
        price_history: 价格历史
        regime_history: 状态历史
        
    Returns:
        稳定性评分 (0-1)
    """
    try:
        if len(regime_history) < 5:
            return 0.5
        
        # 计算状态变化频率
        regime_changes = 0
        for i in range(1, len(regime_history)):
            if regime_history[i] != regime_history[i-1]:
                regime_changes += 1
        
        change_ratio = regime_changes / (len(regime_history) - 1)
        
        # 稳定性评分（变化越少越稳定）
        stability = 1.0 - min(change_ratio, 1.0)
        
        return stability
        
    except Exception as e:
        logger.warning(f"状态稳定性计算失败: {e}")
        return 0.5
