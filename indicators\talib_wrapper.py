"""
TA-Lib包装模块

提供标准化的技术指标计算接口，包装talib库的常用指标。
主要功能：
- EMA批量计算
- 成交量移动平均计算
- 其他常用技术指标的标准化接口
- 缓存机制优化性能
"""

import pandas as pd
import numpy as np
import talib
from typing import List, Dict, Optional, Union
import logging
from .cache_manager import get_global_cache

logger = logging.getLogger(__name__)


def calculate_ema_series(df: pd.DataFrame, periods: List[int], 
                        price_column: str = 'close') -> pd.DataFrame:
    """
    批量计算多个周期的EMA指标（优化版本，带缓存）
    
    Args:
        df: 包含价格数据的DataFrame
        periods: EMA周期列表，如[144, 169, 576, 676]
        price_column: 用于计算EMA的价格列名，默认为'close'
        
    Returns:
        包含所有EMA列的DataFrame副本
        
    Raises:
        ValueError: 当输入数据无效时
        KeyError: 当指定的价格列不存在时
    """
    if df.empty:
        raise ValueError("输入DataFrame不能为空")
        
    if price_column not in df.columns:
        raise KeyError(f"价格列 '{price_column}' 不存在于DataFrame中")
        
    if not periods:
        raise ValueError("周期列表不能为空")
    
    # 性能优化：检查缓存
    cache = get_global_cache()
    cache_params = {
        'function': 'calculate_ema_series',
        'periods': sorted(periods),  # 排序确保缓存键一致性
        'price_column': price_column
    }
    
    cached_result = cache.get(df, cache_params, columns=[price_column])
    if cached_result is not None:
        logger.debug(f"EMA计算缓存命中，周期: {periods}")
        return cached_result
    
    # 性能优化：避免完整DataFrame复制，只复制必要的列
    result_df = df.copy()
    
    try:
        # 性能优化：预先转换为numpy数组，避免重复转换
        prices = df[price_column].values.astype(np.float64, copy=False)
        
        # 性能优化：过滤无效周期，避免不必要的计算
        valid_periods = [p for p in periods if p > 0]
        if len(valid_periods) != len(periods):
            logger.warning(f"跳过无效周期: {[p for p in periods if p <= 0]}")
        
        # 性能优化：批量计算，减少函数调用开销
        ema_results = {}
        for period in valid_periods:
            ema_values = talib.EMA(prices, timeperiod=period)
            ema_results[f'ema_{period}'] = ema_values
        
        # 性能优化：批量赋值，减少DataFrame操作次数
        for col_name, values in ema_results.items():
            result_df[col_name] = values
        
        # 存入缓存
        cache.put(df, cache_params, result_df, columns=[price_column])
            
        logger.debug(f"成功计算 {len(valid_periods)} 个EMA指标")
        return result_df
        
    except Exception as e:
        logger.error(f"计算EMA失败: {str(e)}")
        raise


def calculate_volume_ma(df: pd.DataFrame, periods: List[int],
                       volume_column: str = 'volume') -> pd.DataFrame:
    """
    计算成交量移动平均（增强错误处理版本）

    Args:
        df: 包含成交量数据的DataFrame
        periods: 移动平均周期列表，如[5, 10, 20]
        volume_column: 成交量列名，默认为'volume'

    Returns:
        包含成交量移动平均列的DataFrame副本

    Raises:
        ValueError: 当输入数据无效时
        KeyError: 当指定的成交量列不存在时
    """
    if df.empty:
        raise ValueError("输入DataFrame不能为空")

    if volume_column not in df.columns:
        raise KeyError(f"成交量列 '{volume_column}' 不存在于DataFrame中")

    if not periods:
        raise ValueError("周期列表不能为空")

    # 性能优化：避免完整DataFrame复制
    result_df = df.copy()

    try:
        # 数据质量检查
        volume_series = df[volume_column]
        if volume_series.isna().all():
            logger.warning("成交量列全部为NaN，使用默认值")
            volumes = np.ones(len(df), dtype=np.float64)
        else:
            # 处理NaN值和负值
            volumes = volume_series.fillna(0).clip(lower=0).values.astype(np.float64, copy=False)

        # 数据长度检查
        # if len(volumes) < max(periods):
        #     logger.warning(f"数据长度 {len(volumes)} 小于最大周期 {max(periods)}，可能影响计算结果")

        # 直接使用传入的periods，不进行过滤（简化逻辑）
        valid_periods = periods

        # 性能优化：批量计算，减少函数调用开销
        volume_ma_results = {}
        for period in valid_periods:
            try:
                volume_ma = talib.SMA(volumes, timeperiod=period)
                # 处理计算结果中的NaN
                if np.isnan(volume_ma).all():
                    logger.warning(f"周期 {period} 的移动平均计算结果全为NaN，使用备用方法")
                    # 使用pandas rolling作为备用方法
                    volume_ma = volume_series.rolling(window=period, min_periods=1).mean().values

                volume_ma_results[f'volume_ma_{period}'] = volume_ma

            except Exception as period_e:
                logger.error(f"计算周期 {period} 的移动平均失败: {period_e}")
                # 使用备用方法
                try:
                    volume_ma = volume_series.rolling(window=period, min_periods=1).mean().values
                    volume_ma_results[f'volume_ma_{period}'] = volume_ma
                    logger.info(f"使用备用方法成功计算周期 {period} 的移动平均")
                except Exception as backup_e:
                    logger.error(f"备用方法也失败: {backup_e}")
                    continue

        # 性能优化：批量赋值
        for col_name, values in volume_ma_results.items():
            result_df[col_name] = values

        logger.debug(f"成功计算 {len(volume_ma_results)} 个成交量移动平均")
        return result_df

    except Exception as e:
        logger.error(f"计算成交量移动平均失败: {str(e)}")
        # 返回原始DataFrame而不是抛出异常，提高容错性
        logger.warning("返回原始DataFrame，未添加成交量移动平均列")
        return result_df


def calculate_sma(df: pd.DataFrame, period: int, 
                  price_column: str = 'close') -> pd.Series:
    """
    计算简单移动平均(SMA)
    
    Args:
        df: 包含价格数据的DataFrame
        period: 移动平均周期
        price_column: 用于计算SMA的价格列名
        
    Returns:
        SMA值的Series
    """
    if df.empty or period <= 0:
        raise ValueError("输入数据无效")
        
    if price_column not in df.columns:
        raise KeyError(f"价格列 '{price_column}' 不存在")
        
    try:
        prices = df[price_column].values.astype(float)
        sma_values = talib.SMA(prices, timeperiod=period)
        return pd.Series(sma_values, index=df.index, name=f'sma_{period}')
        
    except Exception as e:
        logger.error(f"计算SMA失败: {str(e)}")
        raise


def calculate_rsi(df: pd.DataFrame, period: int = 14, 
                  price_column: str = 'close') -> pd.Series:
    """
    计算相对强弱指数(RSI)
    
    Args:
        df: 包含价格数据的DataFrame
        period: RSI计算周期，默认14
        price_column: 用于计算RSI的价格列名
        
    Returns:
        RSI值的Series
    """
    if df.empty or period <= 0:
        raise ValueError("输入数据无效")
        
    if price_column not in df.columns:
        raise KeyError(f"价格列 '{price_column}' 不存在")
        
    try:
        prices = df[price_column].values.astype(float)
        rsi_values = talib.RSI(prices, timeperiod=period)
        return pd.Series(rsi_values, index=df.index, name=f'rsi_{period}')
        
    except Exception as e:
        logger.error(f"计算RSI失败: {str(e)}")
        raise


def calculate_macd(df: pd.DataFrame, fastperiod: int = 12, slowperiod: int = 26, 
                   signalperiod: int = 9, price_column: str = 'close') -> Dict[str, pd.Series]:
    """
    计算MACD指标
    
    Args:
        df: 包含价格数据的DataFrame
        fastperiod: 快线周期，默认12
        slowperiod: 慢线周期，默认26
        signalperiod: 信号线周期，默认9
        price_column: 用于计算MACD的价格列名
        
    Returns:
        包含MACD、信号线和柱状图的字典
    """
    if df.empty:
        raise ValueError("输入DataFrame不能为空")
        
    if price_column not in df.columns:
        raise KeyError(f"价格列 '{price_column}' 不存在")
        
    try:
        prices = df[price_column].values.astype(float)
        macd, macdsignal, macdhist = talib.MACD(prices, 
                                               fastperiod=fastperiod,
                                               slowperiod=slowperiod, 
                                               signalperiod=signalperiod)
        
        return {
            'macd': pd.Series(macd, index=df.index, name='macd'),
            'signal': pd.Series(macdsignal, index=df.index, name='macd_signal'),
            'histogram': pd.Series(macdhist, index=df.index, name='macd_histogram')
        }
        
    except Exception as e:
        logger.error(f"计算MACD失败: {str(e)}")
        raise


def validate_dataframe(df: pd.DataFrame, required_columns: List[str]) -> bool:
    """
    验证DataFrame是否包含必需的列
    
    Args:
        df: 待验证的DataFrame
        required_columns: 必需的列名列表
        
    Returns:
        验证是否通过
    """
    if df.empty:
        logger.error("DataFrame为空")
        return False
        
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        logger.error(f"缺少必需的列: {missing_columns}")
        return False
        
    return True


def get_valid_data_range(df: pd.DataFrame, min_periods: int) -> tuple:
    """
    获取有效数据范围，排除NaN值过多的区间
    
    Args:
        df: 输入DataFrame
        min_periods: 最小有效数据点数
        
    Returns:
        (start_index, end_index) 有效数据范围
    """
    if df.empty:
        return (0, 0)
        
    # 找到第一个非NaN值的位置
    first_valid = df.first_valid_index()
    last_valid = df.last_valid_index()
    
    if first_valid is None or last_valid is None:
        return (0, 0)
        
    start_idx = df.index.get_loc(first_valid)
    end_idx = df.index.get_loc(last_valid) + 1
    
    # 确保有足够的数据点
    if end_idx - start_idx < min_periods:
        logger.warning(f"数据点不足: {end_idx - start_idx} < {min_periods}")
        return (0, 0)
        
    return (start_idx, end_idx)