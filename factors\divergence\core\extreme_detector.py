#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
极值点检测器

实现自适应极值点检测，支持实时分析和候选极值点处理。
解决最新K线的模糊性问题，提供完成概率计算。

作者: QuantFM Team
创建时间: 2025-08-11
"""

import numpy as np
from dataclasses import dataclass
from typing import List, Tuple, Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


@dataclass
class ExtremePoint:
    """极值点数据类"""
    index: int                    # 在数据中的索引位置
    price: float                  # 极值价格
    strength: float               # 极值强度 (0-1)
    type: str                     # 'peak' 或 'trough'
    confirmed: bool               # 是否已确认
    completion_prob: float        # 完成概率 (0-1)
    
    def __post_init__(self):
        """数据验证"""
        if self.strength < 0 or self.strength > 1:
            raise ValueError(f"强度必须在0-1之间，当前值: {self.strength}")
        if self.completion_prob < 0 or self.completion_prob > 1:
            raise ValueError(f"完成概率必须在0-1之间，当前值: {self.completion_prob}")
        if self.type not in ['peak', 'trough']:
            raise ValueError(f"类型必须是'peak'或'trough'，当前值: {self.type}")


class AdaptiveExtremeDetector:
    """自适应极值点检测器
    
    核心功能:
    1. 基于ATR的动态参数调整
    2. 多层级过滤：主要极值 → 次要极值 → 噪声过滤
    3. 最新K线模糊性处理
    4. 完成概率计算
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化检测器
        
        Args:
            config: 配置参数字典
        """
        self.config = config or {}
        
        # 基础参数
        self.base_distance = self.config.get('base_distance', 5)
        self.base_prominence_ratio = self.config.get('base_prominence_ratio', 0.02)
        self.min_strength_threshold = self.config.get('min_strength_threshold', 0.3)
        self.adaptive_volatility_factor = self.config.get('adaptive_volatility_factor', 1.5)
        
        # 缓存
        self._atr_cache = {}
        self._extremes_cache = {}
        
    def detect_extremes(self, prices: np.ndarray, high: Optional[np.ndarray] = None, 
                       low: Optional[np.ndarray] = None, volume: Optional[np.ndarray] = None) -> List[ExtremePoint]:
        """检测极值点
        
        Args:
            prices: 价格序列（通常是收盘价）
            high: 最高价序列
            low: 最低价序列  
            volume: 成交量序列
            
        Returns:
            极值点列表，包含确认的和候选的
        """
        if len(prices) < 10:
            logger.warning(f"数据长度不足，需要至少10个数据点，当前: {len(prices)}")
            return []
            
        try:
            # 计算ATR用于动态参数调整
            atr = self._calculate_atr(prices, high, low)
            
            # 动态调整参数
            distance, prominence = self._get_adaptive_params(prices, atr)
            
            # 检测峰值和谷值
            peaks = self._find_peaks_numpy(prices, distance, prominence, 'peak')
            troughs = self._find_peaks_numpy(-prices, distance, prominence, 'trough')
            
            # 合并并排序
            all_extremes = peaks + troughs
            all_extremes.sort(key=lambda x: x.index)
            
            # 处理最新K线的模糊性
            all_extremes = self._handle_latest_bar_ambiguity(prices, all_extremes)
            
            # 计算强度和完成概率
            for extreme in all_extremes:
                extreme.strength = self._calculate_strength(extreme, prices, volume)
                extreme.completion_prob = self._calculate_completion_probability(extreme, prices)
            
            # 过滤弱信号
            filtered_extremes = [e for e in all_extremes if e.strength >= self.min_strength_threshold]
            
            logger.debug(f"检测到 {len(filtered_extremes)} 个有效极值点")
            return filtered_extremes
            
        except Exception as e:
            logger.error(f"极值点检测失败: {e}")
            return []
    
    def _calculate_atr(self, prices: np.ndarray, high: Optional[np.ndarray] = None, 
                      low: Optional[np.ndarray] = None, period: int = 14) -> float:
        """计算平均真实波幅(ATR)"""
        cache_key = f"atr_{len(prices)}_{period}"
        if cache_key in self._atr_cache:
            return self._atr_cache[cache_key]
            
        if high is None or low is None:
            # 使用价格序列的简化ATR计算
            price_changes = np.abs(np.diff(prices))
            atr = np.mean(price_changes[-period:]) if len(price_changes) >= period else np.mean(price_changes)
        else:
            # 标准ATR计算
            high_low = high - low
            high_close = np.abs(high[1:] - prices[:-1])
            low_close = np.abs(low[1:] - prices[:-1])
            
            true_range = np.maximum(high_low[1:], np.maximum(high_close, low_close))
            atr = np.mean(true_range[-period:]) if len(true_range) >= period else np.mean(true_range)
        
        self._atr_cache[cache_key] = atr
        return atr
    
    def _get_adaptive_params(self, prices: np.ndarray, atr: float) -> Tuple[int, float]:
        """获取自适应参数"""
        # 基于ATR调整距离参数
        volatility_factor = atr / np.mean(prices[-20:]) if len(prices) >= 20 else atr / np.mean(prices)
        
        # 动态距离：高波动时增加距离
        adaptive_distance = max(3, int(self.base_distance * (1 + volatility_factor * self.adaptive_volatility_factor)))
        
        # 动态突出度：基于价格水平和波动率
        price_level = np.mean(prices[-10:])
        adaptive_prominence = self.base_prominence_ratio * price_level * (1 + volatility_factor)
        
        return adaptive_distance, adaptive_prominence
    
    def _find_peaks_numpy(self, data: np.ndarray, distance: int, prominence: float, 
                         peak_type: str) -> List[ExtremePoint]:
        """使用numpy实现峰值检测（无scipy依赖）"""
        extremes = []
        
        if len(data) < distance * 2 + 1:
            return extremes
            
        # 简单的峰值检测算法
        for i in range(distance, len(data) - distance):
            is_extreme = True
            current_value = data[i]
            
            # 检查左右邻域
            for j in range(i - distance, i + distance + 1):
                if j == i:
                    continue
                if current_value <= data[j]:  # 对于峰值，当前值应该是最大的
                    is_extreme = False
                    break
            
            # 检查突出度
            if is_extreme:
                left_min = np.min(data[max(0, i - distance):i])
                right_min = np.min(data[i + 1:min(len(data), i + distance + 1)])
                actual_prominence = current_value - max(left_min, right_min)
                
                if actual_prominence >= prominence:
                    # 调整峰值类型（对于-prices，需要反转）
                    actual_type = 'trough' if peak_type == 'trough' else 'peak'
                    actual_price = -current_value if peak_type == 'trough' else current_value
                    
                    extremes.append(ExtremePoint(
                        index=i,
                        price=actual_price,
                        strength=0.0,  # 稍后计算
                        type=actual_type,
                        confirmed=True,  # 稍后根据位置调整
                        completion_prob=1.0  # 稍后计算
                    ))
        
        return extremes
    
    def _handle_latest_bar_ambiguity(self, prices: np.ndarray, extremes: List[ExtremePoint]) -> List[ExtremePoint]:
        """处理最新K线的模糊性"""
        if len(prices) < 3 or not extremes:
            return extremes
            
        last_index = len(prices) - 1
        
        # 检查最后几个点是否可能形成极值
        if self._is_potential_extreme(prices, last_index):
            # 检测斜率反转
            if self._has_slope_inversion(prices, last_index):
                # 确定极值类型
                extreme_type = self._determine_extreme_type(prices, last_index)
                
                # 创建候选极值点
                candidate = ExtremePoint(
                    index=last_index,
                    price=prices[last_index],
                    strength=0.0,  # 稍后计算
                    type=extreme_type,
                    confirmed=False,  # 候选状态
                    completion_prob=0.0  # 稍后计算
                )
                
                extremes.append(candidate)
        
        return extremes
    
    def _is_potential_extreme(self, prices: np.ndarray, index: int) -> bool:
        """检查是否为潜在极值点"""
        if index < 2:
            return False
            
        # 简单的局部极值检查
        current = prices[index]
        prev1 = prices[index - 1]
        prev2 = prices[index - 2]
        
        # 检查是否形成局部最高或最低
        is_local_high = current >= prev1 and prev1 >= prev2
        is_local_low = current <= prev1 and prev1 <= prev2
        
        return is_local_high or is_local_low
    
    def _has_slope_inversion(self, prices: np.ndarray, index: int) -> bool:
        """检测斜率反转"""
        if index < 3:
            return False
            
        # 计算最近几个点的斜率
        recent_slope = prices[index] - prices[index - 1]
        prev_slope = prices[index - 1] - prices[index - 2]
        
        # 检查斜率符号是否发生变化
        return (recent_slope > 0 and prev_slope < 0) or (recent_slope < 0 and prev_slope > 0)
    
    def _determine_extreme_type(self, prices: np.ndarray, index: int) -> str:
        """确定极值类型"""
        if index < 1:
            return 'peak'
            
        # 基于最近的价格变化确定类型
        if prices[index] > prices[index - 1]:
            return 'peak'
        else:
            return 'trough'
    
    def _calculate_strength(self, extreme: ExtremePoint, prices: np.ndarray, 
                          volume: Optional[np.ndarray] = None) -> float:
        """计算极值点强度"""
        try:
            # 价格变化幅度
            price_strength = self._calculate_price_strength(extreme, prices)
            
            # 成交量确认
            volume_strength = self._calculate_volume_strength(extreme, volume) if volume is not None else 0.5
            
            # 时间因子
            time_strength = self._calculate_time_strength(extreme, prices)
            
            # 综合强度计算
            total_strength = (price_strength * 0.5 + volume_strength * 0.3 + time_strength * 0.2)
            
            return np.clip(total_strength, 0.0, 1.0)
            
        except Exception as e:
            logger.warning(f"强度计算失败: {e}")
            return 0.5
    
    def _calculate_price_strength(self, extreme: ExtremePoint, prices: np.ndarray) -> float:
        """计算价格强度"""
        index = extreme.index
        window = min(10, index, len(prices) - index - 1)
        
        if window < 1:
            return 0.5
            
        # 计算局部价格范围
        start_idx = max(0, index - window)
        end_idx = min(len(prices), index + window + 1)
        local_prices = prices[start_idx:end_idx]
        
        price_range = np.max(local_prices) - np.min(local_prices)
        if price_range == 0:
            return 0.5
            
        # 计算相对突出度
        if extreme.type == 'peak':
            prominence = extreme.price - np.min(local_prices)
        else:
            prominence = np.max(local_prices) - extreme.price
            
        strength = prominence / price_range
        return np.clip(strength, 0.0, 1.0)
    
    def _calculate_volume_strength(self, extreme: ExtremePoint, volume: np.ndarray) -> float:
        """计算成交量强度"""
        index = extreme.index
        if index >= len(volume):
            return 0.5
            
        # 计算成交量相对强度
        window = min(20, len(volume))
        start_idx = max(0, index - window + 1)
        recent_volume = volume[start_idx:index + 1]
        
        if len(recent_volume) < 2:
            return 0.5
            
        current_volume = volume[index]
        avg_volume = np.mean(recent_volume[:-1])
        
        if avg_volume == 0:
            return 0.5
            
        volume_ratio = current_volume / avg_volume
        # 使用sigmoid函数将比率转换为0-1强度
        strength = 1 / (1 + np.exp(-(volume_ratio - 1) * 2))
        
        return np.clip(strength, 0.0, 1.0)
    
    def _calculate_time_strength(self, extreme: ExtremePoint, prices: np.ndarray) -> float:
        """计算时间强度（基于距离最新数据的时间）"""
        index = extreme.index
        total_length = len(prices)
        
        # 时间衰减：越近的极值点强度越高
        time_factor = 1.0 - (total_length - index - 1) / total_length
        
        return np.clip(time_factor, 0.0, 1.0)
    
    def _calculate_completion_probability(self, extreme: ExtremePoint, prices: np.ndarray) -> float:
        """计算完成概率"""
        if extreme.confirmed:
            return 1.0
            
        # 对于候选极值点，基于多个因子计算完成概率
        try:
            # 强度因子
            strength_factor = extreme.strength
            
            # 位置因子（距离末尾的位置）
            position_factor = 1.0 if extreme.index == len(prices) - 1 else 0.8
            
            # 趋势一致性因子
            trend_factor = self._calculate_trend_consistency(extreme, prices)
            
            # 综合概率计算
            probability = (strength_factor * 0.4 + position_factor * 0.3 + trend_factor * 0.3)
            
            return np.clip(probability, 0.0, 1.0)
            
        except Exception as e:
            logger.warning(f"完成概率计算失败: {e}")
            return 0.5
    
    def _calculate_trend_consistency(self, extreme: ExtremePoint, prices: np.ndarray) -> float:
        """计算趋势一致性"""
        index = extreme.index
        if index < 3:
            return 0.5
            
        # 分析最近的趋势方向
        recent_trend = prices[index] - prices[index - 3]
        
        # 检查极值类型与趋势的一致性
        if extreme.type == 'peak' and recent_trend > 0:
            return 0.8  # 上升趋势中的峰值
        elif extreme.type == 'trough' and recent_trend < 0:
            return 0.8  # 下降趋势中的谷值
        else:
            return 0.4  # 趋势反转点，不确定性较高
