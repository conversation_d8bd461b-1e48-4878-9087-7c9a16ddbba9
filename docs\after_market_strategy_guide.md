# 盘后策略选股系统使用指南

## 概述

盘后策略选股系统是QuantFM项目的核心功能之一，用于在每个交易日收盘后自动执行策略选股，并将结果存储到数据库和发送到飞书通知。

## 系统架构

```
盘后策略选股系统
├── 主控制器 (AfterMarketScheduler)
├── 数据获取 (从stock_info和stock_kline_day表)
├── 策略执行 (多线程并行处理)
├── 信号存储 (stock_signals表)
└── 飞书通知 (FeishuNotifier)
```

## 功能特点

- **自动调度**: 每个交易日17:00自动执行
- **多线程处理**: 使用线程池并行处理股票，提高效率
- **策略扩展**: 支持添加自定义策略
- **信号存储**: 自动存储到stock_signals表，支持去重
- **飞书通知**: 实时发送策略执行结果到飞书群
- **完整日志**: 详细的执行日志和性能统计

## 安装配置

### 1. 数据库表创建

首先需要创建stock_signals表：

```bash
psql -h 10.8.8.88 -p 6668 -U postgres -d xystock -f services/create_stock_signals_table.sql
```

### 2. 飞书配置

在`config/main.toml`中配置飞书机器人：

```toml
[feishu]
webhook_url = "https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-url"
secret = "your-secret-key"
enabled = true
```

### 3. 策略配置

系统包含双通道斐波那契策略：
- **双通道斐波那契策略**: 检测双通道突破模式的技术分析策略

## 使用方法

### 自动执行

系统会在每个交易日17:00自动执行，无需手动干预。

### 手动测试

```bash
# 测试整个系统
python test_after_market.py

# 仅测试飞书连接
python -c "from test_after_market import test_feishu_connection; test_feishu_connection()"

# 手动执行策略选股
python processes/after_market_schedule.py
```

## 添加自定义策略

在`processes/after_market_schedule.py`的`execute_strategies`方法中添加新策略：

```python
def execute_strategies(self, stock_code: str, stock_name: str, kline_data: List[Dict]) -> List[Signal]:
    signals = []
    
    # 现有策略...
    
    # 添加新策略
    custom_signal = self._custom_strategy(stock_code, stock_name, kline_data)
    if custom_signal:
        signals.append(custom_signal)
    
    return signals

def _custom_strategy(self, stock_code: str, stock_name: str, kline_data: List[Dict]) -> Optional[Signal]:
    """自定义策略实现"""
    try:
        # 策略逻辑
        if condition_met:
            return Signal(
                stock_code=stock_code,
                stock_name=stock_name,
                strategy_name="自定义策略",
                signal_date=date.today(),
                signal_strength=0.8,
                signal_data={'custom_data': 'value'}
            )
        return None
    except Exception as e:
        self.logger.error(f"自定义策略执行失败 {stock_code}: {e}")
        return None
```

## 数据库表结构

### stock_signals表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| stock_code | VARCHAR(10) | 股票代码 (主键) |
| strategy_name | VARCHAR(100) | 策略名称 (主键) |
| signal_date | DATE | 信号日期 (主键) |
| stock_name | VARCHAR(50) | 股票名称 |
| signal_strength | DECIMAL(5,4) | 信号强度 (0-1) |
| signal_data | JSONB | 策略相关数据 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## 性能优化

- **多线程处理**: 默认4个工作线程，可根据CPU核数调整
- **批量操作**: 50只股票为一批，减少数据库压力
- **索引优化**: 针对常用查询创建了相应索引
- **连接池**: 复用数据库连接，提高效率

## 监控和日志

系统提供完整的执行日志：

```
2025-01-05 17:00:00 - after_market_scheduler - INFO - 开始执行盘后策略选股
2025-01-05 17:00:01 - after_market_scheduler - INFO - 获取到 4000 只股票
2025-01-05 17:00:02 - after_market_scheduler - INFO - 开始处理 4000 只股票，分为 80 批
2025-01-05 17:05:30 - after_market_scheduler - INFO - 盘后策略选股完成！
2025-01-05 17:05:30 - after_market_scheduler - INFO - 处理股票数量: 4000
2025-01-05 17:05:30 - after_market_scheduler - INFO - 产生信号数量: 156
2025-01-05 17:05:30 - after_market_scheduler - INFO - 执行耗时: 328.45秒
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置是否正确
   - 确认数据库服务是否正常运行

2. **飞书通知失败**
   - 检查webhook_url和secret配置
   - 确认网络连接正常
   - 运行测试脚本验证配置

3. **策略执行异常**
   - 查看日志文件定位具体错误
   - 检查股票数据是否完整

### 日志位置

系统日志会输出到控制台，建议配置日志文件：

```python
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/after_market.log'),
        logging.StreamHandler()
    ]
)
```

## 联系支持

如有问题，请联系QuantFM团队或查看项目文档。
