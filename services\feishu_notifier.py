#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书通知器

实现成交量异动信号的飞书富媒体通知。

作者: QuantFM Team
创建时间: 2025-07-13
"""

import requests
import json
import hashlib
import hmac
import base64
import time
from datetime import datetime
from typing import List, Dict, Any
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class FeishuNotifier:
    """飞书通知器 - 基于官方API文档实现"""

    def __init__(self, webhook_url: str, secret: str, logger):
        self.webhook_url = webhook_url
        self.secret = secret
        self.logger = logger

        # 通知频率控制
        self.last_notify_time = {}  # {stock_code: timestamp}
        self.notify_interval = 300  # 同一股票5分钟内只通知一次
        self.max_notifications_per_minute = 20  # 每分钟最大通知数
        self.notification_count = 0
        self.last_minute_reset = time.time()

        # 验证配置
        if not self.webhook_url or not self.secret:
            self.logger.warning("飞书webhook配置不完整，通知功能可能无法正常工作")
        else:
            self.logger.info("飞书通知器初始化完成")
    
    def send_strategy_signals(self, signals: List) -> bool:
        """
        发送策略信号到飞书

        Args:
            signals: 策略信号列表

        Returns:
            是否发送成功
        """
        if not self.webhook_url or not self.secret:
            self.logger.warning("飞书配置不完整，跳过通知")
            return False

        if not signals:
            return True

        try:
            # 构建汇总消息
            message = self._build_strategy_summary_message(signals)

            # 发送消息
            success = self._send_message(message)

            if success:
                self.logger.info(f"成功发送策略信号汇总到飞书，包含 {len(signals)} 个信号")

            return success

        except Exception as e:
            self.logger.error(f"发送策略信号到飞书失败: {e}")
            return False

    def send_signals(self, signals: List) -> bool:
        """
        发送信号到飞书
        
        Args:
            signals: 异动信号列表
            
        Returns:
            是否发送成功
        """
        if not self.webhook_url or not self.secret:
            self.logger.warning("飞书配置不完整，跳过通知")
            return False
        
        success_count = 0
        
        for signal in signals:
            try:
                # 频率控制检查
                if self._should_skip_notification(signal.stock_code):
                    continue
                
                # 每分钟通知数量控制
                if not self._check_rate_limit():
                    self.logger.warning("达到每分钟通知数量限制，跳过后续通知")
                    break
                
                # 发送单个信号
                if self._send_single_signal(signal):
                    success_count += 1
                    # 更新通知时间
                    self.last_notify_time[signal.stock_code] = time.time()
                    self.notification_count += 1
                
            except Exception as e:
                self.logger.error(f"发送信号 {signal.stock_code} 到飞书失败: {e}")
        
        if success_count > 0:
            self.logger.info(f"成功发送 {success_count}/{len(signals)} 个信号到飞书")
        
        return success_count > 0
    
    def _should_skip_notification(self, stock_code: str) -> bool:
        """检查是否应该跳过通知"""
        last_time = self.last_notify_time.get(stock_code, 0)
        return (time.time() - last_time) < self.notify_interval
    
    def _check_rate_limit(self) -> bool:
        """检查速率限制"""
        current_time = time.time()
        
        # 重置计数器（每分钟）
        if current_time - self.last_minute_reset > 60:
            self.notification_count = 0
            self.last_minute_reset = current_time
        
        return self.notification_count < self.max_notifications_per_minute
    
    def _send_message(self, message: Dict) -> bool:
        """发送消息到飞书"""
        try:
            # 生成签名
            timestamp = str(int(time.time()))
            sign = self._generate_sign(timestamp)

            # 构建请求数据 - 按照官方文档格式
            data = {
                "timestamp": timestamp,
                "sign": sign,
                "msg_type": "interactive",
                "card": message
            }

            # 发送请求
            response = requests.post(
                self.webhook_url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            if response.status_code == 200:
                response_data = response.json()
                if response_data.get("code") == 0:
                    self.logger.debug("成功发送消息到飞书")
                    return True
                else:
                    self.logger.error(f"飞书API返回错误: {response_data}")
                    return False
            else:
                self.logger.error(f"发送消息到飞书失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False

    def _send_single_signal(self, signal) -> bool:
        """发送单个信号"""
        try:
            # 构建消息内容
            message = self._build_message(signal)
            return self._send_message(message)

        except Exception as e:
            self.logger.error(f"发送单个信号失败: {e}")
            return False

    def _build_strategy_summary_message(self, signals: List) -> Dict:
        """构建策略信号汇总消息"""
        try:
            # 统计信号
            strategy_stats = {}
            top_signals = []

            for signal in signals:
                strategy_name = signal.strategy_name
                if strategy_name not in strategy_stats:
                    strategy_stats[strategy_name] = 0
                strategy_stats[strategy_name] += 1

                # 收集高强度信号
                if signal.signal_strength >= 0.7:
                    top_signals.append(signal)

            # 按信号强度排序，取前10个
            top_signals.sort(key=lambda x: x.signal_strength, reverse=True)
            top_signals = top_signals[:10]

            # 构建统计文本
            stats_text = ""
            for strategy, count in strategy_stats.items():
                stats_text += f"• **{strategy}**: {count}个信号\n"

            # 构建高强度信号列表
            top_signals_text = ""
            if top_signals:
                for i, signal in enumerate(top_signals, 1):
                    strength_emoji = "🔴" if signal.signal_strength >= 0.9 else "🟡" if signal.signal_strength >= 0.8 else "🟢"
                    # 添加更多信号详情
                    signal_detail = ""
                    if hasattr(signal, 'latest_close') and signal.latest_close > 0:
                        signal_detail += f" 价格:{signal.latest_close:.2f}"
                    if hasattr(signal, 'volume_ratio') and signal.volume_ratio > 0:
                        signal_detail += f" 量比:{signal.volume_ratio:.2f}"

                    top_signals_text += f"{i}. **{signal.stock_code}** {signal.stock_name} - {signal.strategy_name} {strength_emoji}{signal_detail}\n"
            else:
                top_signals_text = "暂无高强度信号"

            # 构建现代化卡片消息
            card = {
                "config": {
                    "wide_screen_mode": True
                },
                "header": {
                    "template": "blue",
                    "title": {
                        "content": f"📊 盘后策略选股报告 ({datetime.now().strftime('%Y-%m-%d')})",
                        "tag": "plain_text"
                    }
                },
                "elements": [
                    {
                        "tag": "div",
                        "text": {
                            "content": f"**📈 今日策略执行概况**\n\n共产生 **{len(signals)}** 个策略信号，涵盖 **{len(strategy_stats)}** 种策略类型。",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "div",
                        "text": {
                            "content": f"**📊 策略信号统计**\n\n{stats_text}",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "div",
                        "text": {
                            "content": f"**⭐ 高强度信号 (强度≥0.7)**\n\n{top_signals_text}",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "note",
                        "elements": [
                            {
                                "tag": "plain_text",
                                "content": f"QuantFM盘后策略系统 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                            }
                        ]
                    }
                ]
            }

            return card

        except Exception as e:
            self.logger.error(f"构建策略汇总消息失败: {e}")
            # 返回简单消息作为备选
            return self._build_simple_strategy_message(signals)

    def _build_simple_strategy_message(self, signals: List) -> Dict:
        """构建简单策略消息（备选方案）"""
        return {
            "config": {"wide_screen_mode": True},
            "header": {
                "template": "blue",
                "title": {"content": "盘后策略选股报告", "tag": "plain_text"}
            },
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "content": f"今日共产生 {len(signals)} 个策略信号",
                        "tag": "plain_text"
                    }
                }
            ]
        }
    
    def _build_message(self, signal) -> Dict:
        """构建富媒体消息"""
        try:
            # 信号类型标识
            signal_emoji = "[火]" if signal.signal_type == "opening_anomaly" else "[涨]"

            # 价格变动方向
            if signal.price_change > 0:
                price_emoji = "[涨]"
                price_color = "green"
            elif signal.price_change < 0:
                price_emoji = "[跌]"
                price_color = "red"
            else:
                price_emoji = "[平]"
                price_color = "grey"
            
            # 信号强度
            if signal.confidence >= 0.8:
                confidence_emoji = "🔴"  # 强信号
            elif signal.confidence >= 0.6:
                confidence_emoji = "🟡"  # 中等信号
            else:
                confidence_emoji = "🟢"  # 弱信号
            
            # 构建卡片消息
            card = {
                "config": {
                    "wide_screen_mode": True
                },
                "header": {
                    "template": "red" if signal.signal_type == "opening_anomaly" else "blue",
                    "title": {
                        "content": f"{signal_emoji} 成交量异动提醒",
                        "tag": "plain_text"
                    }
                },
                "elements": [
                    {
                        "tag": "div",
                        "text": {
                            "content": f"**股票代码**: {signal.stock_code}\n**异动类型**: {'[火] 开盘异动' if signal.signal_type == 'opening_anomaly' else '[涨] 常规异动'}\n**成交量比值**: {signal.volume_ratio:.2f}倍\n**价格变动**: {price_emoji} {signal.price_change*100:+.2f}%\n**当前价格**: ¥{signal.current_price:.2f}\n**成交量**: {signal.volume:,}手\n**信号强度**: {confidence_emoji} {signal.confidence:.1%}\n**时间**: {signal.timestamp.strftime('%H:%M:%S')}",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "div",
                        "fields": [
                            {
                                "is_short": True,
                                "text": {
                                    "content": f"**异动级别**\n{self._get_anomaly_level(signal)}",
                                    "tag": "lark_md"
                                }
                            },
                            {
                                "is_short": True,
                                "text": {
                                    "content": f"**建议操作**\n{self._get_action_advice(signal)}",
                                    "tag": "lark_md"
                                }
                            }
                        ]
                    },
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "note",
                        "elements": [
                            {
                                "tag": "plain_text",
                                "content": f"QuantFM实时监控 | {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                            }
                        ]
                    }
                ]
            }
            
            return card
            
        except Exception as e:
            self.logger.error(f"构建消息失败: {e}")
            # 返回简单消息作为备选
            return self._build_simple_message(signal)
    
    def _build_simple_message(self, signal) -> Dict:
        """构建简单消息（备选方案）"""
        return {
            "config": {"wide_screen_mode": True},
            "header": {
                "template": "blue",
                "title": {"content": "成交量异动", "tag": "plain_text"}
            },
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "content": f"{signal.stock_code} 成交量异动 {signal.volume_ratio:.2f}倍",
                        "tag": "plain_text"
                    }
                }
            ]
        }
    
    def _get_anomaly_level(self, signal) -> str:
        """获取异动级别"""
        if signal.volume_ratio >= 3.0:
            return "🔴 极强异动"
        elif signal.volume_ratio >= 2.5:
            return "🟠 强异动"
        elif signal.volume_ratio >= 2.0:
            return "🟡 中等异动"
        else:
            return "🟢 轻微异动"
    
    def _get_action_advice(self, signal) -> str:
        """获取操作建议"""
        if signal.signal_type == "opening_anomaly":
            if signal.price_change > 0:
                return "[火箭] 关注突破"
            else:
                return "[警告] 注意风险"
        else:
            if signal.volume_ratio >= 2.0:
                return "[眼睛] 密切关注"
            else:
                return "[图表] 持续观察"
    
    def send_volume_surge_alert(self, signal_data: Dict[str, Any]):
        """
        发送成交量激增信号通知

        Args:
            signal_data: 信号数据字典
        """
        try:
            if not self.enabled:
                return

            # 构造消息内容
            stock_code = signal_data.get('stock_code', '')
            signal_type = signal_data.get('signal_type', '')
            surge_ratio = signal_data.get('surge_ratio', 0)
            current_volume = signal_data.get('current_volume', 0)
            historical_avg_volume = signal_data.get('historical_avg_volume', 0)
            confidence = signal_data.get('confidence', 0)
            continuous_count = signal_data.get('continuous_count', 0)
            timestamp = signal_data.get('timestamp')
            period_info = signal_data.get('period_info', '')

            # 确定信号类型和阈值
            if signal_type == 'OPENING':
                type_name = "开盘期激增"
                threshold = 100
                type_emoji = "🔥"
                template_color = "red"
            else:
                type_name = "盘中期激增"
                threshold = 10
                type_emoji = "⚡"
                template_color = "blue"

            # 计算激增级别
            surge_level = self._get_surge_level(surge_ratio, threshold)

            # 构造飞书卡片消息
            card_content = {
                "config": {
                    "wide_screen_mode": True
                },
                "header": {
                    "template": template_color,
                    "title": {
                        "content": f"{type_emoji} 成交量激增提醒",
                        "tag": "plain_text"
                    }
                },
                "elements": [
                    {
                        "tag": "div",
                        "text": {
                            "content": f"**股票代码**: {stock_code}\n**激增类型**: {type_name}\n**激增比值**: {surge_ratio:.2f}倍 (阈值: {threshold}倍)\n**当前成交量**: {current_volume:,.0f}\n**历史平均**: {historical_avg_volume:,.0f}\n**置信度**: {confidence:.1%}\n**连续次数**: {continuous_count}次\n**时间段**: {period_info}\n**时间**: {timestamp.strftime('%H:%M:%S') if timestamp else ''}",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "div",
                        "fields": [
                            {
                                "is_short": True,
                                "text": {
                                    "content": f"**激增级别**\n{surge_level}",
                                    "tag": "lark_md"
                                }
                            },
                            {
                                "is_short": True,
                                "text": {
                                    "content": f"**连续状态**\n{self._get_continuity_status(continuous_count)}",
                                    "tag": "lark_md"
                                }
                            }
                        ]
                    }
                ]
            }

            # 发送消息
            self._send_card_message(card_content)

        except Exception as e:
            self.logger.error(f"发送成交量激增信号通知失败: {e}")

    def _get_surge_level(self, surge_ratio: float, threshold: float) -> str:
        """获取激增级别"""
        ratio_multiple = surge_ratio / threshold

        if ratio_multiple >= 5.0:
            return "🔴 极强激增"
        elif ratio_multiple >= 3.0:
            return "🟠 强激增"
        elif ratio_multiple >= 2.0:
            return "🟡 中等激增"
        elif ratio_multiple >= 1.0:
            return "🟢 轻微激增"
        else:
            return "⚪ 未达阈值"

    def _get_continuity_status(self, continuous_count: int) -> str:
        """获取连续状态"""
        if continuous_count >= 5:
            return f"🔥 连续{continuous_count}次"
        elif continuous_count >= 3:
            return f"⚡ 连续{continuous_count}次"
        elif continuous_count >= 2:
            return f"🟡 连续{continuous_count}次"
        else:
            return f"🟢 第{continuous_count}次"

    def _generate_sign(self, timestamp: str) -> str:
        """生成飞书签名 - 按照官方文档实现"""
        try:
            # 按照官方文档：timestamp + "\n" + 密钥 当做签名字符串
            string_to_sign = f"{timestamp}\n{self.secret}"

            # 使用HmacSHA256算法计算签名，再进行Base64 encode
            hmac_code = hmac.new(
                self.secret.encode("utf-8"),
                string_to_sign.encode("utf-8"),
                digestmod=hashlib.sha256
            ).digest()

            sign = base64.b64encode(hmac_code).decode('utf-8')
            return sign

        except Exception as e:
            self.logger.error(f"生成签名失败: {e}")
            return ""
    
    def test_connection(self) -> bool:
        """测试飞书连接"""
        try:
            # 发送测试消息 - 使用简单文本消息
            timestamp = str(int(time.time()))
            sign = self._generate_sign(timestamp)

            data = {
                "timestamp": timestamp,
                "sign": sign,
                "msg_type": "text",
                "content": {
                    "text": "QuantFM盘后策略系统连接测试 ✅"
                }
            }

            response = requests.post(
                self.webhook_url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            if response.status_code == 200:
                response_data = response.json()
                if response_data.get("code") == 0:
                    self.logger.info("飞书连接测试成功")
                    return True
                else:
                    self.logger.error(f"飞书连接测试失败: {response_data}")
                    return False
            else:
                self.logger.error(f"飞书连接测试失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            self.logger.error(f"飞书连接测试异常: {e}")
            return False
