#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查策略配置

检查数据库中的策略配置数据。

作者: QuantFM Team
创建时间: 2025-08-08
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import get_db_manager
from utils.logger import get_logger


def check_strategy_config():
    """检查策略配置"""
    logger = get_logger("CheckStrategyConfig")
    
    try:
        db_manager = get_db_manager()
        
        # 查询策略配置
        sql = "SELECT strategy_name, config_data, is_enabled FROM strategy_configs"
        results = db_manager.fetch_all(sql)
        
        print("策略配置检查结果:")
        print("=" * 60)
        
        if not results:
            print("❌ 没有找到任何策略配置")
            return False
        
        for row in results:
            strategy_name, config_data, is_enabled = row
            print(f"\n策略名称: {strategy_name}")
            print(f"是否启用: {is_enabled}")
            print(f"配置数据类型: {type(config_data)}")
            print(f"配置数据长度: {len(str(config_data)) if config_data else 0}")
            
            if config_data:
                print(f"配置数据前100字符: {str(config_data)[:100]}...")
            else:
                print("配置数据: 空")
        
        # 查询信号数据
        print("\n" + "=" * 60)
        print("信号数据检查:")
        
        sql_signals = "SELECT COUNT(*) FROM strategy_signals"
        count_result = db_manager.fetch_one(sql_signals)
        print(f"通用信号表记录数: {count_result[0] if count_result else 0}")
        
        sql_dual = "SELECT COUNT(*) FROM dual_channel_fibonacci_signals"
        dual_count = db_manager.fetch_one(sql_dual)
        print(f"双通道专用表记录数: {dual_count[0] if dual_count else 0}")
        
        # 查询最新信号
        sql_latest = """
            SELECT stock_code, strategy_name, signal_strength, signal_time 
            FROM strategy_signals 
            ORDER BY signal_time DESC 
            LIMIT 5
        """
        latest_signals = db_manager.fetch_all(sql_latest)
        
        print(f"\n最新5个信号:")
        for signal in latest_signals:
            stock_code, strategy_name, signal_strength, signal_time = signal
            print(f"  {stock_code} - {strategy_name} - 强度:{signal_strength} - 时间:{signal_time}")
        
        return True
        
    except Exception as e:
        logger.error(f"检查策略配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    check_strategy_config()
