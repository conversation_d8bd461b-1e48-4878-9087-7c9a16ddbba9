# Tick数据价格变化字段(change)使用指南

## 概述

本文档介绍了在`stock_tick_data`表中新增的`change`字段的功能、实现原理和使用方法。

## 功能说明

### change字段定义
- **字段名**: `change`
- **数据类型**: `REAL` (浮点数)
- **默认值**: `0.0`
- **含义**: 当前tick的成交价与上一次tick成交价的差值

### 计算逻辑
```
change = 当前价格 - 上一次价格
```

- 对于每只股票的第一条tick数据，`change = 0.0`
- 后续每条tick数据的change值为当前价格减去该股票上一条tick的价格
- 价格变化以元为单位，保留小数点后3位精度

## 实现架构

### 数据流程
1. **数据获取**: `MarketDataFetcher._fetch_realtime_quotes()`
2. **价格计算**: `MarketDataFetcher._calculate_price_change()`
3. **数据存储**: 保存到`stock_tick_data`表

### 内存管理
- 使用`last_price_data`字典维护每只股票的最后价格
- 在每日开盘时自动清空历史价格记录
- 线程安全的价格记录更新

### 数据库集成
- 自动包含在所有tick数据写入操作中
- 支持数据冲突时的更新操作
- 创建了专门的索引优化查询性能

## 使用示例

### 1. 查询价格变化最大的股票
```sql
SELECT 
    stock_code,
    trade_time,
    price,
    change,
    ABS(change) as abs_change
FROM stock_tick_data 
WHERE DATE(trade_time) = CURRENT_DATE
    AND change != 0
ORDER BY ABS(change) DESC
LIMIT 10;
```

### 2. 分析某只股票的价格波动
```sql
SELECT 
    trade_time,
    price,
    change,
    LAG(price) OVER (ORDER BY trade_time) as prev_price,
    CASE 
        WHEN change > 0 THEN '上涨'
        WHEN change < 0 THEN '下跌'
        ELSE '无变化'
    END as direction
FROM stock_tick_data 
WHERE stock_code = '000001'
    AND DATE(trade_time) = CURRENT_DATE
ORDER BY trade_time;
```

### 3. 统计价格变化分布
```sql
SELECT 
    stock_code,
    COUNT(*) as tick_count,
    COUNT(CASE WHEN change > 0 THEN 1 END) as up_ticks,
    COUNT(CASE WHEN change < 0 THEN 1 END) as down_ticks,
    COUNT(CASE WHEN change = 0 THEN 1 END) as flat_ticks,
    AVG(change) as avg_change,
    MAX(change) as max_increase,
    MIN(change) as max_decrease
FROM stock_tick_data 
WHERE DATE(trade_time) = CURRENT_DATE
GROUP BY stock_code
ORDER BY ABS(avg_change) DESC;
```

### 4. 识别异常价格跳动
```sql
SELECT 
    stock_code,
    trade_time,
    price,
    change,
    ABS(change) / LAG(price) OVER (PARTITION BY stock_code ORDER BY trade_time) * 100 as change_pct
FROM stock_tick_data 
WHERE DATE(trade_time) = CURRENT_DATE
    AND ABS(change) > 0.1  -- 价格变化超过1毛钱
ORDER BY ABS(change) DESC;
```

## 性能优化

### 索引设计
```sql
-- 价格变化查询优化
CREATE INDEX idx_tick_price_change 
    ON stock_tick_data (stock_code, change DESC, trade_time DESC)
    WHERE change != 0;

-- 显著价格变化查询优化
CREATE INDEX idx_tick_significant_change 
    ON stock_tick_data (stock_code, trade_time DESC)
    WHERE ABS(change) > 0.01;
```

### 查询建议
1. 使用WHERE条件过滤`change != 0`以提高查询效率
2. 结合时间范围查询避免全表扫描
3. 利用分区索引进行股票代码过滤

## 应用场景

### 1. 实时监控
- 监控价格异常波动
- 识别快速拉升或下跌的股票
- 实时计算价格变化统计

### 2. 量化分析
- 价格跳动频率分析
- 微观结构研究
- 高频交易策略开发

### 3. 风险管理
- 异常价格变动预警
- 市场冲击成本分析
- 流动性风险评估

## 注意事项

### 数据一致性
- change字段依赖于tick数据的时间顺序
- 系统重启后第一条数据的change值为0
- 跨日数据不计算价格变化

### 精度考虑
- 价格变化精度受原始价格数据精度限制
- 建议在分析时考虑最小价格变动单位
- 对于低价股票，小幅变化可能更有意义

### 性能影响
- 内存中维护价格记录，占用少量内存
- 计算开销很小，不影响数据获取性能
- 数据库存储增加约4字节/记录

## 测试验证

运行测试脚本验证功能：
```bash
python tests/test_price_change_calculation.py
```

测试内容包括：
- 基础价格变化计算
- 边界情况处理
- 数据库集成验证
- 性能测试

## 维护说明

### 日常维护
- 系统会自动管理价格记录的生命周期
- 无需手动清理或维护
- 建议定期检查索引性能

### 故障排查
- 检查`last_price_data`字典状态
- 验证数据库change字段值
- 查看相关日志信息

## 版本历史

- **v1.0** (2025-08-17): 初始实现，支持基础价格变化计算
- 后续版本将根据使用反馈进行优化改进
