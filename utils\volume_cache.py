#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高效成交量缓存系统

实现多级缓存机制，提升成交量比值计算效率。

作者: QuantFM Team
创建时间: 2025-07-13
"""

import threading
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from collections import defaultdict, deque
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class VolumeCache:
    """高效成交量缓存系统"""
    
    def __init__(self, db_manager, logger):
        self.db_manager = db_manager
        self.logger = logger
        
        # 缓存数据
        self.today_klines = defaultdict(dict)  # {stock_code: {bucket_time: kline_data}}
        self.historical_avg_volume = {}  # {stock_code: avg_volume}
        self.recent_buckets_avg = defaultdict(deque)  # {stock_code: deque([avg1, avg2, ...])}
        
        # 缓存配置
        self.recent_buckets_count = 6  # 缓存前6个5分钟桶的平均值
        self.historical_days = 20  # 历史统计天数
        
        # 线程锁
        self._cache_lock = threading.RLock()
        
    def initialize(self, stock_list: List[str]):
        """初始化缓存"""
        self.logger.info("初始化成交量缓存...")
        
        try:
            # 加载历史平均成交量
            self._load_historical_avg_volume(stock_list)
            
            # 加载当天已有的K线数据
            self._load_today_klines(stock_list)
            
            # 计算前N个桶的平均值
            self._calculate_recent_buckets_avg()
            
            self.logger.info(f"缓存初始化完成，覆盖 {len(stock_list)} 只股票")
            
        except Exception as e:
            self.logger.error(f"初始化缓存失败: {e}")
    
    def _load_historical_avg_volume(self, stock_list: List[str]):
        """加载历史平均成交量"""
        try:
            # 分批查询，避免SQL语句过长
            batch_size = 500
            
            for i in range(0, len(stock_list), batch_size):
                batch_stocks = stock_list[i:i + batch_size]
                
                query = """
                    SELECT stock_code, AVG(volume) as avg_volume
                    FROM stock_kline_5min 
                    WHERE trade_time >= CURRENT_DATE - INTERVAL '%s days'
                      AND stock_code = ANY(%s)
                    GROUP BY stock_code
                """
                
                result = self.db_manager.execute_query(query, (self.historical_days, batch_stocks))

                if result:
                    with self._cache_lock:
                        for row in result:
                            stock_code, avg_volume = row
                            self.historical_avg_volume[stock_code] = float(avg_volume) if avg_volume else 0
            
            self.logger.info(f"加载了 {len(self.historical_avg_volume)} 只股票的历史平均成交量")
            
        except Exception as e:
            self.logger.error(f"加载历史平均成交量失败: {e}")
    
    def _load_today_klines(self, stock_list: List[str]):
        """加载当天已有的K线数据"""
        try:
            today = datetime.now().date()
            
            # 分批查询
            batch_size = 500
            
            for i in range(0, len(stock_list), batch_size):
                batch_stocks = stock_list[i:i + batch_size]
                
                query = """
                    SELECT trade_time, stock_code, open, high, low, close, volume, amount
                    FROM stock_kline_5min 
                    WHERE trade_time >= %s 
                      AND trade_time < %s + INTERVAL '1 day'
                      AND stock_code = ANY(%s)
                    ORDER BY trade_time
                """
                
                result = self.db_manager.execute_query(query, (today, today, batch_stocks))

                if result:
                    with self._cache_lock:
                        for row in result:
                            trade_time, stock_code, open_price, high, low, close, volume, amount = row

                            kline_data = {
                                'trade_time': trade_time,
                                'open': float(open_price),
                                'high': float(high),
                                'low': float(low),
                                'close': float(close),
                                'volume': int(volume),
                                'amount': float(amount)
                            }

                            self.today_klines[stock_code][trade_time] = kline_data
            
            total_klines = sum(len(klines) for klines in self.today_klines.values())
            self.logger.info(f"加载了 {total_klines} 条当天K线数据")
            
        except Exception as e:
            self.logger.error(f"加载当天K线数据失败: {e}")
    
    def _calculate_recent_buckets_avg(self):
        """计算前N个桶的平均值"""
        try:
            with self._cache_lock:
                for stock_code, klines in self.today_klines.items():
                    if not klines:
                        continue
                    
                    # 按时间排序
                    sorted_times = sorted(klines.keys())
                    
                    # 取最近的N个桶
                    recent_times = sorted_times[-self.recent_buckets_count:]
                    
                    # 计算平均成交量
                    recent_volumes = [klines[t]['volume'] for t in recent_times]
                    
                    # 使用deque存储，自动维护固定长度
                    self.recent_buckets_avg[stock_code] = deque(recent_volumes, maxlen=self.recent_buckets_count)
            
            self.logger.debug("计算前N个桶的平均值完成")
            
        except Exception as e:
            self.logger.error(f"计算前N个桶平均值失败: {e}")
    
    def update_klines(self, klines: Dict[str, Dict]):
        """更新K线缓存"""
        try:
            with self._cache_lock:
                for stock_code, kline in klines.items():
                    trade_time = kline['trade_time']
                    
                    # 更新当天K线缓存
                    self.today_klines[stock_code][trade_time] = kline
                    
                    # 更新前N个桶的平均值
                    self.recent_buckets_avg[stock_code].append(kline['volume'])
            
        except Exception as e:
            self.logger.error(f"更新K线缓存失败: {e}")
    
    # 注意：get_volume_ratio 方法已移除，该功能现在由 factors/volume_data_manager.py 接管
    # 保留此注释以说明功能迁移
    
    def _get_recent_buckets_avg(self, stock_code: str) -> float:
        """获取前N个桶的平均成交量"""
        try:
            recent_volumes = self.recent_buckets_avg.get(stock_code, deque())
            
            if len(recent_volumes) >= 3:  # 至少需要3个数据点
                return sum(recent_volumes) / len(recent_volumes)
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"获取前N个桶平均值失败: {e}")
            return 0.0
    
    def get_historical_avg_volume(self, stock_code: str) -> float:
        """获取历史平均成交量"""
        with self._cache_lock:
            return self.historical_avg_volume.get(stock_code, 0.0)
    
    def get_today_klines(self, stock_code: str) -> Dict:
        """获取当天K线数据"""
        with self._cache_lock:
            return self.today_klines.get(stock_code, {}).copy()
    
    def get_cache_stats(self) -> Dict:
        """获取缓存统计信息"""
        with self._cache_lock:
            total_klines = sum(len(klines) for klines in self.today_klines.values())
            
            return {
                'historical_stocks': len(self.historical_avg_volume),
                'today_klines': total_klines,
                'recent_buckets_stocks': len(self.recent_buckets_avg),
                'cache_size_mb': self._estimate_cache_size()
            }
    
    def _estimate_cache_size(self) -> float:
        """估算缓存大小（MB）"""
        try:
            # 粗略估算
            historical_size = len(self.historical_avg_volume) * 8  # 每个float 8字节
            klines_size = sum(len(klines) for klines in self.today_klines.values()) * 64  # 每个K线约64字节
            recent_size = sum(len(volumes) for volumes in self.recent_buckets_avg.values()) * 8
            
            total_bytes = historical_size + klines_size + recent_size
            return total_bytes / (1024 * 1024)  # 转换为MB
            
        except:
            return 0.0
