"""
通道指标计算模块

提供双通道计算功能，支持可配置的通道参数。
主要功能：
- 双通道计算（基于EMA上下轨）
- 通道上下轨计算逻辑
- 可配置的通道参数
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from .talib_wrapper import calculate_ema_series

logger = logging.getLogger(__name__)


def calculate_dual_channels(df: pd.DataFrame, 
                           channel1_params: Dict[str, int],
                           channel2_params: Dict[str, int],
                           price_column: str = 'close') -> pd.DataFrame:
    """
    计算双通道指标（优化版本）
    
    Args:
        df: 包含价格数据的DataFrame
        channel1_params: 通道1参数，格式为 {'upper': 144, 'lower': 169}
        channel2_params: 通道2参数，格式为 {'upper': 576, 'lower': 676}
        price_column: 用于计算通道的价格列名，默认为'close'
        
    Returns:
        包含通道指标的DataFrame副本
        
    Raises:
        ValueError: 当输入参数无效时
        KeyError: 当指定的价格列不存在时
    """
    if df.empty:
        raise ValueError("输入DataFrame不能为空")
        
    if price_column not in df.columns:
        raise KeyError(f"价格列 '{price_column}' 不存在于DataFrame中")
        
    # 验证通道参数
    _validate_channel_params(channel1_params, "channel1_params")
    _validate_channel_params(channel2_params, "channel2_params")
    
    try:
        # 收集所有需要计算的EMA周期
        all_periods = [
            channel1_params['upper'], channel1_params['lower'],
            channel2_params['upper'], channel2_params['lower']
        ]

        # 检查哪些EMA已经存在，只计算缺失的
        existing_emas = [p for p in all_periods if f"ema_{p}" in df.columns]
        missing_emas = [p for p in all_periods if f"ema_{p}" not in df.columns]

        if missing_emas:
            # 只计算缺失的EMA，保留所有现有列
            result_df = calculate_ema_series(df, missing_emas, price_column)
        else:
            # 所有EMA都已存在，直接使用原DataFrame
            result_df = df.copy()
        
        # 性能优化：使用numpy数组进行向量化计算
        channel1_upper_values = result_df[f"ema_{channel1_params['upper']}"].values
        channel1_lower_values = result_df[f"ema_{channel1_params['lower']}"].values
        channel2_upper_values = result_df[f"ema_{channel2_params['upper']}"].values
        channel2_lower_values = result_df[f"ema_{channel2_params['lower']}"].values
        
        # 向量化计算通道指标
        channel1_width = channel1_upper_values - channel1_lower_values
        channel2_width = channel2_upper_values - channel2_lower_values
        channel1_middle = (channel1_upper_values + channel1_lower_values) * 0.5
        channel2_middle = (channel2_upper_values + channel2_lower_values) * 0.5
        
        # 批量赋值，减少DataFrame操作次数
        channel_data = {
            'channel1_upper': channel1_upper_values,
            'channel1_lower': channel1_lower_values,
            'channel2_upper': channel2_upper_values,
            'channel2_lower': channel2_lower_values,
            'channel1_width': channel1_width,
            'channel2_width': channel2_width,
            'channel1_middle': channel1_middle,
            'channel2_middle': channel2_middle
        }
        
        for col_name, values in channel_data.items():
            result_df[col_name] = values
        
        logger.debug(f"成功计算双通道指标，通道1: {channel1_params}, 通道2: {channel2_params}")
        return result_df
        
    except Exception as e:
        logger.error(f"计算双通道指标失败: {str(e)}")
        raise


def calculate_single_channel(df: pd.DataFrame,
                           upper_period: int,
                           lower_period: int,
                           price_column: str = 'close',
                           channel_name: str = 'channel') -> pd.DataFrame:
    """
    计算单个通道指标
    
    Args:
        df: 包含价格数据的DataFrame
        upper_period: 上轨EMA周期
        lower_period: 下轨EMA周期
        price_column: 用于计算通道的价格列名
        channel_name: 通道名称前缀
        
    Returns:
        包含通道指标的DataFrame副本
    """
    if df.empty:
        raise ValueError("输入DataFrame不能为空")
        
    if upper_period <= 0 or lower_period <= 0:
        raise ValueError("EMA周期必须大于0")
        
    try:
        # 计算EMA
        periods = [upper_period, lower_period]
        result_df = calculate_ema_series(df, periods, price_column)
        
        # 添加通道列
        result_df[f'{channel_name}_upper'] = result_df[f'ema_{upper_period}']
        result_df[f'{channel_name}_lower'] = result_df[f'ema_{lower_period}']
        result_df[f'{channel_name}_width'] = result_df[f'{channel_name}_upper'] - result_df[f'{channel_name}_lower']
        result_df[f'{channel_name}_middle'] = (result_df[f'{channel_name}_upper'] + result_df[f'{channel_name}_lower']) / 2
        
        return result_df
        
    except Exception as e:
        logger.error(f"计算单通道指标失败: {str(e)}")
        raise


def calculate_channel_ratios(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算通道间的比率关系
    
    Args:
        df: 包含双通道数据的DataFrame
        
    Returns:
        包含通道比率的DataFrame副本
        
    Raises:
        ValueError: 当缺少必需的通道列时
    """
    required_columns = ['channel1_upper', 'channel1_lower', 'channel2_upper', 'channel2_lower']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        raise ValueError(f"缺少必需的通道列: {missing_columns}")
        
    result_df = df.copy()
    
    try:
        # 计算通道宽度比率
        result_df['channel_width_ratio'] = result_df['channel1_width'] / result_df['channel2_width']
        
        # 计算通道位置关系
        result_df['channel1_upper_vs_channel2_lower'] = result_df['channel1_upper'] - result_df['channel2_lower']
        result_df['channel1_lower_vs_channel2_upper'] = result_df['channel1_lower'] - result_df['channel2_upper']
        
        # 计算通道重叠度
        overlap_upper = np.minimum(result_df['channel1_upper'], result_df['channel2_upper'])
        overlap_lower = np.maximum(result_df['channel1_lower'], result_df['channel2_lower'])
        result_df['channel_overlap'] = np.maximum(0, overlap_upper - overlap_lower)
        
        return result_df
        
    except Exception as e:
        logger.error(f"计算通道比率失败: {str(e)}")
        raise


def get_channel_boundaries(df: pd.DataFrame, 
                          index: int,
                          channel_name: str = 'channel1') -> Dict[str, float]:
    """
    获取指定索引处的通道边界值
    
    Args:
        df: 包含通道数据的DataFrame
        index: 数据索引
        channel_name: 通道名称（'channel1' 或 'channel2'）
        
    Returns:
        包含通道边界值的字典
        
    Raises:
        IndexError: 当索引超出范围时
        ValueError: 当通道数据不存在时
    """
    if index < 0 or index >= len(df):
        raise IndexError(f"索引 {index} 超出DataFrame范围 [0, {len(df)-1}]")
        
    required_columns = [f'{channel_name}_upper', f'{channel_name}_lower']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        raise ValueError(f"缺少通道列: {missing_columns}")
        
    try:
        row = df.iloc[index]
        
        return {
            'upper': float(row[f'{channel_name}_upper']),
            'lower': float(row[f'{channel_name}_lower']),
            'middle': float(row[f'{channel_name}_middle']) if f'{channel_name}_middle' in df.columns else 
                     (float(row[f'{channel_name}_upper']) + float(row[f'{channel_name}_lower'])) / 2,
            'width': float(row[f'{channel_name}_width']) if f'{channel_name}_width' in df.columns else
                    float(row[f'{channel_name}_upper']) - float(row[f'{channel_name}_lower'])
        }
        
    except Exception as e:
        logger.error(f"获取通道边界值失败: {str(e)}")
        raise


def find_channel_crossovers(df: pd.DataFrame, 
                           price_column: str = 'close',
                           channel_name: str = 'channel1') -> Dict[str, List[int]]:
    """
    寻找价格与通道的交叉点
    
    Args:
        df: 包含价格和通道数据的DataFrame
        price_column: 价格列名
        channel_name: 通道名称
        
    Returns:
        包含交叉点索引的字典，格式为:
        {
            'upper_breakouts': [索引列表],  # 突破上轨的点
            'upper_breakdowns': [索引列表], # 跌破上轨的点
            'lower_breakouts': [索引列表],  # 突破下轨的点
            'lower_breakdowns': [索引列表] # 跌破下轨的点
        }
    """
    if price_column not in df.columns:
        raise KeyError(f"价格列 '{price_column}' 不存在")
        
    required_columns = [f'{channel_name}_upper', f'{channel_name}_lower']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        raise ValueError(f"缺少通道列: {missing_columns}")
        
    try:
        prices = df[price_column].values
        upper_channel = df[f'{channel_name}_upper'].values
        lower_channel = df[f'{channel_name}_lower'].values
        
        # 计算价格相对于通道的位置
        above_upper = prices > upper_channel
        below_lower = prices < lower_channel
        
        # 寻找交叉点（状态变化的点）
        upper_breakouts = []
        upper_breakdowns = []
        lower_breakouts = []
        lower_breakdowns = []
        
        for i in range(1, len(prices)):
            # 检查上轨交叉
            if not above_upper[i-1] and above_upper[i]:  # 突破上轨
                upper_breakouts.append(i)
            elif above_upper[i-1] and not above_upper[i]:  # 跌破上轨
                upper_breakdowns.append(i)
                
            # 检查下轨交叉
            if below_lower[i-1] and not below_lower[i]:  # 突破下轨（从下方向上）
                lower_breakouts.append(i)
            elif not below_lower[i-1] and below_lower[i]:  # 跌破下轨（从上方向下）
                lower_breakdowns.append(i)
                
        return {
            'upper_breakouts': upper_breakouts,
            'upper_breakdowns': upper_breakdowns,
            'lower_breakouts': lower_breakouts,
            'lower_breakdowns': lower_breakdowns
        }
        
    except Exception as e:
        logger.error(f"寻找通道交叉点失败: {str(e)}")
        raise


def _validate_channel_params(params: Dict[str, int], param_name: str) -> None:
    """
    验证通道参数的有效性
    
    Args:
        params: 通道参数字典
        param_name: 参数名称（用于错误信息）
        
    Raises:
        ValueError: 当参数无效时
    """
    if not isinstance(params, dict):
        raise ValueError(f"{param_name} 必须是字典类型")
        
    required_keys = ['upper', 'lower']
    missing_keys = [key for key in required_keys if key not in params]
    
    if missing_keys:
        raise ValueError(f"{param_name} 缺少必需的键: {missing_keys}")
        
    for key in required_keys:
        if not isinstance(params[key], int) or params[key] <= 0:
            raise ValueError(f"{param_name}['{key}'] 必须是正整数")
            
    if params['upper'] >= params['lower']:
        raise ValueError(f"{param_name} 的上轨周期必须小于下轨周期（上轨EMA更敏感）")


def validate_channel_data(df: pd.DataFrame, 
                         channel_names: List[str] = ['channel1', 'channel2']) -> bool:
    """
    验证DataFrame是否包含有效的通道数据
    
    Args:
        df: 待验证的DataFrame
        channel_names: 要验证的通道名称列表
        
    Returns:
        验证是否通过
    """
    if df.empty:
        logger.error("DataFrame为空")
        return False
        
    for channel_name in channel_names:
        required_columns = [f'{channel_name}_upper', f'{channel_name}_lower']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            logger.error(f"通道 {channel_name} 缺少必需的列: {missing_columns}")
            return False
            
        # 检查数据有效性
        for col in required_columns:
            if df[col].isna().all():
                logger.error(f"通道列 {col} 全部为NaN")
                return False
                
    return True