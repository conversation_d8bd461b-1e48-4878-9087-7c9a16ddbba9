#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成交量激增因子系统测试套件

测试成交量激增因子系统的各个组件功能。

作者: QuantFM Team
创建时间: 2025-08-08
"""

import unittest
import sys
import os
from datetime import datetime, timedelta, time as dt_time
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from factors.volume_surge_factor import VolumeSurgeFactorEngine, VolumeSurgeSignal
from factors.signal_continuity_manager import SignalContinuityManager, SignalState, SignalPeriodType
from factors.volume_data_manager import VolumeDataManager


class TestVolumeSurgeFactorEngine(unittest.TestCase):
    """测试成交量激增因子引擎"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = {
            "opening_ratio_threshold": 100,
            "intraday_ratio_threshold": 10,
            "historical_days": 10,
            "min_volume_threshold": 1000,
            "enable_cache": True,
            "cache_ttl": 3600
        }
        
        # Mock数据库管理器
        with patch('factors.volume_surge_factor.get_db_manager'):
            self.engine = VolumeSurgeFactorEngine(self.config)
    
    def test_opening_surge_detection(self):
        """测试开盘期激增检测"""
        # 准备测试数据
        stock_data = {
            "000001.SZ": 1000000,  # 100万成交量
            "000002.SZ": 500000    # 50万成交量
        }
        
        historical_data = {
            "000001.SZ": 10000,    # 历史平均1万，比值100倍
            "000002.SZ": 100000    # 历史平均10万，比值5倍
        }
        
        # 执行检测
        signals = self.engine.opening_surge_detection(stock_data, historical_data)
        
        # 验证结果
        self.assertEqual(len(signals), 1)  # 只有000001.SZ满足100倍阈值
        signal = signals[0]
        self.assertEqual(signal.stock_code, "000001.SZ")
        self.assertEqual(signal.signal_type, "OPENING")
        self.assertEqual(signal.surge_ratio, 100.0)
        self.assertGreater(signal.confidence, 0)
    
    def test_intraday_surge_detection(self):
        """测试盘中期激增检测"""
        # 准备K线数据
        kline_data = {
            "000001.SZ": {"volume": 200000, "trade_time": datetime.now()},
            "000002.SZ": {"volume": 50000, "trade_time": datetime.now()}
        }
        
        historical_data = {
            "000001.SZ": 20000,    # 历史平均2万，比值10倍
            "000002.SZ": 10000     # 历史平均1万，比值5倍
        }
        
        # 执行检测
        signals = self.engine.intraday_surge_detection(kline_data, historical_data)
        
        # 验证结果
        self.assertEqual(len(signals), 1)  # 只有000001.SZ满足10倍阈值
        signal = signals[0]
        self.assertEqual(signal.stock_code, "000001.SZ")
        self.assertEqual(signal.signal_type, "INTRADAY")
        self.assertEqual(signal.surge_ratio, 10.0)
    
    def test_data_validation(self):
        """测试数据验证"""
        # 测试无效数据
        self.assertFalse(self.engine._validate_volume_data(-1000, "000001.SZ"))  # 负数
        self.assertFalse(self.engine._validate_volume_data(500, "000001.SZ"))    # 低于最小阈值
        self.assertFalse(self.engine._validate_volume_data("invalid", "000001.SZ"))  # 非数字
        
        # 测试有效数据
        self.assertTrue(self.engine._validate_volume_data(10000, "000001.SZ"))
    
    def test_confidence_calculation(self):
        """测试置信度计算"""
        # 开盘期阈值测试
        confidence = self.engine._calculate_confidence(200, 100)  # 2倍阈值
        self.assertGreater(confidence, 0.5)
        self.assertLessEqual(confidence, 1.0)
        
        # 盘中期阈值测试
        confidence = self.engine._calculate_confidence(20, 10)   # 2倍阈值
        self.assertGreater(confidence, 0.4)
        self.assertLessEqual(confidence, 0.9)


class TestSignalContinuityManager(unittest.TestCase):
    """测试信号连续性管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = {
            "enable_continuity_check": True,
            "max_signal_gap_seconds": 300,
            "reset_on_discontinuity": True
        }
        self.manager = SignalContinuityManager(self.config)
    
    def test_signal_continuity_check(self):
        """测试信号连续性检查"""
        stock_code = "000001.SZ"
        signal_type = "OPENING"
        timestamp = datetime.now()
        
        # 第一次信号应该被允许
        result = self.manager.check_signal_continuity(stock_code, signal_type, timestamp)
        self.assertTrue(result)
        
        # 同一周期内的重复信号应该被阻止
        result = self.manager.check_signal_continuity(stock_code, signal_type, timestamp)
        self.assertFalse(result)
    
    def test_period_type_detection(self):
        """测试周期类型检测"""
        # 开盘期
        opening_time = datetime.now().replace(hour=9, minute=35, second=0)
        period_type = self.manager._get_period_type(opening_time)
        self.assertEqual(period_type, SignalPeriodType.OPENING)
        
        # 盘中期
        intraday_time = datetime.now().replace(hour=10, minute=30, second=0)
        period_type = self.manager._get_period_type(intraday_time)
        self.assertEqual(period_type, SignalPeriodType.INTRADAY)
        
        # 非交易时间
        off_time = datetime.now().replace(hour=16, minute=0, second=0)
        period_type = self.manager._get_period_type(off_time)
        self.assertEqual(period_type, SignalPeriodType.OFF)
    
    def test_period_id_generation(self):
        """测试周期ID生成"""
        timestamp = datetime(2025, 8, 8, 9, 35, 0)
        
        # 开盘期周期ID
        period_id = self.manager._get_period_id(timestamp, "OPENING")
        self.assertEqual(period_id, "20250808_OPENING")
        
        # 盘中期周期ID
        period_id = self.manager._get_period_id(timestamp, "INTRADAY")
        self.assertEqual(period_id, "20250808_INTRADAY_0935")
    
    def test_consecutive_period_check(self):
        """测试连续周期检查"""
        # 开盘期到盘中期应该是连续的
        last_period = "20250808_OPENING"
        current_period = "20250808_INTRADAY_0945"
        result = self.manager._is_consecutive_period(last_period, current_period)
        self.assertTrue(result)
        
        # 盘中期内连续5分钟应该是连续的
        last_period = "20250808_INTRADAY_0945"
        current_period = "20250808_INTRADAY_0950"
        result = self.manager._is_consecutive_period(last_period, current_period)
        self.assertTrue(result)
        
        # 跳跃的周期不应该是连续的
        last_period = "20250808_INTRADAY_0945"
        current_period = "20250808_INTRADAY_0955"
        result = self.manager._is_consecutive_period(last_period, current_period)
        self.assertFalse(result)


class TestVolumeDataManager(unittest.TestCase):
    """测试成交量数据管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = {
            "enable_cache": True,
            "cache_ttl": 3600,
            "max_cache_size": 10000,
            "preload_historical_data": True
        }
        
        # Mock数据库管理器
        with patch('factors.volume_data_manager.get_db_manager'):
            self.data_manager = VolumeDataManager(self.config)
    
    @patch('factors.volume_data_manager.get_db_manager')
    def test_get_opening_historical_avg(self, mock_db_manager):
        """测试获取开盘期历史平均"""
        # Mock数据库查询结果
        mock_db_manager.return_value.fetch_all.return_value = [
            (50000.0,)  # 历史平均5万
        ]
        
        # 重新初始化以使用mock
        self.data_manager.db_manager = mock_db_manager.return_value
        
        result = self.data_manager.get_opening_historical_avg("000001.SZ", 10)
        self.assertEqual(result, 50000.0)
    
    @patch('factors.volume_data_manager.get_db_manager')
    def test_get_intraday_avg(self, mock_db_manager):
        """测试获取盘中期平均"""
        # Mock数据库查询结果
        mock_db_manager.return_value.fetch_all.return_value = [
            (30000.0,)  # 盘中期平均3万
        ]
        
        # 重新初始化以使用mock
        self.data_manager.db_manager = mock_db_manager.return_value
        
        result = self.data_manager.get_intraday_avg("000001.SZ")
        self.assertEqual(result, 30000.0)
    
    def test_cache_functionality(self):
        """测试缓存功能"""
        # 测试开盘期平均缓存
        cache_key = "opening_avg_000001.SZ_10"
        test_data = 50000.0

        self.data_manager._set_cache(cache_key, test_data)
        cached_data = self.data_manager._get_from_cache(cache_key)

        self.assertEqual(cached_data, test_data)

        # 测试盘中期平均缓存
        cache_key2 = "intraday_avg_000001.SZ_1030"
        test_data2 = 30000.0

        self.data_manager._set_cache(cache_key2, test_data2)
        cached_data2 = self.data_manager._get_from_cache(cache_key2)

        self.assertEqual(cached_data2, test_data2)

        # 测试实时成交量缓存
        cache_key3 = "current_volume_000001.SZ"
        test_data3 = 100000.0

        self.data_manager._set_cache(cache_key3, test_data3)
        cached_data3 = self.data_manager._get_from_cache(cache_key3)

        self.assertEqual(cached_data3, test_data3)
    
    def test_batch_get_current_volumes(self):
        """测试批量获取当前成交量"""
        with patch.object(self.data_manager, 'db_manager') as mock_db:
            # Mock数据库查询结果
            mock_db.fetch_all.return_value = [
                ("000001.SZ", 100000),
                ("000002.SZ", 50000)
            ]
            
            stock_codes = ["000001.SZ", "000002.SZ", "000003.SZ"]
            result = self.data_manager.batch_get_current_volumes(stock_codes)
            
            # 验证结果
            self.assertEqual(result["000001.SZ"], 100000.0)
            self.assertEqual(result["000002.SZ"], 50000.0)
            self.assertEqual(result["000003.SZ"], 0.0)  # 没有数据的股票应该返回0


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """设置集成测试环境"""
        self.factor_config = {
            "opening_ratio_threshold": 100,
            "intraday_ratio_threshold": 10,
            "historical_days": 10
        }
        
        self.continuity_config = {
            "enable_continuity_check": True,
            "max_signal_gap_seconds": 300,
            "reset_on_discontinuity": True
        }
        
        self.data_config = {
            "enable_cache": True,
            "cache_ttl": 3600,
            "preload_historical_data": False
        }
        
        # 创建组件实例
        with patch('factors.volume_surge_factor.get_db_manager'), \
             patch('factors.volume_data_manager.get_db_manager'):
            self.factor_engine = VolumeSurgeFactorEngine(self.factor_config)
            self.continuity_manager = SignalContinuityManager(self.continuity_config)
            self.data_manager = VolumeDataManager(self.data_config)
    
    def test_end_to_end_signal_processing(self):
        """测试端到端信号处理"""
        # 模拟开盘期激增信号
        stock_data = {"000001.SZ": 1000000}
        historical_data = {"000001.SZ": 10000}
        
        # 生成信号
        signals = self.factor_engine.opening_surge_detection(stock_data, historical_data)
        self.assertEqual(len(signals), 1)
        
        signal = signals[0]
        
        # 检查连续性
        is_allowed = self.continuity_manager.check_signal_continuity(
            signal.stock_code,
            signal.signal_type,
            signal.timestamp
        )
        self.assertTrue(is_allowed)
        
        # 获取连续次数
        continuous_count = self.continuity_manager.get_continuous_count(signal.stock_code)
        self.assertEqual(continuous_count, 1)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
