#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KDJ背离检测器

专门处理KDJ指标的背离分析，包括K线、D线和J线的综合分析。

作者: QuantFM Team
创建时间: 2025-08-11
"""

import numpy as np
from dataclasses import dataclass
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


@dataclass
class IndicatorResult:
    """指标分析结果"""
    patterns: List[Dict[str, Any]]      # 检测到的模式
    summary: Dict[str, Any]             # 汇总信息
    signals: List[Dict[str, Any]]       # 信号列表
    metadata: Dict[str, Any]            # 元数据


class KDJDivergenceDetector:
    """KDJ背离检测器
    
    核心功能:
    1. K线和D线作为主要信号，J线作为补充预警
    2. 金叉死叉时机检测
    3. 极值区域停滞处理（>80, <20）
    4. 快速反应能力（短期信号）
    5. 成交量确认（对KDJ特别重要）
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化检测器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # KDJ特定参数
        self.overbought_threshold = self.config.get('overbought_threshold', 80)
        self.oversold_threshold = self.config.get('oversold_threshold', 20)
        self.cross_confirmation_periods = self.config.get('cross_confirmation_periods', 2)
        self.j_line_sensitivity = self.config.get('j_line_sensitivity', 1.2)
        self.volume_weight = self.config.get('volume_weight', 0.4)  # KDJ对成交量更敏感
        
    def detect(self, kdj_data: Dict[str, np.ndarray], price_extremes: List[Any],
              volume: Optional[np.ndarray] = None) -> IndicatorResult:
        """检测KDJ背离模式
        
        Args:
            kdj_data: KDJ数据，包含k、d、j值
            price_extremes: 价格极值点
            volume: 成交量数据
            
        Returns:
            指标分析结果
        """
        try:
            k_line = kdj_data.get('k', np.array([]))
            d_line = kdj_data.get('d', np.array([]))
            j_line = kdj_data.get('j', np.array([]))
            
            if len(k_line) == 0 or len(d_line) == 0:
                logger.warning("KDJ数据不完整")
                return self._get_empty_result()
            
            patterns = []
            signals = []
            
            # 1. 分析K线背离
            k_patterns = self._analyze_k_line_divergence(k_line, price_extremes)
            patterns.extend(k_patterns)
            
            # 2. 分析D线背离
            d_patterns = self._analyze_d_line_divergence(d_line, price_extremes)
            patterns.extend(d_patterns)
            
            # 3. J线预警分析
            if len(j_line) > 0:
                j_warnings = self._analyze_j_line_warnings(j_line, price_extremes)
                patterns.extend(j_warnings)
            
            # 4. 检测金叉死叉
            cross_signals = self._detect_kd_cross(k_line, d_line)
            signals.extend(cross_signals)
            
            # 5. 极值区域分析
            extreme_zone_analysis = self._analyze_extreme_zones(k_line, d_line, j_line)
            
            # 6. 成交量确认
            if volume is not None:
                patterns = self._add_volume_confirmation(patterns, volume)
            
            # 生成汇总
            summary = self._generate_summary(patterns, signals, extreme_zone_analysis)
            
            # 构建元数据
            metadata = {
                'data_length': len(k_line),
                'extreme_zone_analysis': extreme_zone_analysis,
                'has_j_line': len(j_line) > 0,
                'has_volume_confirmation': volume is not None,
                'analysis_timestamp': np.datetime64('now').astype(str)
            }
            
            return IndicatorResult(
                patterns=patterns,
                summary=summary,
                signals=signals,
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"KDJ背离检测失败: {e}")
            return self._get_empty_result()
    
    def _analyze_k_line_divergence(self, k_line: np.ndarray, 
                                  price_extremes: List[Any]) -> List[Dict[str, Any]]:
        """分析K线背离"""
        patterns = []
        
        try:
            # 寻找K线的极值点
            k_peaks = self._find_local_extremes(k_line, 'peak')
            k_troughs = self._find_local_extremes(k_line, 'trough')
            
            # 与价格极值点匹配
            for price_extreme in price_extremes:
                if price_extreme.type == 'peak':
                    matching_k = self._find_matching_extreme(price_extreme, k_peaks, k_line)
                    if matching_k:
                        pattern = self._create_kdj_divergence_pattern(
                            'k_line_divergence', price_extreme, matching_k, k_line, 'k'
                        )
                        if pattern:
                            patterns.append(pattern)
                
                elif price_extreme.type == 'trough':
                    matching_k = self._find_matching_extreme(price_extreme, k_troughs, k_line)
                    if matching_k:
                        pattern = self._create_kdj_divergence_pattern(
                            'k_line_divergence', price_extreme, matching_k, k_line, 'k'
                        )
                        if pattern:
                            patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            logger.warning(f"K线背离分析失败: {e}")
            return []
    
    def _analyze_d_line_divergence(self, d_line: np.ndarray, 
                                  price_extremes: List[Any]) -> List[Dict[str, Any]]:
        """分析D线背离"""
        patterns = []
        
        try:
            # 寻找D线的极值点
            d_peaks = self._find_local_extremes(d_line, 'peak')
            d_troughs = self._find_local_extremes(d_line, 'trough')
            
            # 与价格极值点匹配
            for price_extreme in price_extremes:
                if price_extreme.type == 'peak':
                    matching_d = self._find_matching_extreme(price_extreme, d_peaks, d_line)
                    if matching_d:
                        pattern = self._create_kdj_divergence_pattern(
                            'd_line_divergence', price_extreme, matching_d, d_line, 'd'
                        )
                        if pattern:
                            patterns.append(pattern)
                
                elif price_extreme.type == 'trough':
                    matching_d = self._find_matching_extreme(price_extreme, d_troughs, d_line)
                    if matching_d:
                        pattern = self._create_kdj_divergence_pattern(
                            'd_line_divergence', price_extreme, matching_d, d_line, 'd'
                        )
                        if pattern:
                            patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            logger.warning(f"D线背离分析失败: {e}")
            return []
    
    def _analyze_j_line_warnings(self, j_line: np.ndarray, 
                                price_extremes: List[Any]) -> List[Dict[str, Any]]:
        """分析J线预警信号"""
        patterns = []
        
        try:
            # J线的极端值检测（可能超出0-100范围）
            j_extremes = self._find_j_line_extremes(j_line)
            
            # 与价格极值点匹配
            for price_extreme in price_extremes:
                for j_extreme in j_extremes:
                    time_diff = abs(j_extreme['index'] - price_extreme.index)
                    if time_diff <= 5:  # J线反应更快，时间窗口更小
                        pattern = {
                            'type': 'j_line_warning',
                            'strength': self._calculate_j_warning_strength(j_extreme, price_extreme),
                            'sensitivity_flag': True,  # 标记为敏感信号
                            'j_extreme': j_extreme,
                            'price_extreme': {
                                'index': price_extreme.index,
                                'price': price_extreme.price,
                                'type': price_extreme.type
                            },
                            'time_lead': price_extreme.index - j_extreme['index']  # J线领先时间
                        }
                        patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            logger.warning(f"J线预警分析失败: {e}")
            return []
    
    def _detect_kd_cross(self, k_line: np.ndarray, d_line: np.ndarray) -> List[Dict[str, Any]]:
        """检测KD金叉死叉"""
        signals = []
        
        try:
            if len(k_line) < 2 or len(d_line) < 2:
                return signals
            
            # 检测交叉点
            for i in range(1, min(len(k_line), len(d_line))):
                prev_diff = k_line[i-1] - d_line[i-1]
                curr_diff = k_line[i] - d_line[i]
                
                # 金叉：K线从下方穿越D线
                if prev_diff <= 0 and curr_diff > 0:
                    confirmation = self._confirm_kd_cross(k_line, d_line, i, 'golden')
                    if confirmation['confirmed']:
                        signal = {
                            'type': 'kd_golden_cross',
                            'index': i,
                            'strength': confirmation['strength'],
                            'k_value': k_line[i],
                            'd_value': d_line[i],
                            'zone': self._determine_cross_zone(k_line[i], d_line[i]),
                            'timing_quality': self._assess_cross_timing(k_line, d_line, i)
                        }
                        signals.append(signal)
                
                # 死叉：K线从上方穿越D线
                elif prev_diff >= 0 and curr_diff < 0:
                    confirmation = self._confirm_kd_cross(k_line, d_line, i, 'death')
                    if confirmation['confirmed']:
                        signal = {
                            'type': 'kd_death_cross',
                            'index': i,
                            'strength': confirmation['strength'],
                            'k_value': k_line[i],
                            'd_value': d_line[i],
                            'zone': self._determine_cross_zone(k_line[i], d_line[i]),
                            'timing_quality': self._assess_cross_timing(k_line, d_line, i)
                        }
                        signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.warning(f"KD交叉检测失败: {e}")
            return []
    
    def _analyze_extreme_zones(self, k_line: np.ndarray, d_line: np.ndarray, 
                              j_line: np.ndarray) -> Dict[str, Any]:
        """分析极值区域"""
        try:
            current_k = k_line[-1] if len(k_line) > 0 else 50
            current_d = d_line[-1] if len(d_line) > 0 else 50
            current_j = j_line[-1] if len(j_line) > 0 else 50
            
            # 确定当前区域
            if current_k >= self.overbought_threshold and current_d >= self.overbought_threshold:
                current_zone = 'overbought'
            elif current_k <= self.oversold_threshold and current_d <= self.oversold_threshold:
                current_zone = 'oversold'
            else:
                current_zone = 'middle'
            
            # 计算各区域的时间比例
            overbought_ratio = np.sum((k_line >= self.overbought_threshold) & 
                                    (d_line >= self.overbought_threshold)) / len(k_line)
            oversold_ratio = np.sum((k_line <= self.oversold_threshold) & 
                                  (d_line <= self.oversold_threshold)) / len(k_line)
            
            # J线极端值统计
            j_extreme_ratio = 0
            if len(j_line) > 0:
                j_extreme_ratio = np.sum((j_line > 100) | (j_line < 0)) / len(j_line)
            
            return {
                'current_zone': current_zone,
                'current_k': current_k,
                'current_d': current_d,
                'current_j': current_j,
                'overbought_ratio': overbought_ratio,
                'oversold_ratio': oversold_ratio,
                'j_extreme_ratio': j_extreme_ratio,
                'zone_reliability': self._calculate_zone_reliability(current_zone, current_k, current_d)
            }
            
        except Exception as e:
            logger.warning(f"KDJ极值区域分析失败: {e}")
            return {}
    
    def _find_local_extremes(self, data: np.ndarray, extreme_type: str) -> List[Dict[str, Any]]:
        """寻找局部极值"""
        extremes = []
        window = 2  # KDJ使用较小的窗口，反应更敏感
        
        try:
            for i in range(window, len(data) - window):
                is_extreme = True
                current_value = data[i]
                
                # 检查是否为极值
                for j in range(i - window, i + window + 1):
                    if j == i:
                        continue
                    
                    if extreme_type == 'peak':
                        if current_value <= data[j]:
                            is_extreme = False
                            break
                    else:  # trough
                        if current_value >= data[j]:
                            is_extreme = False
                            break
                
                if is_extreme:
                    extremes.append({
                        'index': i,
                        'value': current_value,
                        'type': extreme_type
                    })
            
            return extremes

        except Exception as e:
            logger.warning(f"KDJ局部极值检测失败: {e}")
            return []

    def _find_matching_extreme(self, price_extreme: Any, kdj_extremes: List[Dict[str, Any]],
                              kdj_data: np.ndarray) -> Optional[Dict[str, Any]]:
        """寻找匹配的KDJ极值"""
        if not kdj_extremes:
            return None

        # 寻找时间上最接近的极值点
        min_distance = float('inf')
        best_match = None

        for kdj_extreme in kdj_extremes:
            distance = abs(kdj_extreme['index'] - price_extreme.index)
            if distance < min_distance and distance <= 8:  # KDJ时间窗口稍小
                min_distance = distance
                best_match = kdj_extreme

        return best_match

    def _create_kdj_divergence_pattern(self, pattern_type: str, price_extreme: Any,
                                     kdj_extreme: Dict[str, Any], kdj_data: np.ndarray,
                                     line_type: str) -> Optional[Dict[str, Any]]:
        """创建KDJ背离模式"""
        try:
            # 计算背离强度
            time_diff = abs(kdj_extreme['index'] - price_extreme.index)
            if time_diff > 8:  # 时间差太大
                return None

            # KDJ特定的强度调整
            base_strength = max(0, 1.0 - time_diff / 8.0) * price_extreme.strength

            # K线和D线权重不同
            line_weight = 1.0 if line_type == 'k' else 0.9  # K线权重稍高
            adjusted_strength = base_strength * line_weight

            return {
                'type': pattern_type,
                'strength': min(adjusted_strength, 1.0),
                'line_type': line_type,
                'line_weight': line_weight,
                'price_extreme': {
                    'index': price_extreme.index,
                    'price': price_extreme.price,
                    'type': price_extreme.type
                },
                'kdj_extreme': kdj_extreme,
                'time_synchronization': 1.0 - time_diff / 8.0,
                'sensitivity_level': 'high'  # KDJ敏感度高
            }

        except Exception as e:
            logger.warning(f"KDJ背离模式创建失败: {e}")
            return None

    def _find_j_line_extremes(self, j_line: np.ndarray) -> List[Dict[str, Any]]:
        """寻找J线极端值"""
        extremes = []

        try:
            # J线可能超出0-100范围，寻找极端值
            for i in range(1, len(j_line) - 1):
                current_j = j_line[i]

                # 极端高值（>100或局部最高）
                if current_j > 100 or (current_j > j_line[i-1] and current_j > j_line[i+1] and current_j > 80):
                    extremes.append({
                        'index': i,
                        'value': current_j,
                        'type': 'extreme_high',
                        'severity': min((current_j - 80) / 20, 2.0)  # 严重程度
                    })

                # 极端低值（<0或局部最低）
                elif current_j < 0 or (current_j < j_line[i-1] and current_j < j_line[i+1] and current_j < 20):
                    extremes.append({
                        'index': i,
                        'value': current_j,
                        'type': 'extreme_low',
                        'severity': min((20 - current_j) / 20, 2.0)
                    })

            return extremes

        except Exception as e:
            logger.warning(f"J线极端值检测失败: {e}")
            return []

    def _calculate_j_warning_strength(self, j_extreme: Dict[str, Any], price_extreme: Any) -> float:
        """计算J线预警强度"""
        try:
            # 基于J线的极端程度和价格极值强度
            j_severity = j_extreme.get('severity', 1.0)
            price_strength = price_extreme.strength

            # J线敏感度调整
            warning_strength = (j_severity * self.j_line_sensitivity + price_strength) / 2

            return min(warning_strength, 1.0)

        except Exception as e:
            logger.warning(f"J线预警强度计算失败: {e}")
            return 0.5

    def _confirm_kd_cross(self, k_line: np.ndarray, d_line: np.ndarray,
                         cross_index: int, cross_type: str) -> Dict[str, Any]:
        """确认KD交叉"""
        try:
            # 检查后续几个周期是否保持交叉状态
            confirmed_periods = 0
            max_check_periods = min(self.cross_confirmation_periods, len(k_line) - cross_index - 1)

            for i in range(1, max_check_periods + 1):
                if cross_index + i >= len(k_line) or cross_index + i >= len(d_line):
                    break

                if cross_type == 'golden':
                    if k_line[cross_index + i] > d_line[cross_index + i]:
                        confirmed_periods += 1
                else:  # death
                    if k_line[cross_index + i] < d_line[cross_index + i]:
                        confirmed_periods += 1

            confirmation_ratio = confirmed_periods / max_check_periods if max_check_periods > 0 else 0

            return {
                'confirmed': confirmation_ratio >= 0.5,  # KDJ确认要求稍低
                'strength': confirmation_ratio,
                'confirmed_periods': confirmed_periods
            }

        except Exception as e:
            logger.warning(f"KD交叉确认失败: {e}")
            return {'confirmed': False, 'strength': 0.0, 'confirmed_periods': 0}

    def _determine_cross_zone(self, k_value: float, d_value: float) -> str:
        """确定交叉发生的区域"""
        avg_value = (k_value + d_value) / 2

        if avg_value >= self.overbought_threshold:
            return 'overbought'
        elif avg_value <= self.oversold_threshold:
            return 'oversold'
        else:
            return 'middle'

    def _assess_cross_timing(self, k_line: np.ndarray, d_line: np.ndarray, cross_index: int) -> str:
        """评估交叉时机质量"""
        try:
            k_value = k_line[cross_index]
            d_value = d_line[cross_index]
            avg_value = (k_value + d_value) / 2

            # 在极值区域的交叉质量更高
            if avg_value <= self.oversold_threshold:
                return 'excellent'  # 超卖区域的金叉
            elif avg_value >= self.overbought_threshold:
                return 'excellent'  # 超买区域的死叉
            elif 30 <= avg_value <= 70:
                return 'good'       # 中间区域
            else:
                return 'fair'       # 其他区域

        except Exception as e:
            logger.warning(f"交叉时机评估失败: {e}")
            return 'unknown'

    def _calculate_zone_reliability(self, zone: str, k_value: float, d_value: float) -> float:
        """计算区域可靠性"""
        try:
            # KD值的一致性
            kd_consistency = 1.0 - abs(k_value - d_value) / 100

            # 极值区域可靠性更高
            if zone in ['overbought', 'oversold']:
                base_reliability = 0.8
            else:
                base_reliability = 0.6

            return min(base_reliability * kd_consistency, 1.0)

        except Exception as e:
            logger.warning(f"区域可靠性计算失败: {e}")
            return 0.5

    def _add_volume_confirmation(self, patterns: List[Dict[str, Any]],
                               volume: np.ndarray) -> List[Dict[str, Any]]:
        """添加成交量确认"""
        try:
            for pattern in patterns:
                if 'price_extreme' in pattern:
                    price_index = pattern['price_extreme']['index']
                    if price_index < len(volume):
                        # 计算成交量相对强度
                        window = min(15, len(volume))  # KDJ用较短窗口
                        start_idx = max(0, price_index - window + 1)
                        recent_volume = volume[start_idx:price_index + 1]

                        if len(recent_volume) > 1:
                            current_volume = volume[price_index]
                            avg_volume = np.mean(recent_volume[:-1])

                            if avg_volume > 0:
                                volume_ratio = current_volume / avg_volume
                                volume_confirmation = min(volume_ratio / 2.0, 1.0)

                                # 调整模式强度（KDJ对成交量更敏感）
                                original_strength = pattern['strength']
                                pattern['strength'] = (
                                    original_strength * (1 - self.volume_weight) +
                                    volume_confirmation * self.volume_weight
                                )
                                pattern['volume_confirmation'] = volume_confirmation

            return patterns

        except Exception as e:
            logger.warning(f"成交量确认失败: {e}")
            return patterns

    def _generate_summary(self, patterns: List[Dict[str, Any]], signals: List[Dict[str, Any]],
                         extreme_zone_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成汇总信息"""
        try:
            total_patterns = len(patterns)
            confirmed_patterns = len([p for p in patterns if p.get('strength', 0) > 0.5])
            max_strength = max([p.get('strength', 0) for p in patterns], default=0)

            # 基于区域和交叉信号生成建议
            current_zone = extreme_zone_analysis.get('current_zone', 'middle')
            golden_crosses = len([s for s in signals if s['type'] == 'kd_golden_cross'])
            death_crosses = len([s for s in signals if s['type'] == 'kd_death_cross'])

            if current_zone == 'oversold' and golden_crosses > 0:
                recommendation = 'strong_buy'
            elif current_zone == 'overbought' and death_crosses > 0:
                recommendation = 'strong_sell'
            elif max_strength > 0.7:
                recommendation = 'moderate_signal'
            else:
                recommendation = 'weak_signal'

            return {
                'total_patterns': total_patterns,
                'confirmed_patterns': confirmed_patterns,
                'max_strength': max_strength,
                'recommendation': recommendation,
                'current_zone': current_zone,
                'golden_crosses': golden_crosses,
                'death_crosses': death_crosses,
                'sensitivity_flags': len([p for p in patterns if p.get('sensitivity_flag', False)]),
                'zone_reliability': extreme_zone_analysis.get('zone_reliability', 0.5)
            }

        except Exception as e:
            logger.warning(f"KDJ汇总生成失败: {e}")
            return {
                'total_patterns': 0,
                'confirmed_patterns': 0,
                'max_strength': 0,
                'recommendation': 'no_signal'
            }

    def _get_empty_result(self) -> IndicatorResult:
        """获取空结果"""
        return IndicatorResult(
            patterns=[],
            summary={
                'total_patterns': 0,
                'confirmed_patterns': 0,
                'max_strength': 0,
                'recommendation': 'no_signal'
            },
            signals=[],
            metadata={'error': 'No valid data'}
        )
