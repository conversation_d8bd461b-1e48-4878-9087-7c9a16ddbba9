"""
日志模块

提供日志记录功能。
"""

import logging
import os
import sys
import time
from datetime import datetime
from logging.handlers import RotatingFileHandler
from typing import Optional, Dict, Any

# 默认日志格式
DEFAULT_LOG_FORMAT = '%(asctime)s [%(levelname)s] [%(name)s] - %(message)s'
DEFAULT_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# 日志级别映射
LOG_LEVEL_MAP = {
    'debug': logging.DEBUG,
    'info': logging.INFO,
    'warning': logging.WARNING,
    'error': logging.ERROR,
    'critical': logging.CRITICAL
}

class CustomLogger:
    """自定义日志类，提供单例模式和缓存功能"""

    _instance: Optional[Any] = None
    _loggers = {}

    def __init__(self, name):
        """
        初始化日志器

        Args:
            name: 日志器名称
        """
        self.name = name

    @classmethod
    def get_logger(cls, name, level='info', log_dir='logs', console=True):
        """
        获取日志器实例

        Args:
            name: 日志器名称
            level: 日志级别
            log_dir: 日志目录
            console: 是否输出到控制台

        Returns:
            logging.Logger: 日志器实例
        """
        if name not in cls._loggers:
            logger = logging.getLogger(name)

            # 确保移除所有现有的handler
            if logger.handlers:
                for handler in logger.handlers:
                    handler.close()
                logger.handlers.clear()

            # 获取日志级别
            log_level = LOG_LEVEL_MAP.get(level.lower(), logging.INFO)
            logger.setLevel(log_level)

            # 创建日志目录
            if not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)

            # 文件处理器
            log_file = os.path.join(log_dir, f'{name}_{datetime.now().strftime("%Y%m%d")}.log')
            file_handler = RotatingFileHandler(
                log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8-sig'
            )
            file_handler.setLevel(log_level)

            # 控制台处理器
            if console:
                console_handler = logging.StreamHandler(sys.stdout)
                console_handler.setLevel(log_level)

                # 设置格式
                formatter = logging.Formatter(DEFAULT_LOG_FORMAT, DEFAULT_DATE_FORMAT)
                file_handler.setFormatter(formatter)
                console_handler.setFormatter(formatter)

                # 添加处理器
                logger.addHandler(console_handler)

            # 设置文件处理器格式并添加
            formatter = logging.Formatter(DEFAULT_LOG_FORMAT, DEFAULT_DATE_FORMAT)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

            # 防止日志向上冒泡
            logger.propagate = False

            cls._loggers[name] = logger

        return cls._loggers[name]

def setup_logger(
    name: str,
    level: str = 'info',
    log_file: Optional[str] = None,
    console: bool = True,
    file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    log_format: str = DEFAULT_LOG_FORMAT,
    date_format: str = DEFAULT_DATE_FORMAT
) -> logging.Logger:
    """
    设置日志记录器

    Args:
        name: 日志记录器名称
        level: 日志级别，可选值为 'debug', 'info', 'warning', 'error', 'critical'
        log_file: 日志文件路径，如果为None则不记录到文件
        console: 是否输出到控制台
        file_size: 单个日志文件大小限制，默认10MB
        backup_count: 保留的日志文件数量，默认5个
        log_format: 日志格式
        date_format: 日期格式

    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 获取日志级别
    level = LOG_LEVEL_MAP.get(level.lower(), logging.INFO)

    # 创建日志记录器
    logger = logging.getLogger(name)

    # 如果已经有处理器，先清除
    if logger.handlers:
        # 关闭所有处理器，释放资源
        for handler in logger.handlers:
            handler.close()
        logger.handlers.clear()

    # 设置日志级别
    logger.setLevel(level)

    # 防止日志向上冒泡，避免重复输出
    logger.propagate = False

    # 创建格式化器
    formatter = logging.Formatter(log_format, date_format)

    # 添加控制台处理器
    if console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    # 添加文件处理器
    if log_file:
        try:
            # 确保日志目录存在
            log_dir = os.path.dirname(log_file)
            if log_dir:
                # 尝试递归创建目录，忽略已存在的目录
                os.makedirs(log_dir, exist_ok=True)

                # 检查目录是否成功创建且可写
                if not os.path.exists(log_dir):
                    logger.error(f"无法创建日志目录: {log_dir}")
                    # 退化为仅控制台输出
                    return logger

                if not os.access(log_dir, os.W_OK):
                    logger.error(f"日志目录没有写入权限: {log_dir}")
                    # 退化为仅控制台输出
                    return logger

            # 创建滚动文件处理器
            file_handler = RotatingFileHandler(
                log_file,
                maxBytes=file_size,
                backupCount=backup_count,
                encoding='utf-8-sig'
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

        except Exception as e:
            # 如果出现任何错误，记录错误并继续使用控制台输出
            if console:
                logger.error(f"设置日志文件处理器失败: {str(e)}")

    return logger

class Logger:
    """
    日志记录器类，提供便捷的日志记录方法
    """
    def __init__(
        self,
        name: str,
        level: str = 'info',
        log_dir: str = 'logs',
        console: bool = True,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化日志记录器

        Args:
            name: 日志记录器名称
            level: 日志级别
            log_dir: 日志文件目录
            console: 是否输出到控制台
            config: 其他配置项
        """
        self.name = name

        # 使用配置项
        if config:
            level = config.get('level', level)
            log_dir = config.get('log_dir', log_dir)
            console = config.get('console', console)

        # 创建日志目录
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        timestamp = time.strftime('%Y%m%d', time.localtime())
        log_file = os.path.join(log_dir, f'{name}_{timestamp}.log')

        # 设置日志记录器
        self.logger = setup_logger(
            name=name,
            level=level,
            log_file=log_file,
            console=console
        )

    def debug(self, message: str) -> None:
        """记录调试级别日志"""
        self.logger.debug(message)

    def info(self, message: str) -> None:
        """记录信息级别日志"""
        self.logger.info(message)

    def warning(self, message: str) -> None:
        """记录警告级别日志"""
        self.logger.warning(message)

    def error(self, message: str) -> None:
        """记录错误级别日志"""
        self.logger.error(message)

    def critical(self, message: str) -> None:
        """记录严重错误级别日志"""
        self.logger.critical(message)

    def exception(self, message: str) -> None:
        """记录异常信息，包含堆栈跟踪"""
        self.logger.exception(message)

# 创建默认的应用日志记录器
app_logger = Logger(name='ctrading')

# 定义快捷函数
def debug(message: str) -> None:
    """记录调试级别日志"""
    app_logger.debug(message)

def info(message: str) -> None:
    """记录信息级别日志"""
    app_logger.info(message)

def warning(message: str) -> None:
    """记录警告级别日志"""
    app_logger.warning(message)

def error(message: str) -> None:
    """记录错误级别日志"""
    app_logger.error(message)

def critical(message: str) -> None:
    """记录严重错误级别日志"""
    app_logger.critical(message)

def exception(message: str) -> None:
    """记录异常信息，包含堆栈跟踪"""
    app_logger.exception(message)

def get_logger(
    name: str,
    level: str = 'info',
    log_dir: str = 'logs',
    log_file: Optional[str] = None,
    console: bool = True,
    file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    use_custom_logger: bool = False
) -> logging.Logger:
    """
    获取日志记录器

    Args:
        name: 日志记录器名称
        level: 日志级别，可选值为'debug', 'info', 'warning', 'error', 'critical'
        log_dir: 日志文件目录
        log_file: 日志文件名，如果为None则使用自动生成的文件名
        console: 是否输出到控制台
        file_size: 日志文件大小限制，单位为字节
        backup_count: 备份文件数量
        use_custom_logger: 是否使用CustomLogger

    Returns:
        logging.Logger: 日志记录器实例
    """
    # 确保日志目录存在
    if not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)

    if use_custom_logger:
        return CustomLogger.get_logger(name=name, level=level, log_dir=log_dir, console=console)

    # 如果没有提供日志文件名，则自动生成
    if not log_file:
        timestamp = time.strftime('%Y%m%d', time.localtime())
        log_file = os.path.join(log_dir, f'{name}_{timestamp}.log')
    else:
        log_file = os.path.join(log_dir, log_file)

    return setup_logger(
        name=name,
        level=level,
        log_file=log_file,
        console=console,
        file_size=file_size,
        backup_count=backup_count
    )

# 配置根日志记录器，确保第三方库日志也被正确处理
def configure_root_logger(level: str = 'warning', log_file: str = None):
    """
    配置根日志记录器

    Args:
        level: 日志级别
        log_file: 日志文件路径
    """
    root_logger = logging.getLogger()

    # 清除已有处理器
    if root_logger.handlers:
        for handler in root_logger.handlers:
            handler.close()
        root_logger.handlers.clear()

    # 设置级别
    root_logger.setLevel(LOG_LEVEL_MAP.get(level.lower(), logging.WARNING))

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter(DEFAULT_LOG_FORMAT, DEFAULT_DATE_FORMAT)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # 文件处理器
    if log_file:
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,
            backupCount=3,
            encoding='utf-8-sig'
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

    return root_logger

# 初始化时配置根日志记录器，但关闭向上冒泡以避免重复日志
# 同时禁用现有的basicConfig配置
logging.basicConfig(handlers=[logging.NullHandler()])
