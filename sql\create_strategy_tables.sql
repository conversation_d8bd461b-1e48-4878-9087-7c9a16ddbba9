-- 创建策略相关数据库表
-- 作者: QuantFM Team
-- 创建时间: 2025-08-08

-- 1. 双通道斐波那契策略信号表
CREATE TABLE IF NOT EXISTS dual_channel_fibonacci_signals (
    id SERIAL PRIMARY KEY,
    signal_time TIMESTAMPTZ NOT NULL,
    stock_code VARCHAR(20) NOT NULL,
    stock_name VARCHAR(100) NOT NULL,
    strategy_name VARCHAR(100) NOT NULL DEFAULT '双通道斐波那契突破',
    
    -- 关键时间点
    break_t1_date TIMESTAMPTZ NOT NULL,  -- 通道1突破日期
    break_t2_date TIMESTAMPTZ NOT NULL,  -- 通道2突破日期（触发日期）
    start_low_date TIMESTAMPTZ NOT NULL, -- 起始低点日期
    target_high_date TIMESTAMPTZ NOT NULL, -- 目标高点日期
    
    -- 关键价格点
    start_low_price DECIMAL(10,3) NOT NULL,   -- 起始低点价格
    target_high_price DECIMAL(10,3) NOT NULL, -- 目标高点价格
    break_t1_price DECIMAL(10,3) NOT NULL,    -- 通道1突破价格
    break_t2_price DECIMAL(10,3) NOT NULL,    -- 通道2突破价格
    
    -- 信号属性
    signal_strength DECIMAL(5,3) NOT NULL,    -- 信号强度
    volume_ratio_t1 DECIMAL(8,3) NOT NULL,    -- 通道1突破时成交量比率
    volume_ratio_t2 DECIMAL(8,3) NOT NULL,    -- 通道2突破时成交量比率
    
    -- 计算指标
    breakout_amplitude DECIMAL(8,3) NOT NULL, -- 突破幅度
    stability_score DECIMAL(5,3) NOT NULL,    -- 稳定性评分
    structure_score DECIMAL(5,3) NOT NULL,    -- 结构评分
    
    -- 其他信息
    signal_note TEXT,                          -- 信号备注
    
    -- 时间戳
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- 唯一约束：同一股票同一天只能有一个信号
    UNIQUE(stock_code, break_t2_date::DATE)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_dual_channel_signals_stock_code ON dual_channel_fibonacci_signals(stock_code);
CREATE INDEX IF NOT EXISTS idx_dual_channel_signals_signal_time ON dual_channel_fibonacci_signals(signal_time);
CREATE INDEX IF NOT EXISTS idx_dual_channel_signals_break_t2_date ON dual_channel_fibonacci_signals(break_t2_date);
CREATE INDEX IF NOT EXISTS idx_dual_channel_signals_signal_strength ON dual_channel_fibonacci_signals(signal_strength);

-- 2. 通用策略信号表（用于存储所有策略的信号）
CREATE TABLE IF NOT EXISTS strategy_signals (
    id SERIAL PRIMARY KEY,
    signal_time TIMESTAMPTZ NOT NULL,
    stock_code VARCHAR(20) NOT NULL,
    stock_name VARCHAR(100) NOT NULL,
    strategy_name VARCHAR(100) NOT NULL,
    signal_type VARCHAR(50) NOT NULL DEFAULT 'strategy_signal',
    
    -- 基础信号属性
    signal_strength DECIMAL(5,3) NOT NULL,
    signal_price DECIMAL(10,3) NOT NULL,
    latest_volume BIGINT,
    avg_volume BIGINT,
    volume_ratio DECIMAL(8,3),
    latest_close DECIMAL(10,3),
    max_high_20d DECIMAL(10,3),
    breakout_ratio DECIMAL(8,3),
    
    -- 扩展属性（JSON格式存储策略特定数据）
    strategy_data JSONB,
    signal_note TEXT,
    
    -- 状态字段
    is_active BOOLEAN DEFAULT TRUE,
    is_processed BOOLEAN DEFAULT FALSE,
    
    -- 时间戳
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- 唯一约束：同一股票同一策略同一天只能有一个信号
    UNIQUE(stock_code, strategy_name, signal_time::DATE)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_strategy_signals_stock_code ON strategy_signals(stock_code);
CREATE INDEX IF NOT EXISTS idx_strategy_signals_signal_time ON strategy_signals(signal_time);
CREATE INDEX IF NOT EXISTS idx_strategy_signals_strategy_name ON strategy_signals(strategy_name);
CREATE INDEX IF NOT EXISTS idx_strategy_signals_signal_strength ON strategy_signals(signal_strength);
CREATE INDEX IF NOT EXISTS idx_strategy_signals_is_active ON strategy_signals(is_active);
CREATE INDEX IF NOT EXISTS idx_strategy_signals_strategy_data ON strategy_signals USING GIN(strategy_data);

-- 3. 策略配置表
CREATE TABLE IF NOT EXISTS strategy_configs (
    id SERIAL PRIMARY KEY,
    strategy_name VARCHAR(100) NOT NULL UNIQUE,
    strategy_class VARCHAR(200) NOT NULL,
    config_data JSONB NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    description TEXT,
    
    -- 时间戳
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_strategy_configs_strategy_name ON strategy_configs(strategy_name);
CREATE INDEX IF NOT EXISTS idx_strategy_configs_is_enabled ON strategy_configs(is_enabled);

-- 4. 策略执行日志表
CREATE TABLE IF NOT EXISTS strategy_execution_logs (
    id SERIAL PRIMARY KEY,
    execution_time TIMESTAMPTZ NOT NULL,
    strategy_name VARCHAR(100) NOT NULL,
    execution_type VARCHAR(50) NOT NULL, -- 'scheduled', 'manual', 'backtest'
    
    -- 执行统计
    stocks_processed INTEGER DEFAULT 0,
    signals_generated INTEGER DEFAULT 0,
    execution_duration_ms INTEGER DEFAULT 0,
    
    -- 执行状态
    status VARCHAR(20) NOT NULL DEFAULT 'running', -- 'running', 'completed', 'failed'
    error_message TEXT,
    
    -- 执行详情
    execution_details JSONB,
    
    -- 时间戳
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_strategy_execution_logs_execution_time ON strategy_execution_logs(execution_time);
CREATE INDEX IF NOT EXISTS idx_strategy_execution_logs_strategy_name ON strategy_execution_logs(strategy_name);
CREATE INDEX IF NOT EXISTS idx_strategy_execution_logs_status ON strategy_execution_logs(status);

-- 5. 策略性能统计表
CREATE TABLE IF NOT EXISTS strategy_performance_stats (
    id SERIAL PRIMARY KEY,
    strategy_name VARCHAR(100) NOT NULL,
    stat_date DATE NOT NULL,
    
    -- 信号统计
    total_signals INTEGER DEFAULT 0,
    high_strength_signals INTEGER DEFAULT 0, -- 强度 > 0.8
    medium_strength_signals INTEGER DEFAULT 0, -- 强度 0.5-0.8
    low_strength_signals INTEGER DEFAULT 0, -- 强度 < 0.5
    
    -- 性能统计
    avg_signal_strength DECIMAL(5,3),
    avg_execution_time_ms INTEGER,
    success_rate DECIMAL(5,3),
    
    -- 市场表现（如果有后续跟踪）
    avg_return_1d DECIMAL(8,3),
    avg_return_3d DECIMAL(8,3),
    avg_return_7d DECIMAL(8,3),
    win_rate DECIMAL(5,3),
    
    -- 时间戳
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- 唯一约束
    UNIQUE(strategy_name, stat_date)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_strategy_performance_stats_strategy_name ON strategy_performance_stats(strategy_name);
CREATE INDEX IF NOT EXISTS idx_strategy_performance_stats_stat_date ON strategy_performance_stats(stat_date);

-- 6. 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表创建更新时间戳触发器
CREATE TRIGGER update_dual_channel_fibonacci_signals_updated_at 
    BEFORE UPDATE ON dual_channel_fibonacci_signals 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_strategy_signals_updated_at 
    BEFORE UPDATE ON strategy_signals 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_strategy_configs_updated_at 
    BEFORE UPDATE ON strategy_configs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_strategy_execution_logs_updated_at 
    BEFORE UPDATE ON strategy_execution_logs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_strategy_performance_stats_updated_at 
    BEFORE UPDATE ON strategy_performance_stats 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. 插入默认策略配置
INSERT INTO strategy_configs (strategy_name, strategy_class, config_data, description) 
VALUES (
    '双通道斐波那契突破',
    'strategies.trending.dual_channel_fibonacci.DualChannelFibonacciStrategy',
    '{
        "ema_short_1": 144,
        "ema_long_1": 169,
        "ema_short_2": 576,
        "ema_long_2": 676,
        "volume_ratio": 1.5,
        "min_days": 1,
        "max_days": 60,
        "max_intrusion_days": 3,
        "pre_check_days": 60,
        "pivot_window": 5
    }',
    '基于两个EMA通道的突破策略，检测从通道1下方开始，依次突破通道1和通道2的完整周期'
) ON CONFLICT (strategy_name) DO NOTHING;

-- 8. 创建视图：策略信号汇总
CREATE OR REPLACE VIEW v_strategy_signals_summary AS
SELECT 
    strategy_name,
    DATE(signal_time) as signal_date,
    COUNT(*) as total_signals,
    COUNT(CASE WHEN signal_strength >= 0.8 THEN 1 END) as high_strength_signals,
    COUNT(CASE WHEN signal_strength >= 0.5 AND signal_strength < 0.8 THEN 1 END) as medium_strength_signals,
    COUNT(CASE WHEN signal_strength < 0.5 THEN 1 END) as low_strength_signals,
    AVG(signal_strength) as avg_signal_strength,
    AVG(volume_ratio) as avg_volume_ratio,
    STRING_AGG(stock_code, ', ' ORDER BY signal_strength DESC) as top_stocks
FROM strategy_signals 
WHERE is_active = TRUE
GROUP BY strategy_name, DATE(signal_time)
ORDER BY signal_date DESC, strategy_name;

-- 9. 创建视图：最新策略信号
CREATE OR REPLACE VIEW v_latest_strategy_signals AS
SELECT DISTINCT ON (stock_code, strategy_name)
    *
FROM strategy_signals 
WHERE is_active = TRUE
ORDER BY stock_code, strategy_name, signal_time DESC;

COMMENT ON TABLE dual_channel_fibonacci_signals IS '双通道斐波那契策略专用信号表';
COMMENT ON TABLE strategy_signals IS '通用策略信号表，存储所有策略的信号';
COMMENT ON TABLE strategy_configs IS '策略配置表';
COMMENT ON TABLE strategy_execution_logs IS '策略执行日志表';
COMMENT ON TABLE strategy_performance_stats IS '策略性能统计表';
COMMENT ON VIEW v_strategy_signals_summary IS '策略信号汇总视图';
COMMENT ON VIEW v_latest_strategy_signals IS '最新策略信号视图';
