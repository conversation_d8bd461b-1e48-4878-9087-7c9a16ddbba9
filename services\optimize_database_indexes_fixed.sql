-- QuantFM 数据库索引优化脚本 (修复版)
-- 用于优化TimescaleDB查询性能，支持删除共享内存后的高频数据库访问
-- 
-- 修复内容：
-- 1. 移除CONCURRENTLY关键字（TimescaleDB hypertables不支持）
-- 2. 修正字段名（trade_date -> trade_time）
-- 3. 适配实际表结构
--
-- 作者: QuantFM Team
-- 创建时间: 2025-07-13

-- =====================================================
-- Tick数据表索引优化
-- =====================================================

-- 复合索引：股票代码 + 时间降序（用于最新数据查询）
CREATE INDEX IF NOT EXISTS idx_tick_stock_time_desc 
    ON stock_tick_data (stock_code, trade_time DESC);

-- 当日数据查询优化（部分索引）
CREATE INDEX IF NOT EXISTS idx_tick_today 
    ON stock_tick_data (stock_code, trade_time DESC) 
    WHERE DATE(trade_time) = CURRENT_DATE;

-- 成交量分析查询优化
CREATE INDEX IF NOT EXISTS idx_tick_volume_analysis 
    ON stock_tick_data (stock_code, trade_time) 
    WHERE cur_vol > 0 AND amount > 0;

-- 价格范围查询优化
CREATE INDEX IF NOT EXISTS idx_tick_price_range 
    ON stock_tick_data (stock_code, price, trade_time);

-- =====================================================
-- 5分钟K线表索引优化
-- =====================================================

-- 复合索引：股票代码 + 时间降序
CREATE INDEX IF NOT EXISTS idx_kline5_stock_time_desc 
    ON stock_kline_5min (stock_code, trade_time DESC);

-- 成交量异动分析优化
CREATE INDEX IF NOT EXISTS idx_kline5_volume_analysis 
    ON stock_kline_5min (stock_code, trade_time DESC) 
    WHERE volume > 0 AND amount > 0;

-- 当日K线查询优化
CREATE INDEX IF NOT EXISTS idx_kline5_today 
    ON stock_kline_5min (stock_code, trade_time DESC) 
    WHERE DATE(trade_time) = CURRENT_DATE;

-- =====================================================
-- 日K线表索引优化
-- =====================================================

-- 复合索引：股票代码 + 时间降序
CREATE INDEX IF NOT EXISTS idx_kline_day_stock_time_desc 
    ON stock_kline_day (stock_code, trade_time DESC);

-- 技术指标计算优化（需要大量历史数据）
CREATE INDEX IF NOT EXISTS idx_kline_day_time_asc 
    ON stock_kline_day (stock_code, trade_time ASC);

-- 价格查询优化
CREATE INDEX IF NOT EXISTS idx_kline_day_close_price 
    ON stock_kline_day (stock_code, trade_time DESC, close);

-- =====================================================
-- 2小时K线表索引优化
-- =====================================================

-- 复合索引：股票代码 + 时间降序
CREATE INDEX IF NOT EXISTS idx_kline2h_stock_time_desc 
    ON stock_kline_2hour (stock_code, trade_time DESC);

-- 当日2小时K线查询优化
CREATE INDEX IF NOT EXISTS idx_kline2h_today 
    ON stock_kline_2hour (stock_code, trade_time DESC) 
    WHERE DATE(trade_time) = CURRENT_DATE;

-- =====================================================
-- 复合索引优化（针对特定查询模式）
-- =====================================================

-- 成交量比值分析专用索引
CREATE INDEX IF NOT EXISTS idx_tick_volume_ratio_analysis 
    ON stock_tick_data (stock_code, EXTRACT(HOUR FROM trade_time), EXTRACT(MINUTE FROM trade_time))
    WHERE cur_vol > 0 AND amount > 0;

-- 实时数据获取专用索引
CREATE INDEX IF NOT EXISTS idx_realtime_data_fetch 
    ON stock_tick_data (DATE(trade_time), stock_code, trade_time DESC)
    WHERE DATE(trade_time) >= CURRENT_DATE - INTERVAL '1 day';

-- K线生成专用索引
CREATE INDEX IF NOT EXISTS idx_kline_generation 
    ON stock_tick_data (stock_code, DATE(trade_time), trade_time)
    WHERE volume > 0;

-- =====================================================
-- 查询性能优化设置
-- =====================================================

-- 增加工作内存以支持大型排序和哈希操作
SET work_mem = '256MB';

-- 优化随机页面成本（SSD存储）
SET random_page_cost = 1.1;

-- 增加有效缓存大小
SET effective_cache_size = '4GB';

-- =====================================================
-- 索引维护和监控
-- =====================================================

-- 创建索引使用情况监控视图
CREATE OR REPLACE VIEW v_index_usage_stats AS
SELECT 
    schemaname,
    relname as tablename,
    indexrelname as indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE 
        WHEN idx_scan = 0 THEN 'UNUSED'
        WHEN idx_scan < 100 THEN 'LOW_USAGE'
        WHEN idx_scan < 1000 THEN 'MEDIUM_USAGE'
        ELSE 'HIGH_USAGE'
    END as usage_level
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
    AND relname IN ('stock_tick_data', 'stock_kline_5min', 'stock_kline_day', 'stock_kline_2hour')
ORDER BY idx_scan DESC;

-- 创建表大小监控视图
CREATE OR REPLACE VIEW v_table_size_stats AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as indexes_size,
    pg_total_relation_size(schemaname||'.'||tablename) as total_bytes
FROM pg_tables 
WHERE schemaname = 'public'
    AND tablename IN ('stock_tick_data', 'stock_kline_5min', 'stock_kline_day', 'stock_kline_2hour')
ORDER BY total_bytes DESC;

-- =====================================================
-- 索引创建完成后的验证
-- =====================================================

-- 验证索引是否创建成功
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public'
    AND tablename IN ('stock_tick_data', 'stock_kline_5min', 'stock_kline_day', 'stock_kline_2hour')
    AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

-- 检查索引大小
SELECT 
    schemaname,
    relname as tablename,
    indexrelname as indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
    AND relname IN ('stock_tick_data', 'stock_kline_5min', 'stock_kline_day', 'stock_kline_2hour')
ORDER BY pg_relation_size(indexrelid) DESC;
