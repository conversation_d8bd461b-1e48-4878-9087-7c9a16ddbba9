#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成交量激增因子

实现成交量激增因子的核心计算逻辑，支持开盘期100倍和盘中期10倍的激增检测。

功能特点：
1. 开盘因子计算 (100倍阈值)
2. 盘中因子计算 (10倍阈值)  
3. 连续监控机制
4. 数据验证和异常处理
5. 性能优化和向量化计算

作者: QuantFM Team
创建时间: 2025-08-08
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger
from data.db_manager import get_db_manager
from utils.realtime_kline_calculator import RealTimeKlineCalculator


@dataclass
class VolumeSurgeSignal:
    """成交量激增信号"""
    stock_code: str
    signal_type: str  # 'OPENING' 或 'INTRADAY'
    current_volume: float
    historical_avg_volume: float
    surge_ratio: float
    timestamp: datetime
    confidence: float
    period_info: str  # 时间段信息
    continuous_count: int = 0  # 连续次数
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'stock_code': self.stock_code,
            'signal_type': self.signal_type,
            'current_volume': self.current_volume,
            'historical_avg_volume': self.historical_avg_volume,
            'surge_ratio': self.surge_ratio,
            'timestamp': self.timestamp,
            'confidence': self.confidence,
            'period_info': self.period_info,
            'continuous_count': self.continuous_count
        }


class VolumeSurgeFactorEngine:
    """成交量激增因子计算引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化因子计算引擎
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.logger = get_logger("VolumeSurgeFactorEngine")
        
        # 阈值配置
        self.opening_threshold = config.get("opening_ratio_threshold", 100)
        self.intraday_threshold = config.get("intraday_ratio_threshold", 10)
        self.min_volume_threshold = config.get("min_volume_threshold", 1000)
        self.historical_days = config.get("historical_days", 10)
        
        # 数据库管理器
        self.db_manager = get_db_manager()
        
        # K线计算器
        self.kline_calculator = RealTimeKlineCalculator(self.logger)
        
        # 缓存配置
        self.enable_cache = config.get("enable_cache", True)
        self.cache_ttl = config.get("cache_ttl", 3600)
        
        # 计算缓存
        self.calculation_cache = {}
        self.cache_timestamps = {}
        
        # 性能统计
        self.stats = {
            'opening_calculations': 0,
            'intraday_calculations': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'signals_generated': 0,
            'data_events_processed': 0
        }
        
        self.logger.info(f"成交量激增因子引擎初始化完成 - 开盘阈值: {self.opening_threshold}x, 盘中阈值: {self.intraday_threshold}x")
    
    def opening_surge_detection(self, stock_data: Dict[str, Any], 
                              historical_avg_data: Dict[str, float]) -> List[VolumeSurgeSignal]:
        """
        开盘期激增检测
        
        Args:
            stock_data: 当前股票数据 {stock_code: current_volume}
            historical_avg_data: 历史平均数据 {stock_code: avg_volume}
            
        Returns:
            激增信号列表
        """
        self.stats['opening_calculations'] += 1
        signals = []
        
        try:
            for stock_code, current_volume in stock_data.items():
                # 数据验证
                if not self._validate_volume_data(current_volume, stock_code):
                    continue
                
                # 获取历史平均值
                historical_avg = historical_avg_data.get(stock_code)
                if historical_avg is None or historical_avg <= 0:
                    self.logger.debug(f"股票 {stock_code} 缺少历史平均数据")
                    continue
                
                # 计算激增比值
                surge_ratio = current_volume / historical_avg
                
                # 判断是否超过阈值
                if surge_ratio >= self.opening_threshold:
                    confidence = self._calculate_confidence(surge_ratio, self.opening_threshold)
                    
                    signal = VolumeSurgeSignal(
                        stock_code=stock_code,
                        signal_type='OPENING',
                        current_volume=current_volume,
                        historical_avg_volume=historical_avg,
                        surge_ratio=surge_ratio,
                        timestamp=datetime.now(),
                        confidence=confidence,
                        period_info=f"开盘期({datetime.now().strftime('%H:%M:%S')})"
                    )
                    
                    signals.append(signal)
                    self.stats['signals_generated'] += 1
                    
                    self.logger.info(f"开盘期激增信号: {stock_code}, 比值: {surge_ratio:.2f}x, 置信度: {confidence:.3f}")
        
        except Exception as e:
            self.logger.error(f"开盘期激增检测失败: {e}")
        
        return signals
    
    def intraday_surge_detection(self, kline_data: Dict[str, Dict], 
                               historical_avg_data: Dict[str, float]) -> List[VolumeSurgeSignal]:
        """
        盘中期激增检测
        
        Args:
            kline_data: K线数据 {stock_code: kline_info}
            historical_avg_data: 历史平均数据 {stock_code: avg_volume}
            
        Returns:
            激增信号列表
        """
        self.stats['intraday_calculations'] += 1
        signals = []
        
        try:
            for stock_code, kline in kline_data.items():
                # 提取当前成交量
                current_volume = kline.get('volume', 0)
                
                # 数据验证
                if not self._validate_volume_data(current_volume, stock_code):
                    continue
                
                # 获取历史平均值
                historical_avg = historical_avg_data.get(stock_code)
                if historical_avg is None or historical_avg <= 0:
                    self.logger.debug(f"股票 {stock_code} 缺少历史平均数据")
                    continue
                
                # 计算激增比值
                surge_ratio = current_volume / historical_avg
                
                # 判断是否超过阈值
                if surge_ratio >= self.intraday_threshold:
                    confidence = self._calculate_confidence(surge_ratio, self.intraday_threshold)
                    
                    signal = VolumeSurgeSignal(
                        stock_code=stock_code,
                        signal_type='INTRADAY',
                        current_volume=current_volume,
                        historical_avg_volume=historical_avg,
                        surge_ratio=surge_ratio,
                        timestamp=datetime.now(),
                        confidence=confidence,
                        period_info=f"盘中期({datetime.now().strftime('%H:%M:%S')})"
                    )
                    
                    signals.append(signal)
                    self.stats['signals_generated'] += 1
                    
                    self.logger.info(f"盘中期激增信号: {stock_code}, 比值: {surge_ratio:.2f}x, 置信度: {confidence:.3f}")
        
        except Exception as e:
            self.logger.error(f"盘中期激增检测失败: {e}")
        
        return signals
    
    def process_data_event(self, event_data: Dict[str, Any]) -> List[VolumeSurgeSignal]:
        """
        处理数据事件（连续监控的核心方法）
        
        Args:
            event_data: 事件数据
            
        Returns:
            激增信号列表
        """
        self.stats['data_events_processed'] += 1
        
        event_type = event_data.get('type')
        timestamp = event_data.get('timestamp', datetime.now())
        
        # 根据时间判断处理策略
        current_time = timestamp.time()
        
        if self._is_opening_period(current_time):
            # 开盘期处理
            return self._process_opening_event(event_data)
        elif self._is_intraday_period(current_time):
            # 盘中期处理
            return self._process_intraday_event(event_data)
        else:
            return []
    
    def _process_opening_event(self, event_data: Dict[str, Any]) -> List[VolumeSurgeSignal]:
        """处理开盘期事件"""
        try:
            # 从事件数据中提取股票成交量信息
            stock_data = event_data.get('stock_data', {})
            
            # 获取历史平均数据
            historical_data = {}
            for stock_code in stock_data.keys():
                historical_data[stock_code] = self._get_opening_historical_avg(stock_code)
            
            return self.opening_surge_detection(stock_data, historical_data)
            
        except Exception as e:
            self.logger.error(f"处理开盘期事件失败: {e}")
            return []
    
    def _process_intraday_event(self, event_data: Dict[str, Any]) -> List[VolumeSurgeSignal]:
        """处理盘中期事件"""
        try:
            # 从事件数据中提取K线信息
            kline_data = event_data.get('kline_data', {})
            
            # 获取历史平均数据
            historical_data = {}
            for stock_code in kline_data.keys():
                historical_data[stock_code] = self._get_intraday_historical_avg(stock_code)
            
            return self.intraday_surge_detection(kline_data, historical_data)
            
        except Exception as e:
            self.logger.error(f"处理盘中期事件失败: {e}")
            return []

    def _get_opening_historical_avg(self, stock_code: str) -> float:
        """获取开盘期历史平均成交量"""
        try:
            # 检查缓存
            cache_key = f"opening_avg_{stock_code}_{self.historical_days}"
            cached_value = self.get_from_cache(cache_key)
            if cached_value is not None:
                return cached_value

            # 查询数据库
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=self.historical_days)

            query = """
                SELECT AVG(volume_sum) as avg_volume
                FROM (
                    SELECT
                        DATE(trade_time) as trade_date,
                        SUM(cur_vol) as volume_sum
                    FROM stock_tick_data
                    WHERE stock_code = %s
                      AND DATE(trade_time) BETWEEN %s AND %s
                      AND TIME(trade_time) BETWEEN '09:30:00' AND '09:45:00'
                    GROUP BY DATE(trade_time)
                ) daily_volumes
                WHERE volume_sum > 0
            """

            result = self.db_manager.fetch_all(query, (stock_code, start_date, end_date))

            if result and result[0] and result[0][0]:
                avg_volume = float(result[0][0])
                self.set_cache(cache_key, avg_volume)
                return avg_volume
            else:
                return 0.0

        except Exception as e:
            self.logger.error(f"获取开盘期历史平均成交量失败 {stock_code}: {e}")
            return 0.0

    def _get_intraday_historical_avg(self, stock_code: str) -> float:
        """获取盘中期历史平均成交量"""
        try:
            # 检查缓存
            current_hour = datetime.now().hour
            cache_key = f"intraday_avg_{stock_code}_{current_hour}"
            cached_value = self.get_from_cache(cache_key)
            if cached_value is not None:
                return cached_value

            # 查询当日已完成的5分钟K线平均成交量
            today = datetime.now().date()
            current_time = datetime.now().strftime('%H:%M:%S')

            query = """
                SELECT AVG(volume_sum) as avg_volume
                FROM (
                    SELECT
                        time_bucket('5 minutes', trade_time) as bucket_time,
                        SUM(cur_vol) as volume_sum
                    FROM stock_tick_data
                    WHERE stock_code = %s
                      AND DATE(trade_time) = %s
                      AND TIME(trade_time) BETWEEN '09:45:00' AND %s
                    GROUP BY bucket_time
                    HAVING SUM(cur_vol) > 0
                ) bucket_volumes
            """

            result = self.db_manager.fetch_all(query, (stock_code, today, current_time))

            if result and result[0] and result[0][0]:
                avg_volume = float(result[0][0])
                self.set_cache(cache_key, avg_volume)
                return avg_volume
            else:
                return 0.0

        except Exception as e:
            self.logger.error(f"获取盘中期历史平均成交量失败 {stock_code}: {e}")
            return 0.0

    def _is_opening_period(self, current_time) -> bool:
        """判断是否为开盘期"""
        from datetime import time
        opening_start = time(9, 30, 0)
        opening_end = time(9, 45, 0)
        return opening_start <= current_time < opening_end

    def _is_intraday_period(self, current_time) -> bool:
        """判断是否为盘中期"""
        from datetime import time
        intraday_start = time(9, 45, 0)
        intraday_end = time(15, 0, 0)
        return intraday_start <= current_time < intraday_end

    def _validate_volume_data(self, volume: float, stock_code: str) -> bool:
        """验证成交量数据"""
        if not isinstance(volume, (int, float)):
            return False
        if volume < 0:
            return False
        if volume < self.min_volume_threshold:
            return False
        if volume > 1e12:  # 异常过大的成交量
            return False
        return True

    def _calculate_confidence(self, surge_ratio: float, threshold: float) -> float:
        """计算置信度"""
        if surge_ratio < threshold:
            return 0.0

        base_confidence = min(0.9, 0.5 + 0.1 * np.log10(surge_ratio / threshold))

        if threshold >= 100:  # 开盘期
            confidence_multiplier = 1.0
        else:  # 盘中期
            confidence_multiplier = 0.9

        return min(1.0, base_confidence * confidence_multiplier)

    def get_from_cache(self, cache_key: str) -> Optional[Any]:
        """从缓存获取数据"""
        if not self.enable_cache or cache_key not in self.calculation_cache:
            self.stats['cache_misses'] += 1
            return None

        cache_time = self.cache_timestamps.get(cache_key, 0)
        if datetime.now().timestamp() - cache_time < self.cache_ttl:
            self.stats['cache_hits'] += 1
            return self.calculation_cache[cache_key]
        else:
            del self.calculation_cache[cache_key]
            del self.cache_timestamps[cache_key]
            self.stats['cache_misses'] += 1
            return None

    def set_cache(self, cache_key: str, data: Any):
        """设置缓存"""
        if self.enable_cache:
            self.calculation_cache[cache_key] = data
            self.cache_timestamps[cache_key] = datetime.now().timestamp()

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_calculations = self.stats['opening_calculations'] + self.stats['intraday_calculations']
        cache_hit_rate = 0.0
        if self.stats['cache_hits'] + self.stats['cache_misses'] > 0:
            cache_hit_rate = self.stats['cache_hits'] / (self.stats['cache_hits'] + self.stats['cache_misses'])

        return {
            'total_calculations': total_calculations,
            'opening_calculations': self.stats['opening_calculations'],
            'intraday_calculations': self.stats['intraday_calculations'],
            'signals_generated': self.stats['signals_generated'],
            'data_events_processed': self.stats['data_events_processed'],
            'cache_hit_rate': cache_hit_rate,
            'cache_size': len(self.calculation_cache),
            'opening_threshold': self.opening_threshold,
            'intraday_threshold': self.intraday_threshold
        }
