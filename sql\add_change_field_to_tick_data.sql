-- 为stock_tick_data表添加change字段
-- 用于存储当前价格与上一次tick价格的差值
-- 
-- 作者: QuantFM Team
-- 创建时间: 2025-08-17

-- 添加change字段到stock_tick_data表
ALTER TABLE stock_tick_data 
ADD COLUMN IF NOT EXISTS change REAL DEFAULT 0.0;

-- 添加字段注释
COMMENT ON COLUMN stock_tick_data.change IS '价格变化：当前价格与上一次tick价格的差值';

-- 创建索引以优化基于价格变化的查询
CREATE INDEX IF NOT EXISTS idx_tick_price_change 
    ON stock_tick_data (stock_code, change DESC, trade_time DESC)
    WHERE change != 0;

-- 创建索引以优化价格变化幅度查询
CREATE INDEX IF NOT EXISTS idx_tick_significant_change 
    ON stock_tick_data (stock_code, trade_time DESC)
    WHERE ABS(change) > 0.01;  -- 价格变化超过1分钱的记录

-- 验证字段添加
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'stock_tick_data' 
    AND column_name = 'change';

-- 验证索引创建
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'stock_tick_data'
    AND indexname LIKE '%change%'
ORDER BY indexname;

-- 示例查询：获取价格变化最大的股票
-- SELECT 
--     stock_code,
--     trade_time,
--     price,
--     change,
--     ABS(change) as abs_change
-- FROM stock_tick_data 
-- WHERE DATE(trade_time) = CURRENT_DATE
--     AND change != 0
-- ORDER BY ABS(change) DESC
-- LIMIT 10;

-- 示例查询：获取某只股票的价格变化历史
-- SELECT 
--     trade_time,
--     price,
--     change,
--     LAG(price) OVER (ORDER BY trade_time) as prev_price
-- FROM stock_tick_data 
-- WHERE stock_code = '000001'
--     AND DATE(trade_time) = CURRENT_DATE
-- ORDER BY trade_time;
