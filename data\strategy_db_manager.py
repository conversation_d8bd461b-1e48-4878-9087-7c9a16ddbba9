#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略数据库管理器

专门用于处理策略信号的数据库操作。

功能特点：
1. 策略信号保存
2. 双通道斐波那契信号专用保存
3. 策略配置管理
4. 执行日志记录
5. 性能统计

作者: QuantFM Team
创建时间: 2025-08-08
"""

import sys
import os
from datetime import datetime, date
from typing import List, Dict, Any, Optional, Union
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger
from data.db_manager import get_db_manager


class StrategyDBManager:
    """策略数据库管理器"""
    
    def __init__(self):
        """初始化策略数据库管理器"""
        self.logger = get_logger("StrategyDBManager")
        self.db_manager = get_db_manager()
        
    def save_dual_channel_signal(self, signal) -> bool:
        """
        保存双通道斐波那契信号到专用表
        
        Args:
            signal: EnhancedSignal对象
            
        Returns:
            是否保存成功
        """
        try:
            sql = """
                INSERT INTO dual_channel_fibonacci_signals (
                    signal_time, stock_code, stock_name, strategy_name,
                    break_t1_date, break_t2_date, start_low_date, target_high_date,
                    start_low_price, target_high_price, break_t1_price, break_t2_price,
                    signal_strength, volume_ratio_t1, volume_ratio_t2,
                    breakout_amplitude, stability_score, structure_score,
                    signal_note
                ) VALUES (
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s,
                    %s, %s, %s,
                    %s
                )
                ON CONFLICT (stock_code, break_t2_date::DATE) 
                DO UPDATE SET
                    signal_strength = EXCLUDED.signal_strength,
                    volume_ratio_t2 = EXCLUDED.volume_ratio_t2,
                    signal_note = EXCLUDED.signal_note,
                    updated_at = CURRENT_TIMESTAMP
            """
            
            params = (
                signal.break_t2_date,  # signal_time
                signal.stock_code,
                signal.stock_name,
                signal.strategy_name,
                signal.break_t1_date,
                signal.break_t2_date,
                signal.start_low_date,
                signal.target_high_date,
                signal.start_low_price,
                signal.target_high_price,
                signal.break_t1_price,
                signal.break_t2_price,
                signal.signal_strength,
                signal.volume_ratio_t1,
                signal.volume_ratio_t2,
                signal.breakout_amplitude,
                signal.stability_score,
                signal.structure_score,
                signal.signal_note
            )
            
            self.db_manager.execute_query(sql, params)
            self.logger.info(f"成功保存双通道信号: {signal.stock_code}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存双通道信号失败 {signal.stock_code}: {e}")
            return False
    
    def save_strategy_signal(self, signal, strategy_data: Dict[str, Any] = None) -> bool:
        """
        保存策略信号到通用表
        
        Args:
            signal: 信号对象（支持EnhancedSignal或其他信号类型）
            strategy_data: 策略特定数据
            
        Returns:
            是否保存成功
        """
        try:
            # 提取通用属性
            signal_time = getattr(signal, 'break_t2_date', getattr(signal, 'signal_date', datetime.now()))
            stock_code = getattr(signal, 'stock_code', '')
            stock_name = getattr(signal, 'stock_name', '')
            strategy_name = getattr(signal, 'strategy_name', '')
            signal_strength = getattr(signal, 'signal_strength', 0.0)
            signal_price = getattr(signal, 'break_t2_price', getattr(signal, 'latest_close', 0.0))
            latest_volume = getattr(signal, 'latest_volume', 0)
            avg_volume = getattr(signal, 'avg_volume', 0)
            volume_ratio = getattr(signal, 'volume_ratio', 0.0)
            latest_close = getattr(signal, 'latest_close', 0.0)
            max_high_20d = getattr(signal, 'max_high_20d', 0.0)
            breakout_ratio = getattr(signal, 'breakout_ratio', 0.0)
            signal_note = getattr(signal, 'signal_note', '')
            
            # 构建策略数据
            if strategy_data is None:
                strategy_data = {}
            
            # 如果是EnhancedSignal，添加详细数据
            if hasattr(signal, 'break_t1_date'):
                strategy_data.update({
                    'break_t1_date': signal.break_t1_date.isoformat() if signal.break_t1_date else None,
                    'break_t2_date': signal.break_t2_date.isoformat() if signal.break_t2_date else None,
                    'start_low_date': signal.start_low_date.isoformat() if signal.start_low_date else None,
                    'target_high_date': signal.target_high_date.isoformat() if signal.target_high_date else None,
                    'start_low_price': signal.start_low_price,
                    'target_high_price': signal.target_high_price,
                    'break_t1_price': signal.break_t1_price,
                    'volume_ratio_t1': signal.volume_ratio_t1,
                    'volume_ratio_t2': signal.volume_ratio_t2,
                    'breakout_amplitude': signal.breakout_amplitude,
                    'stability_score': signal.stability_score,
                    'structure_score': signal.structure_score
                })
            
            sql = """
                INSERT INTO strategy_signals (
                    signal_time, stock_code, stock_name, strategy_name,
                    signal_strength, signal_price, latest_volume, avg_volume,
                    volume_ratio, latest_close, max_high_20d, breakout_ratio,
                    strategy_data, signal_note
                ) VALUES (
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s
                )
                ON CONFLICT (stock_code, strategy_name, signal_time::DATE) 
                DO UPDATE SET
                    signal_strength = EXCLUDED.signal_strength,
                    signal_price = EXCLUDED.signal_price,
                    strategy_data = EXCLUDED.strategy_data,
                    signal_note = EXCLUDED.signal_note,
                    updated_at = CURRENT_TIMESTAMP
            """
            
            params = (
                signal_time,
                stock_code,
                stock_name,
                strategy_name,
                signal_strength,
                signal_price,
                latest_volume,
                avg_volume,
                volume_ratio,
                latest_close,
                max_high_20d,
                breakout_ratio,
                json.dumps(strategy_data, ensure_ascii=False),
                signal_note
            )
            
            self.db_manager.execute_query(sql, params)
            self.logger.info(f"成功保存策略信号: {stock_code} - {strategy_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存策略信号失败 {getattr(signal, 'stock_code', 'unknown')}: {e}")
            return False
    
    def log_strategy_execution(self, strategy_name: str, execution_type: str = 'scheduled',
                             stocks_processed: int = 0, signals_generated: int = 0,
                             execution_duration_ms: int = 0, status: str = 'completed',
                             error_message: str = None, execution_details: Dict[str, Any] = None) -> bool:
        """
        记录策略执行日志
        
        Args:
            strategy_name: 策略名称
            execution_type: 执行类型
            stocks_processed: 处理的股票数量
            signals_generated: 生成的信号数量
            execution_duration_ms: 执行时长（毫秒）
            status: 执行状态
            error_message: 错误信息
            execution_details: 执行详情
            
        Returns:
            是否记录成功
        """
        try:
            sql = """
                INSERT INTO strategy_execution_logs (
                    execution_time, strategy_name, execution_type,
                    stocks_processed, signals_generated, execution_duration_ms,
                    status, error_message, execution_details
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """
            
            params = (
                datetime.now(),
                strategy_name,
                execution_type,
                stocks_processed,
                signals_generated,
                execution_duration_ms,
                status,
                error_message,
                json.dumps(execution_details, ensure_ascii=False) if execution_details else None
            )
            
            self.db_manager.execute_query(sql, params)
            self.logger.debug(f"记录策略执行日志: {strategy_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"记录策略执行日志失败: {e}")
            return False
    
    def get_strategy_config(self, strategy_name: str) -> Optional[Dict[str, Any]]:
        """
        获取策略配置
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            策略配置字典
        """
        try:
            sql = """
                SELECT config_data, is_enabled 
                FROM strategy_configs 
                WHERE strategy_name = %s
            """
            
            result = self.db_manager.fetch_one(sql, (strategy_name,))

            if result:
                # 数据库管理器返回字典格式
                if isinstance(result, dict):
                    config_data = result.get('config_data')
                    is_enabled = result.get('is_enabled')
                else:
                    # 兼容元组格式
                    config_data, is_enabled = result

                if is_enabled:
                    if config_data:
                        if isinstance(config_data, str):
                            return json.loads(config_data)
                        elif isinstance(config_data, dict):
                            return config_data
                        else:
                            return config_data
                    else:
                        self.logger.warning(f"策略 {strategy_name} 配置数据为空")
                        return {}
                else:
                    self.logger.warning(f"策略 {strategy_name} 已被禁用")
                    return None
            else:
                self.logger.warning(f"未找到策略配置: {strategy_name}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取策略配置失败: {e}")
            return None
    
    def update_strategy_performance(self, strategy_name: str, stat_date: date = None,
                                  performance_data: Dict[str, Any] = None) -> bool:
        """
        更新策略性能统计
        
        Args:
            strategy_name: 策略名称
            stat_date: 统计日期
            performance_data: 性能数据
            
        Returns:
            是否更新成功
        """
        try:
            if stat_date is None:
                stat_date = date.today()
            
            if performance_data is None:
                performance_data = {}
            
            # 从当日信号计算统计数据
            sql_stats = """
                SELECT 
                    COUNT(*) as total_signals,
                    COUNT(CASE WHEN signal_strength >= 0.8 THEN 1 END) as high_strength_signals,
                    COUNT(CASE WHEN signal_strength >= 0.5 AND signal_strength < 0.8 THEN 1 END) as medium_strength_signals,
                    COUNT(CASE WHEN signal_strength < 0.5 THEN 1 END) as low_strength_signals,
                    AVG(signal_strength) as avg_signal_strength
                FROM strategy_signals 
                WHERE strategy_name = %s 
                  AND DATE(signal_time) = %s
                  AND is_active = TRUE
            """
            
            stats_result = self.db_manager.fetch_one(sql_stats, (strategy_name, stat_date))
            
            if stats_result:
                # 处理字典格式返回
                if isinstance(stats_result, dict):
                    total_signals = stats_result.get('count', 0)
                    high_strength = stats_result.get('count_1', 0)  # 这些键名可能不同
                    medium_strength = stats_result.get('count_2', 0)
                    low_strength = stats_result.get('count_3', 0)
                    avg_strength = stats_result.get('avg', 0)
                else:
                    # 兼容元组格式
                    total_signals, high_strength, medium_strength, low_strength, avg_strength = stats_result
                
                sql_update = """
                    INSERT INTO strategy_performance_stats (
                        strategy_name, stat_date, total_signals,
                        high_strength_signals, medium_strength_signals, low_strength_signals,
                        avg_signal_strength
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s
                    )
                    ON CONFLICT (strategy_name, stat_date) 
                    DO UPDATE SET
                        total_signals = EXCLUDED.total_signals,
                        high_strength_signals = EXCLUDED.high_strength_signals,
                        medium_strength_signals = EXCLUDED.medium_strength_signals,
                        low_strength_signals = EXCLUDED.low_strength_signals,
                        avg_signal_strength = EXCLUDED.avg_signal_strength,
                        updated_at = CURRENT_TIMESTAMP
                """
                
                params = (
                    strategy_name, stat_date, total_signals or 0,
                    high_strength or 0, medium_strength or 0, low_strength or 0,
                    float(avg_strength) if avg_strength else 0.0
                )
                
                self.db_manager.execute_query(sql_update, params)
                self.logger.debug(f"更新策略性能统计: {strategy_name} - {stat_date}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"更新策略性能统计失败: {e}")
            return False
    
    def get_latest_signals(self, strategy_name: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取最新的策略信号
        
        Args:
            strategy_name: 策略名称（可选）
            limit: 返回数量限制
            
        Returns:
            信号列表
        """
        try:
            if strategy_name:
                sql = """
                    SELECT * FROM strategy_signals 
                    WHERE strategy_name = %s AND is_active = TRUE
                    ORDER BY signal_time DESC 
                    LIMIT %s
                """
                params = (strategy_name, limit)
            else:
                sql = """
                    SELECT * FROM strategy_signals 
                    WHERE is_active = TRUE
                    ORDER BY signal_time DESC 
                    LIMIT %s
                """
                params = (limit,)
            
            results = self.db_manager.fetch_all(sql, params)

            if not results:
                return []

            # 获取列名
            column_names = [
                'id', 'signal_time', 'stock_code', 'stock_name', 'strategy_name',
                'signal_type', 'signal_strength', 'signal_price', 'latest_volume',
                'avg_volume', 'volume_ratio', 'latest_close', 'max_high_20d',
                'breakout_ratio', 'strategy_data', 'signal_note', 'is_active',
                'is_processed', 'created_at', 'updated_at'
            ]

            signals = []
            for row in results:
                # 确保行数据长度与列名匹配
                row_data = list(row)
                if len(row_data) < len(column_names):
                    row_data.extend([None] * (len(column_names) - len(row_data)))

                signal_dict = dict(zip(column_names[:len(row_data)], row_data))

                # 解析JSON字段
                if signal_dict.get('strategy_data'):
                    try:
                        signal_dict['strategy_data'] = json.loads(signal_dict['strategy_data'])
                    except (json.JSONDecodeError, TypeError):
                        signal_dict['strategy_data'] = {}

                signals.append(signal_dict)

            return signals
            
        except Exception as e:
            self.logger.error(f"获取最新信号失败: {e}")
            return []


# 全局实例
_strategy_db_manager = None

def get_strategy_db_manager() -> StrategyDBManager:
    """获取策略数据库管理器单例"""
    global _strategy_db_manager
    if _strategy_db_manager is None:
        _strategy_db_manager = StrategyDBManager()
    return _strategy_db_manager
